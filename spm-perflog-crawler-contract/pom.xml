<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.3.0</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <groupId>com.siemens.spm</groupId>
    <artifactId>spm-perflog-crawler-contract</artifactId>
    <version>3.2.0</version>
    <name>spm-perflog-crawler-contract</name>
    <description>spm-perflog-crawler-contract</description>

    <properties>
        <java.version>17</java.version>

        <spm-common.version>3.2.0</spm-common.version>
        <spm-perflog-lib.version>3.2.0</spm-perflog-lib.version>
    </properties>

    <dependencies>
        <!-- Internal dependencies -->
        <dependency>
            <groupId>com.siemens.spm</groupId>
            <artifactId>spm-common</artifactId>
            <version>${spm-common.version}</version>
        </dependency>
        <dependency>
            <groupId>com.siemens.spm</groupId>
            <artifactId>spm-perflog-lib</artifactId>
            <version>${spm-perflog-lib.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>maven-repo-product</id>
            <name>maven-repo-product</name>
            <url>${env.MAVEN_REPO_PRODUCT_URL}</url>
        </repository>
        <!-- snapshotRepository>
            <id>maven-repo</id>
            <name>maven-repo</name>
            <url>${env.MAVEN_REPO_URL}</url>
        </snapshotRepository> -->
    </distributionManagement>
    
</project>
