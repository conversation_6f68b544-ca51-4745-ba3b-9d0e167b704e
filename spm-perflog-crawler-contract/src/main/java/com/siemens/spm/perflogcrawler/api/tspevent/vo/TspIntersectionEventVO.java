package com.siemens.spm.perflogcrawler.api.tspevent.vo;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TspIntersectionEventVO implements Serializable {

    private static final long serialVersionUID = 3236867157255864723L;

    @JsonProperty("id")
    private String id;

    @JsonProperty("event_type")
    private String eventType;

    @JsonProperty("vehicle_id")
    private Integer vehicleId;

    @JsonProperty("request_id")
    private String requestId;

    @JsonProperty("priority")
    private Integer priority;

    // TODO: May be update this field to enum to hold description value

    @JsonProperty("intersection_asset_id")
    private int intersectionAssetId;

    @JsonProperty("network_id")
    private String networkId;

    @JsonProperty("vehicle_location")
    private VehicleLocation vehicleLocation;

    @JsonProperty("intersection_uuid")
    private String intersectionUUID;

    @JsonProperty("intersection_name")
    private String intersectionName;

    @JsonProperty("date_time_stamp")
    private Long dateTimeStamp;

    @JsonProperty("est_arrival_time")
    private Integer estArrivalTime;

    @JsonProperty("passenger_loading")
    private Integer passengerLoading;

    @JsonProperty("time_behind_ahead")
    private Integer timeBehindAhead;

    @JsonProperty("route_number")
    private Integer routeNumber;

    @JsonProperty("block_number")
    private Integer blockNumber;

    @JsonProperty("trip_direction")
    private String tripDirection;

    @JsonProperty("direction_at_next_intersection")
    private String nextIntersectionDirection;

}

