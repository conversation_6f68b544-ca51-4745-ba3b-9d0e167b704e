package com.siemens.spm.perflogcrawler.api.controller;

import jakarta.validation.constraints.NotNull;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;

import com.siemens.spm.common.api.PublicController;
import com.siemens.spm.perflogcrawler.api.vo.response.TaskProgressCancelResultObject;
import com.siemens.spm.perflogcrawler.api.vo.response.TaskProgressDeleteResultObject;
import com.siemens.spm.perflogcrawler.api.vo.response.TaskProgressResultObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

@RequestMapping(TaskProgressControllerV2.API_ROOT)
@Tag(name = "task-progress-v2")
public interface TaskProgressControllerV2 extends PublicController {
    String API_ROOT = PUBLIC_API + "/v2";
    String TASK_PROGRESS = "/task-progresses";
    String TASK_PROGRESS_CANCEL = TASK_PROGRESS + "/{id}/cancel";

    /**
     * Delete a task progress (status = COMPLETED, ERROR or CANCELLED) via task id
     *
     * @param id
     * @return
     */
    @Operation(summary = "Delete a task progress")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "400", description = "Bad Request")
    })
    @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true,
            in = ParameterIn.HEADER, description = "Bearer Token type")
    @ResponseStatus(HttpStatus.OK)
    @DeleteMapping(TASK_PROGRESS + "/{id}")
    ResponseEntity<TaskProgressDeleteResultObject> deleteTaskProgress(@PathVariable @NotNull Long id);

    /**
     * Cancel a task progress (status = QUEUED or PROCESSING) via task id
     *
     * @param id
     * @return
     */
    @Operation(summary = "Cancel a task progress")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "400", description = "Bad Request")
    })
    @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true,
            in = ParameterIn.HEADER, description = "Bearer Token type")
    @ResponseStatus(HttpStatus.OK)
    @PutMapping(TASK_PROGRESS_CANCEL)
    ResponseEntity<TaskProgressCancelResultObject> cancelTaskProgress(@PathVariable @NotNull Long id);

    /**
     * Get all active task progress
     *
     * @return
     */
    @Operation(summary = "Get active task progresses")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "400", description = "Bad Request")
    })
    @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true,
            in = ParameterIn.HEADER, description = "Bearer Token type")
    @ResponseStatus(HttpStatus.OK)
    @GetMapping(TASK_PROGRESS)
    ResponseEntity<TaskProgressResultObject> getActiveTaskProgresses();

}
