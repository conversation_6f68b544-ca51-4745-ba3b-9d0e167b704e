package com.siemens.spm.perflogcrawler.api.intercom;

import java.time.LocalDateTime;

import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.siemens.spm.common.intercom.InterComController;
import com.siemens.spm.common.shared.vo.SimpleResultObject;

/**
 * <AUTHOR>
 */
@RequestMapping(TrafficFlowController.API_ROOT)
public interface TrafficFlowController extends InterComController {

    String API_ROOT = INTERNAL_API + "/v1/traffic-flow";

    @PostMapping
    ResponseEntity<SimpleResultObject> aggregateTrafficFlowData(
            @RequestParam(value = "agencyId") Integer agencyId,
            @RequestParam(value = "int_uuid", required = false) String intUUID,
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) @RequestParam(value = "from_time")
                    LocalDateTime fromTime,
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) @RequestParam(value = "to_time")
                    LocalDateTime toTime);

}
