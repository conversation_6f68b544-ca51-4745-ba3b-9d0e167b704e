package com.siemens.spm.perflogcrawler.api.vo.request;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CleanCacheRequest implements Serializable {

    /**
     * 
     */
    private static final long serialVersionUID = -6547618855565366967L;

    @JsonProperty("tsp_event_live_time")
    private Long tspEventLiveTime;
}
