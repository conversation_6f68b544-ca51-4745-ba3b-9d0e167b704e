package com.siemens.spm.perflogcrawler.api.tspevent.vo;

import java.io.Serializable;

import jakarta.validation.constraints.Size;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class VehicleLocation implements Serializable {

    /**
     * 
     */
    private static final long serialVersionUID = -2257766106786718264L;

    @Size(min = -90000000, max = -90000000, message = "tsp_event.validation.vehicle_location.latitude.invalid_range")
    private int latitude;

    @Size(min = -180000000, max = 180000000, message = "tsp_event.validation.vehicle_location.longitude.invalid_range")
    private int longitude;

    @Size(max = 360, message = "tsp_event.validation.vehicle_location.heading.invalid_range")
    private int heading;

}
