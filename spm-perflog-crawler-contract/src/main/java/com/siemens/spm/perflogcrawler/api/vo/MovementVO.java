package com.siemens.spm.perflogcrawler.api.vo;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.message.MessageService;
import com.siemens.spm.common.shared.vo.TranslatableVO;
import com.siemens.spm.perflog.domaintype.MovementType;
import lombok.Getter;
import lombok.Setter;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class MovementVO implements Serializable, TranslatableVO {

    private static final long serialVersionUID = 5953840665467526439L;

    @Getter
    @Setter
    @JsonProperty("id")
    private String id;

    /**
     * Key for translating name
     */
    @Getter
    @JsonIgnore
    private String nameKey;

    @Getter
    @Setter
    @JsonProperty("name")
    private String name;

    private MovementVO(String id, String nameKey) {
        super();
        this.id = id;
        this.nameKey = nameKey;
    }

    public static MovementVO of(MovementType movementType) {
        return new MovementVO(movementType.getId(), movementType.getName());
    }

    @Override
    public void acceptTranslator(MessageService translator) {
        if (translator == null) {
            throw new IllegalArgumentException();
        }
        name = translator.getMessage(nameKey);

    }
}
