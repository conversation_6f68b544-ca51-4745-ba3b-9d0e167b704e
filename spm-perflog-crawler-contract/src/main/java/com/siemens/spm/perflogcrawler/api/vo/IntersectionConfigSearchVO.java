package com.siemens.spm.perflogcrawler.api.vo;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class IntersectionConfigSearchVO implements Serializable {

    private static final long serialVersionUID = 4183357594508625777L;

    @JsonProperty("uuid")
    private String uuid;

    @JsonProperty("from_time_utc")
    private Long fromTimeUtc;

    @JsonProperty("to_time_utc")
    private Long toTimeUtc;

    @JsonProperty("valid_from_time")
    private LocalDateTime validFromTime;

    @JsonProperty("valid_to_time")
    private LocalDateTime validToTime;

    @JsonProperty("latest")
    private Boolean latest;

}
