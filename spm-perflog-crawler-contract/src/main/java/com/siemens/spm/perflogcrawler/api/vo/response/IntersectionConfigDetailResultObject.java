package com.siemens.spm.perflogcrawler.api.vo.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.siemens.spm.perflogcrawler.api.vo.IntersectionConfigDetailVO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.http.HttpStatus;

import com.siemens.spm.common.shared.vo.AbstractResultObject;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class IntersectionConfigDetailResultObject
        extends AbstractResultObject<IntersectionConfigDetailVO, IntersectionConfigDetailResultObject.StatusCode> {

    private IntersectionConfigDetailVO data;

    private StatusCode statusCode;

    public IntersectionConfigDetailResultObject() {
        super();
    }

    public IntersectionConfigDetailResultObject(StatusCode statusCode) {
        super(null, statusCode);
    }

    public IntersectionConfigDetailResultObject(IntersectionConfigDetailVO intersectionConfigDetailVO) {
        super(intersectionConfigDetailVO);
    }

    @Override
    public IntersectionConfigDetailVO getData() {
        return data;
    }

    @Override
    protected void setStatusCode(StatusCode value) {
        this.statusCode = value;
    }

    @Override
    public String getMessage() {
        return statusCode.getMessage();
    }

    @Override
    protected StatusCode getErrorStatusValue() {
        return StatusCode.ERROR;
    }

    @Override
    protected void setData(IntersectionConfigDetailVO intersectionConfigDetailVO) {
        this.data = intersectionConfigDetailVO;
    }

    @Override
    public StatusCode getSuccessfulStatusValue() {
        return StatusCode.SUCCESS;
    }

    @Getter
    @AllArgsConstructor
    public enum StatusCode {
        /**
         * OK
         */
        CREATED("created", HttpStatus.CREATED),
        SUCCESS("success", HttpStatus.OK),
        NO_CONTENT("no_content", HttpStatus.NO_CONTENT),

        /**
         * Bad Request
         */
        CONFIG_NOT_FOUND("int_config_not_found", HttpStatus.BAD_REQUEST),
        INTERSECTION_NOT_BELONG_AGENCY("intersection_not_belong_agency", HttpStatus.BAD_REQUEST),

        /**
         * Internal Server Error
         */
        ERROR("unknown_exception", HttpStatus.INTERNAL_SERVER_ERROR);

        private final String errorField;
        private final String message;
        private final HttpStatus httpStatus;

        StatusCode(String message, HttpStatus httpStatus) {
            this(null, message, httpStatus);
        }
    }

    @Override
    public StatusCode getStatusCode() {
        return statusCode;
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }

    @Override
    public String getErrorFieldName() {
        return statusCode.getErrorField();
    }

}
