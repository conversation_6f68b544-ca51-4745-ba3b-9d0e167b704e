package com.siemens.spm.perflogcrawler.api.vo.response;

import org.springframework.http.HttpStatus;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.siemens.spm.common.shared.vo.AbstractResultObject;
import com.siemens.spm.perflogcrawler.api.vo.IntTopologyInternalMapVO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@RequiredArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class IntTopologyInternalSearchResultObject
        extends AbstractResultObject<IntTopologyInternalMapVO, IntTopologyInternalSearchResultObject.StatusCode> {

    private static final long serialVersionUID = -4324038411106743214L;

    private IntTopologyInternalMapVO data;

    @NonNull
    private StatusCode statusCode;

    public IntTopologyInternalSearchResultObject(IntTopologyInternalMapVO data) {
        this(data, StatusCode.SUCCESS);
    }

    @Override
    public String getMessage() {
        return statusCode.getMessage();
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }

    @Override
    public String getErrorFieldName() {
        return statusCode.getErrorField();
    }

    @Override
    protected StatusCode getErrorStatusValue() {
        return StatusCode.ERROR;
    }

    @Override
    public StatusCode getSuccessfulStatusValue() {
        return StatusCode.SUCCESS;
    }

    @Getter
    @AllArgsConstructor
    public enum StatusCode {
        /** OK */
        SUCCESS("success", HttpStatus.OK),

        /** Internal Server Error */
        ERROR("unknown_exception", HttpStatus.INTERNAL_SERVER_ERROR);

        private final String errorField;
        private final String message;
        private final HttpStatus httpStatus;

        StatusCode(String message, HttpStatus httpStatus) {
            this(null, message, httpStatus);
        }
    }

}
