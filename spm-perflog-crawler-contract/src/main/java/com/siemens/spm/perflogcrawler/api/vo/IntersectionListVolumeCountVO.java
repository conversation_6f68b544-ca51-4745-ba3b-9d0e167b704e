/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : IntersectionListVolumeCountVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.perflogcrawler.api.vo;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class IntersectionListVolumeCountVO implements Serializable {

    private static final long serialVersionUID = 5629543456002395811L;

    @JsonProperty("intersections")
    private List<IntersectionVolumeCountVO> intersectionVolumeCountVOS;

}
