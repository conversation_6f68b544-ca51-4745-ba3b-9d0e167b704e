package com.siemens.spm.perflogcrawler.api.security;

import java.util.List;

import com.siemens.spm.common.constant.CommonConstants;
import com.siemens.spm.common.shared.domaintype.ResourceScope;

public interface TaskProgressScopeService {
    
    /**
     * Default bean name
     */
    public static final String DEFAULT_BEAN = "taskProgressScopeService";

    /**
     * Expression to check if permission scope is enough to access task progress
     */
    public static final String AND_CHECK_SCOPE_OWNER = CommonConstants.AND + DEFAULT_BEAN + ".checkScopeOwner";
   
    /**
     * Expression to check if permission scope is enough to access task progress
     */
    public static final String AND_CHECK_SCOPE_AGENCY = CommonConstants.AND + DEFAULT_BEAN + ".checkScopeAgency";
    
    /**
     * Current authenticated user is allowed to access given task progress if:
     * <p><ul>
     * <li>Current permission scope is super-scope of {@link ResourceScope#OWNER} or
     * <li>Current permission scope is {@link ResourceScope#OWNER} and current user id matches with task progress owner id
     * </ul></p>
     * 
     * @param taskId task id
     * @return true if current user can access given task progress. Otherwise false.
     */
    boolean checkScopeOwner(Long taskId);
    
    /**
     * Check if current authenticated user is allowed to access all given task progresses.
     * <br>
     * See {@link #checkScopeOwner(Long)} for more detail.
     * 
     * @param taskIdList list of task progress ids
     * @return true if current user can access all given task progresses. Otherwise false.
     */
    boolean checkScopeOwner(List<Long> taskIdList);
    
    /**
     * Current authenticated user is allowed to access given task progress if:
     * <p><ul>
     * <li>The given task progress belongs to current user agency or
     * <li>The current permission scope must be super-scope of {@link ResourceScope#AGENCY}
     * 
     * @param taskId
     * @return true if current user can access given task progress. Otherwise false.
     */
    boolean checkScopeAgency(Long taskId);
 
    /**
     * Check if current authenticated user is allowed to access all given task progresses.
     * <br>
     * See {@link #checkScopeAgency(Long)} for more detail.
     * @param taskIdList
     * @return true if current user can access all given task progress. Otherwise false.
     */
    boolean checkScopeAgency(List<Long> taskIdList);
    
}
