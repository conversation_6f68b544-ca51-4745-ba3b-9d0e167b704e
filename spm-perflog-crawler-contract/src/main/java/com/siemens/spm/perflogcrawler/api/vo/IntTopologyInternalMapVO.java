package com.siemens.spm.perflogcrawler.api.vo;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.perflog.vo.ApproachVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class IntTopologyInternalMapVO implements Serializable {

    private static final long serialVersionUID = -2387384796903746666L;

    /**
     * For each entry in map:
     * <li>Key: intersection_id</li>
     * <li>Value: List of approach</li>
     */
    @JsonProperty("approach_map")
    private Map<String, List<ApproachVO>> approachMap;

}
