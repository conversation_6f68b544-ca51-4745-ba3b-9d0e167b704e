package com.siemens.spm.perflogcrawler.api.vo;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class IntersectionConfigDetailVO implements Serializable {

    private static final long serialVersionUID = -6830541225996130090L;

    @JsonProperty("configuration")
    private IntersectionConfigVO intersectionConfigVO;

    @JsonProperty("latest")
    private Boolean latest;

    @JsonProperty("from_time_utc")
    private Long fromTimeUtc;

}
