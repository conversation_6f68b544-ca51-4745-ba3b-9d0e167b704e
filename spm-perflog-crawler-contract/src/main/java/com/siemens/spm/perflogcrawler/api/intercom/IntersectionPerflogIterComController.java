package com.siemens.spm.perflogcrawler.api.intercom;

import com.siemens.spm.common.intercom.InterComController;
import com.siemens.spm.common.util.RestUtils;
import com.siemens.spm.perflogcrawler.api.vo.request.IntersectionPerflogSearchRequest;
import com.siemens.spm.perflogcrawler.api.vo.response.IntersectionPerflogInternalVOResultObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

@RequestMapping(IntersectionPerflogIterComController.API_ROOT)
public interface IntersectionPerflogIterComController extends InterComController {

    String API_ROOT = INTERNAL_API + "/v1";
    String INTERSECTION_PERFLOG_RESOURCES = "/intersections-perflog";

    /**
     * Search intersectionPerflog internal
     *
     * @param searchRequest {@link IntersectionPerflogSearchRequest}
     * @return {@code ResponseEntity<IntersectionPerflogInternalVOResultObject>}
     */
    @PostMapping(INTERSECTION_PERFLOG_RESOURCES)
    ResponseEntity<IntersectionPerflogInternalVOResultObject> searchIntersectionPerflogInternal(
            @RequestBody IntersectionPerflogSearchRequest searchRequest);

    static ResponseEntity<IntersectionPerflogInternalVOResultObject> invokeSearchIntersectionPerflogInternal(
            String endpoint, IntersectionPerflogSearchRequest searchRequest) {
        String url = endpoint + API_ROOT + INTERSECTION_PERFLOG_RESOURCES;
        return RestUtils.postWithAgencyHeader(url, String.valueOf(searchRequest.getAgencyId()), searchRequest, IntersectionPerflogInternalVOResultObject.class);
    }

}
