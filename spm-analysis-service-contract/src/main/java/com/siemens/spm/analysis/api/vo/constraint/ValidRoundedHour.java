package com.siemens.spm.analysis.api.vo.constraint;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

@Constraint(validatedBy = {})
@Retention(RetentionPolicy.RUNTIME)
@Target({ ElementType.FIELD })
public @interface ValidRoundedHour {

    String message() default "Hour is not rounded";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}
