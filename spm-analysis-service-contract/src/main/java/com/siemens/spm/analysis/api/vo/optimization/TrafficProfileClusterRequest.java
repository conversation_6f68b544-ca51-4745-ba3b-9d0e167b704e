package com.siemens.spm.analysis.api.vo.optimization;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TrafficProfileClusterRequest {

    @JsonProperty("bin_size")
    private Integer binSize;

    @JsonProperty("min_cluster_duration")
    private Integer minClusterDuration;

    @JsonProperty("num_of_clusters")
    private Integer numOfClusters;

    @JsonProperty("type")
    private String type;

    @JsonProperty("data")
    private List<TrafficProfileDataVO> data;

}
