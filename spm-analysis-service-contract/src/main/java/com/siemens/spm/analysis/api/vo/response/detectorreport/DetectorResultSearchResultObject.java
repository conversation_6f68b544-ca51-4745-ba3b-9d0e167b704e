package com.siemens.spm.analysis.api.vo.response.detectorreport;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import org.springframework.http.HttpStatus;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.analysis.api.vo.detectorreport.DetectorResultVO;
import com.siemens.spm.common.shared.vo.AbstractResultObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DetectorResultSearchResultObject extends AbstractResultObject<DetectorResultSearchResultObject.ResponseData, DetectorResultSearchResultObject.StatusCode> {

    private static final long serialVersionUID = 4185890070605136255L;

    private ResponseData data;

    private StatusCode statusCode;

    @Override
    public ResponseData getData() {
        return data;
    }

    @Override
    public StatusCode getStatusCode() {
        return statusCode;
    }

    @Override
    public String getMessage() {
        return statusCode.getMessage();
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }

    @Override
    public String getErrorFieldName() {
        return statusCode.getErrorField();
    }

    @Override
    protected void setData(ResponseData responseData) {
        data = responseData;
    }

    @Override
    protected void setStatusCode(StatusCode value) {
        statusCode = value;
    }

    @Override
    protected StatusCode getErrorStatusValue() {
        return StatusCode.ERROR;
    }

    @Override
    public StatusCode getSuccessfulStatusValue() {
        return StatusCode.SUCCESS;
    }

    @Getter
    @AllArgsConstructor
    public enum StatusCode {
        SUCCESS("success", HttpStatus.OK),

        ERROR("unknown_exception", HttpStatus.INTERNAL_SERVER_ERROR),
        NOT_FOUND("no_data", HttpStatus.NOT_FOUND),

        INVALID_SORT_COLUMN("sort", "sort_column_invalid", HttpStatus.BAD_REQUEST),
        INVALID_SORT_ORDER("sort", "sort_order_invalid", HttpStatus.BAD_REQUEST);

        private final String errorField;
        private final String message;
        private final HttpStatus httpStatus;

        StatusCode(String message, HttpStatus httpStatus) {
            this(null, message, httpStatus);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ResponseData implements Serializable {

        private static final long serialVersionUID = -6369509576315928129L;

        @JsonProperty("total_count")
        private Long totalCount;

        @JsonProperty("results")
        private List<DetectorResultVO> results;

        public static DetectorResultSearchResultObject.ResponseData empty() {
            return DetectorResultSearchResultObject.ResponseData.builder()
                    .results(new ArrayList<>())
                    .totalCount(0L)
                    .build();
        }
    }

    public static DetectorResultSearchResultObject success(DetectorResultSearchResultObject.ResponseData responseData) {
        return new DetectorResultSearchResultObject(responseData, DetectorResultSearchResultObject.StatusCode.SUCCESS);
    }

    public static DetectorResultSearchResultObject error(DetectorResultSearchResultObject.StatusCode statusCode) {
        if (statusCode == null) {
            throw new IllegalArgumentException();
        }

        return new DetectorResultSearchResultObject(null, statusCode);
    }
}
