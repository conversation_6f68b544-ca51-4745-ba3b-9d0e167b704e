package com.siemens.spm.analysis.api.vo.request.summaryreport;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.analysis.api.vo.summaryreport.StatsTypeVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SummaryStatisticRequestVO {

    @JsonProperty("from_time")
    private Timestamp fromTime;

    @JsonProperty("to_time")
    private Timestamp toTime;

    @JsonProperty("stats_types")
    private List<StatsTypeVO> statsTypeVOList;

    @JsonProperty("int_ids")
    private List<String> intIds;

    @JsonProperty("agencyId")
    private Integer agencyId;
}
