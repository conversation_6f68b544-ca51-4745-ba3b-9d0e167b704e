package com.siemens.spm.analysis.api.vo.response.detectorreport;

import org.springframework.http.HttpStatus;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.siemens.spm.common.shared.vo.AbstractSimpleResultObject;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DetectorResultManipulateResultObject extends AbstractSimpleResultObject<DetectorResultManipulateResultObject.StatusCode> {

    private StatusCode statusCode;

    @Override
    public StatusCode getStatusCode() {
        return statusCode;
    }

    @Override
    public String getMessage() {
        return statusCode.getMessage();
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }

    @Override
    public String getErrorFieldName() {
        return statusCode.getErrorField();
    }

    @Override
    protected void setStatusCode(StatusCode value) {
        statusCode = value;
    }

    @Override
    protected StatusCode getErrorStatusValue() {
        return StatusCode.ERROR;
    }

    @Override
    public StatusCode getSuccessfulStatusValue() {
        return StatusCode.SUCCESS;
    }

    @Getter
    @AllArgsConstructor
    public enum StatusCode {
        SUCCESS("success", HttpStatus.OK),
        NOT_FOUND("not_found", HttpStatus.NOT_FOUND),
        ERROR("unknown_exception", HttpStatus.INTERNAL_SERVER_ERROR);

        private final String errorField;
        private final String message;
        private final HttpStatus httpStatus;

        StatusCode(String message, HttpStatus httpStatus) {
            this(null, message, httpStatus);
        }
    }

    public static DetectorResultManipulateResultObject success() {
        return new DetectorResultManipulateResultObject(DetectorResultManipulateResultObject.StatusCode.SUCCESS);
    }

    public static DetectorResultManipulateResultObject error() {
        return new DetectorResultManipulateResultObject(DetectorResultManipulateResultObject.StatusCode.ERROR);
    }
}
