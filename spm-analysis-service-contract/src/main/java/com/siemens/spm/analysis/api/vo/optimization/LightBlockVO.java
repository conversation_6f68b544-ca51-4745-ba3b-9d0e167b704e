package com.siemens.spm.analysis.api.vo.optimization;

import java.io.Serializable;
import java.time.LocalTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LightBlockVO implements Serializable {

    private static final long serialVersionUID = 566252095073900575L;

    @JsonProperty("phase")
    private int phase;

    @JsonProperty("color")
    private String color;

    @JsonProperty("start_time")
    private LocalTime startTime;

    @JsonProperty("end_time")
    private LocalTime endTime;

    @JsonProperty("det_hit")
    private Long detHit;

    @JsonProperty("forward_green_wave_lines")
    private List<LocalTime> forwardGreenWaveLines;

    @JsonProperty("forward_green_wave_percent")
    private Double forwardGreenWavePercent;

    @JsonProperty("backward_green_wave_percent")
    private Double backwardGreenWavePercent;

    @JsonProperty("backward_green_wave_lines")
    private List<LocalTime> backwardGreenWaveLines;

    @JsonProperty("forward_green_percent")
    private Double forwardGreenPercent;

    @JsonProperty("backward_green_percent")
    private Double backwardGreenPercent;

    /**
     * This field should only available when color is green. Actually, it's not represent for a light block, it
     * represents for a cycle of coordinated phase
     */
    @JsonProperty("aog_percent")
    private Double aogPercent;

    /**
     * {@code true} if this light block have max aog percent in all block in ring. Otherwise, this field should be
     * {@code null}.
     * <p>
     * NOTE: This field should only available with the color is green and phase in coordinated phase
     */
    @JsonProperty("max_aog")
    private Boolean maxAog;

    /**
     * {@code true} if this light block have min aog percent in all block in ring. Otherwise, this field should be
     * {@code null}.
     * <p>
     * NOTE: This field should only available with the color is green and phase in coordinated phase
     */
    @JsonProperty("min_aog")
    private Boolean minAog;

    @JsonIgnore
    public long getDetHitLongValue() {
        return detHit == null ? 0 : detHit;
    }

}
