package com.siemens.spm.analysis.api.vo.optimization;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PhaseOptimizationResultVO extends PhaseOptimizationPredictVO {

    private static final long serialVersionUID = 6852849234335893245L;

    // Is phase coordinated phase?
    @JsonProperty("is_coord_phase")
    private boolean isCoordPhase;

    @JsonProperty("is_selected_phase")
    private boolean isSelectedPhase;

    @JsonProperty("existing_aog")
    private int existingAog;

    @JsonProperty("predicted_aog_percent")
    private double predictedAogPercent;

    @JsonProperty("existing_aog_percent")
    private double existingAogPercent;

    @JsonProperty("existing_volume")
    private Integer existingVolume;

    @JsonProperty("predicted_volume")
    private Integer predictedVolume;

    @JsonProperty("bias")
    public double getBias() {
        return predictedAogPercent - existingAogPercent;
    }

}
