package com.siemens.spm.analysis.api.vo.optimization;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OptimizedOffsetResultVO implements Serializable {

    /**
     * 
     */
    private static final long serialVersionUID = 1L;

    /**
     * The consider offset
     */
    @JsonProperty("offset")
    private int offset;

    /**
     * Sum of predicted upstream AoG
     */
    @JsonProperty("upstream_aog")
    private long upstreamAog;

    @JsonProperty("upstream_volume")
    private long upstreamVolumn;

    /**
     * Sum of predicted downstream AoG
     */
    @JsonProperty("downstream_aog")
    private long downstreamAog;

    @JsonProperty("downstream_volume")
    private long downstreamVolumn;

    /**
     * Sum of predicted AoG
     */
    @JsonProperty("total_aog")
    private long totalAog;

    @JsonProperty("total_volume")
    private long totalVolumn;

    /**
     * Weight sum (is calculated by a specific expression)
     */
    @JsonProperty("weighted_sum")
    private long weightedSum;

}
