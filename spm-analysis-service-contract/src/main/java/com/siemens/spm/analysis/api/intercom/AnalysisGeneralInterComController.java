package com.siemens.spm.analysis.api.intercom;

import com.siemens.spm.analysis.api.vo.request.AgencyIdListRequestVO;
import com.siemens.spm.analysis.api.vo.request.IntersectionIdListRequestVO;
import com.siemens.spm.common.intercom.InterComController;
import com.siemens.spm.common.shared.vo.SimpleResultObject;
import com.siemens.spm.common.util.RestUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

@RequestMapping(AnalysisGeneralInterComController.API_ROOT)
public interface AnalysisGeneralInterComController extends InterComController {

    String VERSION = "/v1";
    String API_ROOT = INTERNAL_API + VERSION + "/analysis-service";

    String DELETE_AGENCY_RESOURCE = "/agency/delete";

    String DELETE_INTERSECTION_RESOURCE = "/intersection/delete";

    @PostMapping(DELETE_AGENCY_RESOURCE)
    ResponseEntity<SimpleResultObject> deleteByAgency(@RequestBody AgencyIdListRequestVO request);

    static ResponseEntity<SimpleResultObject> invokeDeleteByAgency(String serverEndpoint,
                                                                   AgencyIdListRequestVO requestVO) {
        String endpoint = serverEndpoint + API_ROOT + DELETE_AGENCY_RESOURCE;

        return RestUtils.post(endpoint, requestVO, SimpleResultObject.class);
    }

    @PostMapping(DELETE_INTERSECTION_RESOURCE)
    ResponseEntity<SimpleResultObject> deleteByIntersection(@RequestBody IntersectionIdListRequestVO request);

    static ResponseEntity<SimpleResultObject> invokeDeleteByIntersection(String serverEndpoint,
                                                                         Integer agencyId,
                                                                         IntersectionIdListRequestVO requestVO) {
        String endpoint = serverEndpoint + API_ROOT + DELETE_INTERSECTION_RESOURCE;
        return RestUtils.postWithAgencyHeader(endpoint, String.valueOf(agencyId), requestVO,
                SimpleResultObject.class);
    }
}
