package com.siemens.spm.analysis.api.vo.performancemetric;

import java.io.Serializable;
import java.time.DayOfWeek;
import java.util.Set;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.analysis.api.vo.DateRange;
import com.siemens.spm.analysis.api.vo.constraint.ValidDateRange;
import com.siemens.spm.common.constant.TimeConstants;
import com.siemens.spm.common.shared.domaintype.analysis.AnalysisType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PMTemplateSimpleDataVO implements Serializable {

    private static final long serialVersionUID = 4395924084256145686L;

    @JsonProperty("description")
    protected String description;

    @JsonProperty("agency_id")
    protected Integer agencyId;

    @NotNull(message = "time_zone_invalid")
    @Pattern(regexp = TimeConstants.REGEXP_FOR_ZONE_ID, message = "time_zone_invalid")
    @JsonProperty("time_zone")
    protected String timeZone;

    @NotNull
    @JsonProperty("metric_type")
    protected String metricId;

    @ValidDateRange(message = "templates.date_range.invalid")
    @JsonProperty("date_range")
    protected DateRange dateRange;

    @NotNull(message = "templates.week_days_not_null")
    @JsonProperty("week_days")
    protected transient Set<DayOfWeek> weekDays;

    @Valid
    @NotNull(message = "performance_metric.templates.invalid_metadata")
    @JsonProperty("metadata")
    protected PMMetadataVO metaDataVO;

    @JsonIgnore
    public boolean isValid() {
        if (metricId == null) {
            return false;
        }
        AnalysisType metricType = AnalysisType.getById(metricId);
        if (metricType == null) {
            return false;
        }

        return metaDataVO.isValid(metricType);
    }

}
