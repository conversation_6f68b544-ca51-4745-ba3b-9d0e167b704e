/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AnalysisResultObject.java
 * Project     : SPM Platform
 */

package com.siemens.spm.analysis.api.vo.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.siemens.spm.common.shared.vo.AbstractResultObject;
import lombok.AllArgsConstructor;
import lombok.Getter;

import org.springframework.http.HttpStatus;

import java.io.Serializable;

@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties({ "statusCode", "message", "httpStatus" })
public class AnalysisResultNonChartObject<T_CHART extends Serializable> extends AbstractResultObject<T_CHART, AnalysisResultNonChartObject.StatusCode> {

    private static final long serialVersionUID = 5713718549010544650L;

    private StatusCode statusCode;

    private T_CHART data;

    /**
     * Create result object with SUCCESS status and data
     *
     * @param analysisVO
     */
    public AnalysisResultNonChartObject(T_CHART analysisVO) {
        this.data = analysisVO;
        this.statusCode = getSuccessfulStatusValue();
    }

    /**
     * Create result object with null data and an error code
     *
     * @param statusCode Indicating error
     */
    public AnalysisResultNonChartObject(StatusCode statusCode) {
        this.statusCode = statusCode;
    }

    @Override
    protected void setData(T_CHART value) {
        this.data = value;
    }

    @Override
    public T_CHART getData() {
        return data;
    }

    @Override
    public StatusCode getStatusCode() {
        return statusCode;
    }

    @Override
    protected StatusCode getErrorStatusValue() {
        return StatusCode.UNKNOWN_ERROR;
    }

    @Override
    public String getMessage() {
        return statusCode.getMessage();
    }

    @Override
    public StatusCode getSuccessfulStatusValue() {
        return StatusCode.SUCCESS;
    }

    @Getter
    @AllArgsConstructor
    public enum StatusCode {
        /** OK */
        SUCCESS("success", HttpStatus.OK),

        INTERSECTION_NOT_AVAILABLE("int_uuid", "intersection_is_not_available", HttpStatus.BAD_REQUEST),
        INVALID_TIME_RANGE("invalid_time_range", HttpStatus.BAD_REQUEST),

        MISSING_TOPOLOGY_CONFIG("analysis.missing_topology", HttpStatus.BAD_REQUEST),
        MISMATCH_TOPOLOGY("analysis.mismatch_topology", HttpStatus.BAD_REQUEST),

        /* PerfLog Errors */
        /** PerfLog data is not available in selected interval */
        NO_PERFLOG_DATA("analysis.no_perflog_data", HttpStatus.BAD_REQUEST),
        /** PerfLog data is not valid */
        INVALID_PERFLOG_DATA("analysis.invalid_perflog_data", HttpStatus.INTERNAL_SERVER_ERROR),

        /** Uncaught exception */
        UNKNOWN_ERROR("unknown_error", HttpStatus.INTERNAL_SERVER_ERROR);

        private final String errorField;
        private final String message;
        private final HttpStatus httpStatus;

        StatusCode(String message, HttpStatus httpStatus) {
            this(null, message, httpStatus);
        }
    }

    @Override
    protected void setStatusCode(StatusCode value) {
        this.statusCode = value;
    }

    @Override
    public String getErrorFieldName() {
        return this.statusCode.getErrorField();
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }

}
