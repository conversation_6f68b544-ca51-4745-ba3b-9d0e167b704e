package com.siemens.spm.analysis.api.vo.detectorreport;

import java.io.Serial;
import java.io.Serializable;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Set;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.analysis.api.vo.TemplateScheduleVO;
import com.siemens.spm.analysis.api.vo.enums.ScheduleScope;
import com.siemens.spm.common.constant.TimeConstants;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DetectorTemplateScheduleVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 3661098515530404045L;

    @NotNull(message = "templates.schedule.scope_not_null")
    @JsonProperty("scope")
    private ScheduleScope scope;

    @JsonProperty("time")
    private LocalTime time;

    @JsonProperty("value")
    private Value value;

    @JsonProperty("mail_receive")
    private boolean mailReceive;

    @JsonIgnore
    public boolean isValidValue() {
        if (value == null) {
            return false;
        }

        switch (scope) {
        case ONCE:
            return value.getDate() != null;

        case EVERY_DAY:
            return value.isValidEveryDay();

        case DAY_OF_WEEK:
            return value.isValidDayOfWeeks();

        case DAY_OF_MONTH:
            return value.isValidDayOfMonths();

        default:
            return false;
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Value implements Serializable {

        private static final long serialVersionUID = 7086222170670247216L;

        @JsonProperty("date")
        private LocalDate date;

        @JsonProperty("offset")
        private Integer offset;

        @JsonProperty("day_of_week")
        private Set<DayOfWeek> dayOfWeeks;

        @JsonProperty("day_of_month")
        private Set<Integer> dayOfMonths;

        @JsonIgnore
        public boolean isValidEveryDay() {
            return date != null && offset != null
                    && offset >= TimeConstants.MIN_DAY_PER_MONTH
                    && offset <= TimeConstants.MAX_DAY_PER_MONTH;
        }

        @JsonIgnore
        public boolean isValidDayOfWeeks() {
            if (dayOfWeeks == null
                    || dayOfWeeks.size() < TimeConstants.MIN_DAY_PER_WEEK
                    || dayOfWeeks.size() > TimeConstants.MAX_DAY_PER_WEEK) {
                return false;
            }

            for (DayOfWeek dayOfWeek : dayOfWeeks) {
                if (dayOfWeek == null) {
                    return false;
                }
            }

            return true;
        }

        @JsonIgnore
        public boolean isValidDayOfMonths() {
            if (dayOfMonths == null
                    || dayOfMonths.size() < TimeConstants.MIN_DAY_PER_MONTH
                    || dayOfMonths.size() > TimeConstants.MAX_DAY_PER_MONTH) {
                return false;
            }

            for (Integer dayOfMonth : dayOfMonths) {
                if (dayOfMonth == null) {
                    return false;
                }
            }

            return true;
        }
    }

    public static DetectorTemplateScheduleVO.Value resolveScheduleValue(@NotNull DetectorTemplateScheduleVO scheduleVO) {
        DetectorTemplateScheduleVO.Value value = scheduleVO.getValue();
        if (scheduleVO.getScope() == null) {
            throw new IllegalArgumentException("Template schedule Scope can not be null!");
        }

        switch (scheduleVO.getScope()) {
        case ONCE:
            return DetectorTemplateScheduleVO.Value.builder()
                    .date(value.getDate())
                    .build();
        case EVERY_DAY:
            return DetectorTemplateScheduleVO.Value.builder()
                    .date(value.getDate())
                    .offset(value.getOffset())
                    .build();
        case DAY_OF_WEEK:
            return DetectorTemplateScheduleVO.Value.builder()
                    .dayOfWeeks(value.getDayOfWeeks())
                    .build();
        case DAY_OF_MONTH:
            return DetectorTemplateScheduleVO.Value.builder()
                    .dayOfMonths(value.getDayOfMonths())
                    .build();
        default:
            return null;
        }
    }
}
