package com.siemens.spm.analysis.api.vo.performancemetric;

import java.io.Serializable;
import java.time.LocalTime;
import java.util.List;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.analysis.util.DateTimeUtil;
import com.siemens.spm.analysis.vo.abnormaldata.AbnormalDataEventFilterVO;
import com.siemens.spm.common.shared.domaintype.analysis.AnalysisType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PMMetadataVO implements Serializable {

    private static final long serialVersionUID = -6579036919856481240L;

    @JsonProperty("bin_size")
    private String binSize;

    @JsonProperty("from_time")
    @DateTimeFormat(pattern = DateTimeUtil.TIME_FORMAT)
    private LocalTime fromTime;

    @JsonProperty("to_time")
    @DateTimeFormat(pattern = DateTimeUtil.TIME_FORMAT)
    private LocalTime toTime;

    /**
     * This field is only fo abnormal data analysis type
     */
    @JsonProperty("abnormal_data_filter")
    private List<AbnormalDataEventFilterVO> abnormalDataFilter;

    @JsonIgnore
    public boolean isValid(AnalysisType type) {
        // TODO: Validate metadata depend on analysis type

        return type != null;
    }

}
