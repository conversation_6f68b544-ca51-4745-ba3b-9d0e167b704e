package com.siemens.spm.analysis.api.vo.optimization;

import java.io.Serializable;

import org.springframework.util.StringUtils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.message.MessageService;
import com.siemens.spm.common.shared.vo.TranslatableVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OptimizationTypeVO implements TranslatableVO, Serializable {

    private static final long serialVersionUID = 1L;

    public OptimizationTypeVO(String key) {
        this.key = key;
    }

    @JsonProperty("key")
    private String key;

    @JsonProperty("name")
    private String name;

    @Override
    public void acceptTranslator(MessageService translator) {
        if (StringUtils.hasText(key)) {
            name = translator.getMessage(key.toLowerCase());
        }
    }
}
