package com.siemens.spm.analysis.api.vo.response.basepaging;

import java.util.List;

import org.springframework.http.HttpStatus;

import com.siemens.spm.common.shared.vo.AbstractResultObject;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
public class PageResultObject<T, STATUS extends PageResultObject.StatusCode> extends AbstractResultObject<PageResponse<T>, STATUS> {

    private STATUS statusCode;

    private PageResponse<T> data;

    @Override
    public PageResponse<T> getData() {
        return data;
    }

    @Override
    public STATUS getStatusCode() {
        return statusCode;
    }

    @Override
    public String getMessage() {
        return statusCode.getMessage();
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }

    @Override
    public String getErrorFieldName() {
        return statusCode.getErrorField();
    }

    @Override
    protected void setData(PageResponse<T> data) {
        this.data = data;
    }

    @Override
    protected void setStatusCode(STATUS statusCode) {
        this.statusCode = statusCode;
    }

    @Override
    protected STATUS getErrorStatusValue() {
        return (STATUS) STATUS.UNKNOWN_ERROR;
    }

    @Override
    public STATUS getSuccessfulStatusValue() {
        return (STATUS) STATUS.SUCCESS;
    }

    @Getter
    @AllArgsConstructor
    public enum StatusCode {
        /** OK */
        SUCCESS("success", HttpStatus.OK),

        NOT_FOUND("not_found", HttpStatus.NOT_FOUND),

        NO_DATA("no_data", HttpStatus.NO_CONTENT),

        INVALID_TIME_RANGE("invalid_time_range", HttpStatus.BAD_REQUEST),

        INVALID_INTERSECTION("invalid_intersection", HttpStatus.BAD_REQUEST),

        INVALID_AGENCY("invalid_agency", HttpStatus.BAD_REQUEST),

        /** Uncaught exception */
        UNKNOWN_ERROR("unknown_error", HttpStatus.INTERNAL_SERVER_ERROR);

        private final String errorField;
        private final String message;
        private final HttpStatus httpStatus;

        StatusCode(String message, HttpStatus httpStatus) {
            this(null, message, httpStatus);
        }
    }

}
