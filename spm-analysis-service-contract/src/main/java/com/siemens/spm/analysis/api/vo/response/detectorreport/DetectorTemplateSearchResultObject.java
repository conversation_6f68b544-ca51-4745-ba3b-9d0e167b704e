package com.siemens.spm.analysis.api.vo.response.detectorreport;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

import org.springframework.http.HttpStatus;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.analysis.api.vo.detectorreport.DetectorTemplateCoreDataVO;
import com.siemens.spm.common.shared.vo.AbstractResultObject;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class DetectorTemplateSearchResultObject extends AbstractResultObject<DetectorTemplateSearchResultObject.ResponseData,
        DetectorTemplateSearchResultObject.StatusCode> {

    @Serial
    private static final long serialVersionUID = 153311131089005387L;

    private ResponseData data;

    private StatusCode statusCode;

    @Override
    protected StatusCode getErrorStatusValue() {
        return StatusCode.ERROR;
    }

    @Override
    public StatusCode getSuccessfulStatusValue() {
        return StatusCode.SUCCESS;
    }

    @Override
    public ResponseData getData() {
        return data;
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }

    @Override
    public String getMessage() {
        return statusCode.getMessage();
    }

    @Override
    protected void setData(ResponseData scheduleVO) {
        data = scheduleVO;
    }

    @Override
    public String getErrorFieldName() {
        return statusCode.getErrorField();
    }

    @Override
    protected void setStatusCode(StatusCode value) {
        statusCode = value;
    }

    @Getter
    @AllArgsConstructor
    public enum StatusCode {
        SUCCESS("success", HttpStatus.OK),

        ERROR("unknown_exception", HttpStatus.INTERNAL_SERVER_ERROR),
        NOT_FOUND("no_data", HttpStatus.NOT_FOUND),

        INVALID_INPUT_EXCEPTION("invalid_input_exception", HttpStatus.BAD_REQUEST),
        MISSING_AGENCY_ID("agency_id_not_empty", HttpStatus.BAD_REQUEST),
        AGENCY_MISMATCHED("alarm_rule.user_not_access_agency_data", HttpStatus.BAD_REQUEST),
        INVALID_SORT_COLUMN("sort", "sort_column_invalid", HttpStatus.BAD_REQUEST),
        INVALID_SORT_ORDER("sort", "sort_order_invalid", HttpStatus.BAD_REQUEST);

        private final String errorField;
        private final String message;
        private final HttpStatus httpStatus;

        StatusCode(String message, HttpStatus httpStatus) {
            this(null, message, httpStatus);
        }
    }

    public static DetectorTemplateSearchResultObject buildSuccessResponse(@NotNull DetectorTemplateSearchResultObject.ResponseData data) {
        return new DetectorTemplateSearchResultObject(data, DetectorTemplateSearchResultObject.StatusCode.SUCCESS);
    }

    public static DetectorTemplateSearchResultObject error() {
        return new DetectorTemplateSearchResultObject(null, DetectorTemplateSearchResultObject.StatusCode.ERROR);
    }

    @Override
    public StatusCode getStatusCode() {
        return statusCode;
    }

    @Data
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Builder
    @AllArgsConstructor
    public static class ResponseData implements Serializable {

        @Serial
        private static final long serialVersionUID = -9181508533781760336L;

        @JsonProperty("total_count")
        private Long totalCount;

        @JsonProperty("detector_templates")
        private List<DetectorTemplateCoreDataVO> detectorTemplates;
    }
}
