package com.siemens.spm.analysis.api.vo.detectorreport;

import java.io.Serial;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.shared.vo.IntersectionIdsVO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DetectorTemplateDetailVO extends DetectorTemplateSimpleVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 8655098975575304480L;

    @Valid
    @NotNull
    @JsonProperty("general_data")
    private DetectorTemplateGeneralVO generalVO;

    @NotNull(message = "intersection_not_null")
    @JsonProperty("intersection")
    private IntersectionIdsVO intersectionIdsVO;

    @NotNull(message = "templates.schedule.not_null")
    @JsonProperty("schedule")
    private DetectorTemplateScheduleVO scheduleVO;
}
