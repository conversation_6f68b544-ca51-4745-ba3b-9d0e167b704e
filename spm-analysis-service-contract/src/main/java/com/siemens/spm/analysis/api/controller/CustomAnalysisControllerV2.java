package com.siemens.spm.analysis.api.controller;

import java.time.LocalDateTime;

import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import com.siemens.spm.analysis.api.vo.CustomAnalysisVO;
import com.siemens.spm.analysis.api.vo.response.CustomAnalysisObject;
import com.siemens.spm.analysis.api.vo.response.CustomAnalysisResultObject;
import com.siemens.spm.analysis.api.vo.response.basepaging.PageResultObject;
import com.siemens.spm.common.api.PublicController;
import com.siemens.spm.common.constant.AgencyConstants;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;

@Tag(name = "analysis-v2", description = "Analysis Resources")
@RequestMapping(CustomAnalysisControllerV2.API_ROOT)
public interface CustomAnalysisControllerV2 extends PublicController {

    String VERSION = "/v2";
    String CUSTOM_ANALYSIS_RESOURCE = "/analysis/custom-analysis";

    String API_ROOT = PUBLIC_API + VERSION + CUSTOM_ANALYSIS_RESOURCE;

    @PostMapping
    @Operation(summary = "Create a Custom Analysis filter")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "400", description = "Bad Request or data is not available"),
            @ApiResponse(responseCode = "200", description = "Create successfully"),
            @ApiResponse(responseCode = "500", description = "Unknown error")
    })
    ResponseEntity<CustomAnalysisResultObject> createCustomAnalysis(
            @Parameter(name = AgencyConstants.AGENCY_ID_HEADER_V2, 
                       example = AgencyConstants.AGENCY_ID_HEADER_V2 + "=4246",
                       required = true,
                       in = ParameterIn.HEADER, description = "Agency Id")
            @RequestHeader(AgencyConstants.AGENCY_ID_HEADER_V2) Integer agencyId,

            @Parameter(description = "Body of request")
            @Valid
            @RequestBody CustomAnalysisVO request
    );

    @PutMapping("/{id}")
    @Operation(summary = "Update a Custom Analysis filter")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "400", description = "Bad Request or data is not available"),
            @ApiResponse(responseCode = "200", description = "Update successfully"),
            @ApiResponse(responseCode = "500", description = "Unknown error")
    })
    ResponseEntity<CustomAnalysisResultObject> updateCustomAnalysis(
            @Parameter(name = AgencyConstants.AGENCY_ID_HEADER_V2, 
                       example = AgencyConstants.AGENCY_ID_HEADER_V2 + "=4246",
                       required = true,
                       in = ParameterIn.HEADER, description = "Agency Id")
            @RequestHeader(AgencyConstants.AGENCY_ID_HEADER_V2) Integer agencyId,

            @Parameter(description = "ID")
            @PathVariable(value = "id", required = true) Long id,
            @Parameter(description = "Body of request")
            @RequestBody
            @Valid CustomAnalysisVO request
    );

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete a Custom Analysis filter by ID ")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "400", description = "Bad Request or data is not available"),
            @ApiResponse(responseCode = "200", description = "Delete successfully"),
            @ApiResponse(responseCode = "500", description = "Unknown error")
    })
    ResponseEntity<CustomAnalysisResultObject> deleteCustomAnalysis(
            @Parameter(name = AgencyConstants.AGENCY_ID_HEADER_V2, 
                       example = AgencyConstants.AGENCY_ID_HEADER_V2 + "=4246",
                       required = true,
                       in = ParameterIn.HEADER, description = "Agency Id")
            @RequestHeader(AgencyConstants.AGENCY_ID_HEADER_V2) Integer agencyId,

            @Parameter(description = "Id of CustomAnalysis is deleted")
            @PathVariable(value = "id", required = true) Long id
    );

    @GetMapping
    @Operation(summary = "List Custom Analysis filter by paging")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "400", description = "Bad Request or data is not available"),
            @ApiResponse(responseCode = "200", description = "OK"),
            @ApiResponse(responseCode = "500", description = "Unknown error")
    })
    ResponseEntity<PageResultObject<CustomAnalysisVO, PageResultObject.StatusCode>> getCustomAnalysis(
            @Parameter(name = AgencyConstants.AGENCY_ID_HEADER_V2, 
                       example = AgencyConstants.AGENCY_ID_HEADER_V2 + "=4246",
                       required = true,
                       in = ParameterIn.HEADER, description = "Agency Id")
            @RequestHeader(AgencyConstants.AGENCY_ID_HEADER_V2) Integer agencyId,

            @Parameter(description = "Page to list")
            @RequestParam(value = "page", defaultValue = "0") int page,

            @Parameter(description = "Number record per page")
            @RequestParam(value = "size", defaultValue = "50") int size
    );

    @GetMapping("/{id}/run")
    @Operation(summary = "Run Custom Analysis apply by filter, intersection and time range")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "400", description = "Bad Request or data is not available"),
            @ApiResponse(responseCode = "200", description = "OK"),
            @ApiResponse(responseCode = "500", description = "Unknown error")
    })
    ResponseEntity<CustomAnalysisObject> runCustomAnalysis(
            @Parameter(description = "Id CustomAnalysis")
            @PathVariable(value = "id") Long id,

            @Parameter(name = AgencyConstants.AGENCY_ID_HEADER_V2, 
                       example = AgencyConstants.AGENCY_ID_HEADER_V2 + "=4246",
                       required = true,
                       in = ParameterIn.HEADER, description = "Agency Id")
            @RequestHeader(AgencyConstants.AGENCY_ID_HEADER_V2) Integer agencyId,

            @Parameter(description = "Intersection UUID")
            @RequestParam(value = "int_uuid") String intUuid,

            @Parameter(description = "From time to get data")
            @RequestParam(value = "from_time", required = true)
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime fromTime,

            @Parameter(description = "To time to get data")
            @RequestParam(value = "to_time", required = true)
            @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
            LocalDateTime toTime
    );

}
