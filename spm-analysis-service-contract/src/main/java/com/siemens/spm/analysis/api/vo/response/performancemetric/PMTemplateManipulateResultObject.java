package com.siemens.spm.analysis.api.vo.response.performancemetric;

import org.springframework.http.HttpStatus;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.siemens.spm.common.shared.vo.AbstractSimpleResultObject;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PMTemplateManipulateResultObject
        extends AbstractSimpleResultObject<PMTemplateManipulateResultObject.PMTemplateManipulateStatusCode> {

    private static final long serialVersionUID = 5673885780207393987L;

    private PMTemplateManipulateStatusCode statusCode;

    @Override
    public String getMessage() {
        return statusCode.getMessage();
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }

    @Override
    public String getErrorFieldName() {
        return statusCode.getErrorField();
    }

    @Override
    protected PMTemplateManipulateStatusCode getErrorStatusValue() {
        return PMTemplateManipulateStatusCode.ERROR;
    }

    @Override
    public PMTemplateManipulateStatusCode getSuccessfulStatusValue() {
        return PMTemplateManipulateStatusCode.SUCCESS;
    }

    @Getter
    @AllArgsConstructor
    public enum PMTemplateManipulateStatusCode {

        SUCCESS("success", HttpStatus.OK),
        NOT_FOUND("not_found", HttpStatus.NOT_FOUND),
        NO_CONTENT("no_content", HttpStatus.NO_CONTENT),
        CREATED("created", HttpStatus.CREATED),

        // Bad request
        INVALID_REQUEST("body", "performance_metric.templates.invalid_request_data", HttpStatus.BAD_REQUEST),
        TEMPLATE_NOT_FOUND("templates.not_found", HttpStatus.BAD_REQUEST),
        NO_ACTION("action", "no_action", HttpStatus.BAD_REQUEST),
        ACTION_INVALID("action", "templates.action_invalid", HttpStatus.BAD_REQUEST),
        INTERSECTION_SCOPE_INVALID("intersection: scope", "templates.intersections.scope_invalid",
                HttpStatus.BAD_REQUEST),
        DATE_RANGE_CONFLICT_SCHEDULE("date_range", "templates.date_range_conflict_schedule", HttpStatus.BAD_REQUEST),
        INVALID_DATE_RANGE("date_range", "performance_metric.templates.invalid_date_range", HttpStatus.BAD_REQUEST),

        // Status codes map 1-1 with status code of
        // IntersectionsInAgencyVerifyResultObject
        MISSING_AGENCY_ID("agency_id", "agency_id_is_missing", HttpStatus.BAD_REQUEST),
        AGENCY_NOT_FOUND("agency_id", "agency_not_found", HttpStatus.BAD_REQUEST),
        INTERSECTION_NOT_FOUND("intersection", "intersection_not_found", HttpStatus.BAD_REQUEST),
        INTERSECTION_NOT_UNDER_AGENCY_MANAGEMENT("intersection", "intersection_not_under_agency_management",
                HttpStatus.BAD_REQUEST),
        // end status codes mapping

        ERROR("unknown_exception", HttpStatus.INTERNAL_SERVER_ERROR);

        private final String errorField;
        private final String message;
        private final HttpStatus httpStatus;

        PMTemplateManipulateStatusCode(String message, HttpStatus httpStatus) {
            this(null, message, httpStatus);
        }

    }

    public static PMTemplateManipulateResultObject build(PMTemplateManipulateStatusCode statusCode) {
        if (statusCode == null) {
            throw new IllegalArgumentException("Status code can not be null!");
        }

        return new PMTemplateManipulateResultObject(statusCode);

    }

}
