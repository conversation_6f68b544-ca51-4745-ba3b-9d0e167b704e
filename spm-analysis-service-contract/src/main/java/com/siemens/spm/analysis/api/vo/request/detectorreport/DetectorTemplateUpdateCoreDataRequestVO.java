package com.siemens.spm.analysis.api.vo.request.detectorreport;

import java.io.Serial;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.siemens.spm.analysis.api.vo.detectorreport.DetectorTemplateGeneralVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DetectorTemplateUpdateCoreDataRequestVO extends DetectorTemplateGeneralVO {

    @Serial
    private static final long serialVersionUID = -3776606416526097622L;
}
