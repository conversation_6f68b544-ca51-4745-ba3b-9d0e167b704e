/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AnalysisService.java
 * Project     : SPM Platform
 */

package com.siemens.spm.analysis.api.boundary;

import java.time.LocalDateTime;

import com.siemens.spm.analysis.api.vo.CustomAnalysisVO;
import com.siemens.spm.analysis.api.vo.PerflogEventVO;
import com.siemens.spm.analysis.api.vo.response.CustomAnalysisObject;
import com.siemens.spm.analysis.api.vo.response.CustomAnalysisResultObject;
import com.siemens.spm.analysis.api.vo.response.basepaging.PageResultObject;

public interface CustomAnalysisService {

    CustomAnalysisResultObject createCustomAnalysis(CustomAnalysisVO request);

    CustomAnalysisResultObject updateCustomAnalysis(CustomAnalysisVO request);

    CustomAnalysisResultObject deleteCustomAnalysis(Long id);

    PageResultObject<CustomAnalysisVO, PageResultObject.StatusCode> getCustomAnalysis(Integer agencyId, int page, int size);

    CustomAnalysisObject runCustomAnalysis(
            Long id,
            Integer agencyId,
            String intUuid,
            LocalDateTime fromTime,
            LocalDateTime toTime);
}
