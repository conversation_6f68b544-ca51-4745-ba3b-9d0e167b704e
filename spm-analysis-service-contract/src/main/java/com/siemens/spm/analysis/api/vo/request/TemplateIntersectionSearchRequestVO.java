package com.siemens.spm.analysis.api.vo.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.shared.vo.request.IntersectionSearchSimpleRequestVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TemplateIntersectionSearchRequestVO extends IntersectionSearchSimpleRequestVO {

    private static final long serialVersionUID = -1311600861607469400L;

    @JsonProperty("template_id")
    private Long templateId;
}
