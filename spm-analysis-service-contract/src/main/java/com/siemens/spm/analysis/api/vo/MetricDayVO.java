/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : MetricDayVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.analysis.api.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
public class MetricDayVO implements Serializable {

    private static final long serialVersionUID = 9031022121531231224L;

    @JsonProperty("date")
    private LocalDate date;

    @JsonProperty("value")
    private Double value;

    @Builder.Default
    private boolean dayHasData = false;

    @Builder.Default
    private boolean isMissingDay = true;

    @Builder.Default
    private boolean isDisabledDay = false;

    @JsonProperty("hourBlockCount")
    private int availableHourCount;

    @JsonProperty("missingHourCount")
    private int missingHourCount;

    @JsonProperty("disabledHourCount")
    private int disabledHourCount;

    @JsonProperty("availablePercent")
    private double percentAvailableData;

    @JsonProperty("percentNoData")
    private double percentNoData;

    @JsonProperty("percentDisabledHour")
    private double percentDisabledHour;

    public static MetricDayVO evaluateDay(MetricDayVO metricDayVO) {
        metricDayVO.dayHasData = metricDayVO.getAvailableHourCount() > 0;
        metricDayVO.isDisabledDay = !metricDayVO.dayHasData && metricDayVO.disabledHourCount > 0;
        metricDayVO.isMissingDay = !metricDayVO.dayHasData && !metricDayVO.isDisabledDay;

        return metricDayVO;
    }
}
