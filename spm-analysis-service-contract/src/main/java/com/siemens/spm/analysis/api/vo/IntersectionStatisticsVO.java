/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : IntersectionAorPercentVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.analysis.api.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.util.MathUtil;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.sql.Timestamp;

@Slf4j
@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class IntersectionStatisticsVO implements Serializable {

    private static final long serialVersionUID = -3775496355202620670L;

    private static final int STATISTIC_HOURS = 24;

    @JsonProperty("int_uuid")
    private String intUUID;

    @JsonProperty("aor_percent")
    private double aorPercent;

    @JsonProperty("aog_percent")
    private double aogPercent;

    @JsonProperty("avg_ped_delay")
    private double avgPedDelay;

    @JsonProperty("avg_app_delay")
    private double avgAppDelay;

    @JsonProperty("vehicle_volume")
    private long vehicleVolume;

    @JsonProperty("coord_health_trans")
    private double coordHealthTrans;

    @JsonProperty("split_failure")
    private long splitFailure;

    @JsonProperty("pp_requests")
    private long ppRequests;

    @JsonProperty("rlv_count")
    private long rlvCount;

    @JsonProperty("avg_queue_length")
    private double avgQueueLength;

    @JsonProperty("avg_preemption_priority")
    private double avgPreemptionRequest;

    @JsonProperty("updated_time")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Timestamp updatedTime;

    @JsonIgnore
    private int detHit;

    @JsonIgnore
    private int aorHit;

    @JsonIgnore
    private int aogHit;

    @JsonIgnore
    private int transHit;

    @JsonIgnore
    private int instepHit;

    @JsonIgnore
    private int splitFail;

    @JsonIgnore
    private int totalPedDelayHit;

    @JsonIgnore
    private long totalSumPedDelayTime;

    @JsonIgnore
    private int totalAppDelayHit;

    @JsonIgnore
    private double totalAppDelayTime;

    @JsonIgnore
    private double totalAvgQueueLength;

    @JsonIgnore
    private int numAvgQueueLength;

    public IntersectionStatisticsVO(String intUUID, Timestamp updatedTime) {
        this.intUUID = intUUID;
        this.updatedTime = updatedTime;
        this.detHit = 0;
        this.aorHit = 0;
        this.aogHit = 0;
        this.transHit = 0;
        this.instepHit = 0;
        this.splitFail = 0;
        this.totalAppDelayHit = 0;
        this.totalAppDelayTime = 0;
        this.rlvCount = 0;
    }

    /**
     * @param detectorHit
     */
    public void addDetectorHit(Integer detectorHit) {
        if (detectorHit == null) {
            return;
        }
        this.detHit += detectorHit;
    }

    public void addAvgQueueLength(Double avgQueueLength) {
        if (avgQueueLength == null || avgQueueLength < 0) {
            return;
        }
        this.totalAvgQueueLength += avgQueueLength;
        this.numAvgQueueLength++;
    }

    private void updateAvgQueueLength() {
        if (this.numAvgQueueLength == 0) {
            this.avgQueueLength = 0;
            return;
        }

        double ret = MathUtil.roundUpValue(this.totalAvgQueueLength / this.numAvgQueueLength);
        log.info("[{}]Update average queue length: {} / {} = {}", intUUID, totalAvgQueueLength, numAvgQueueLength, avgQueueLength);
        this.avgQueueLength = ret > 200 ? 0 : ret;
    }

    /**
     *
     */
    public void updateVehicleVolume(long hours) {
        this.vehicleVolume = Math.round((double) this.detHit / hours);
    }

    /**
     * @param aorHit
     */
    public void addAorHit(Integer aorHit) {
        if (aorHit == null) {
            return;
        }
        this.aorHit += aorHit;
    }

    /**
     * @param aogHit
     */
    public void addAogHit(Integer aogHit) {
        if (aogHit == null) {
            return;
        }
        this.aogHit += aogHit;
    }

    protected void updateAorPercent() {
        if (0 == detHit) {
            aorPercent = 0; // Avoid division by zero
        } else {
            aorPercent = MathUtil.roundUpValue((double) aorHit / detHit * 100.0);
            log.info("[{}]Update AOR percent: {} / {} = {}", intUUID, aorHit, detHit, aorPercent);
        }
    }

    protected void updateAogPercent() {
        if (0 == detHit) {
            aogPercent = 0; // Avoid division by zero
        } else {
            aogPercent = MathUtil.roundUpValue((double) aogHit / detHit * 100.0);
            log.info("[{}]Update AOG percent: {} / {} = {}", intUUID, aogHit, detHit, aogPercent);
        }
    }

    /**
     * @param transHit
     * @param instepHit
     */
    public void addTransHit(Integer transHit, Integer instepHit) {
        if (transHit != null) {
            this.transHit += transHit;
        }
        if (instepHit != null) {
            this.instepHit += instepHit;
        }
    }

    protected void updateCoordHealthTransPercent() {
        if (0 == transHit) {
            coordHealthTrans = 0;
        } else {
            coordHealthTrans = MathUtil.roundUpValue((100.0 * transHit) / (transHit + instepHit) * 100.0);
        }
    }

    public void addSplitFailure(Integer splitFailure) {
        if (splitFailure != null) {
            this.splitFail += splitFailure;
        }
    }

    public void addPpRequests(Integer ppRequests) {
        if (ppRequests != null) {
            this.ppRequests += ppRequests;
        }
    }

    /**
     *
     */
    public void updateAvgPreemptionRequest(long hours) {
        if (hours <= 0) {
            this.avgPreemptionRequest = 0;
            return;
        }
        this.avgPreemptionRequest = MathUtil.roundUpValue((double) this.ppRequests / hours);
    }

    /**
     * @param pedDelayHit
     * @param sumPedDelayTime
     */
    public void updatePedDelay(Integer pedDelayHit, Long sumPedDelayTime) {
        if (pedDelayHit != null) {
            totalPedDelayHit += pedDelayHit;
        }

        if (sumPedDelayTime != null) {
            totalSumPedDelayTime += sumPedDelayTime;
        }
    }

    private void updateAvgPedDelay() {
        if (totalPedDelayHit == 0) {
            avgPedDelay = 0;
        } else {
            avgPedDelay = MathUtil.roundUpValue((double) this.totalSumPedDelayTime / this.totalPedDelayHit);
            log.info("[{}]Update average pedestrian delay: {} / {} = {}", intUUID, totalSumPedDelayTime, totalPedDelayHit, avgPedDelay);
        }
    }

    public void addAppDelay(Integer appDelayHit, Double totalAppDelayTime) {
        if (appDelayHit != null) {
            this.totalAppDelayHit += appDelayHit;
        }

        if (totalAppDelayTime != null) {
            this.totalAppDelayTime += totalAppDelayTime;
        }
    }

    private void updateAvgAppDelay() {
        if (totalAppDelayHit == 0) {
            avgAppDelay = 0;
        } else {
            avgAppDelay = MathUtil.roundUpValue(this.totalAppDelayTime / this.totalAppDelayHit);
        }
    }

    public void addRLVCount(Integer rlvCount) {
        if (rlvCount != null) {
            this.rlvCount += rlvCount;
        }
    }

    public void updateAverage(long hours) {
        updateVehicleVolume(hours);
        updateAogPercent();
        updateAorPercent();
        updateCoordHealthTransPercent();
        updateAvgPedDelay();
        updateAvgAppDelay();
        updateAvgPreemptionRequest(hours);
        updateAvgQueueLength();
    }

}
