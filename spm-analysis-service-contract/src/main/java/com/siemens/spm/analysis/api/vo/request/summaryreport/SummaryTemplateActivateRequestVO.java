package com.siemens.spm.analysis.api.vo.request.summaryreport;

import jakarta.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.analysis.api.vo.enums.TemplateStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SummaryTemplateActivateRequestVO extends SummaryTemplateSimpleRequestVO {

    private static final long serialVersionUID = 716936422801192009L;

    @NotNull(message = "templates.status_not_empty")
    @JsonProperty("status")
    private TemplateStatus status;

}
