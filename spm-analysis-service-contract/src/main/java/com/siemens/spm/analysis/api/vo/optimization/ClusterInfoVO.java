package com.siemens.spm.analysis.api.vo.optimization;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ClusterInfoVO implements Serializable {

    private static final long serialVersionUID = -3842114299527758862L;

    @JsonProperty("min")
    protected Double min;

    @JsonProperty("max")
    protected Double max;

    @JsonProperty("avg")
    protected Double avg;

    @JsonProperty("group1_min")
    protected Double group1Min;

    @JsonProperty("group1_max")
    protected Double group1Max;

    @JsonProperty("group1_avg")
    protected Double group1Avg;

    @JsonProperty("group2_min")
    protected Double group2Min;

    @JsonProperty("group2_max")
    protected Double group2Max;

    @JsonProperty("group2_avg")
    protected Double group2Avg;

}
