package com.siemens.spm.analysis.api.vo.response.summaryreport;

import org.springframework.http.HttpStatus;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.siemens.spm.analysis.api.vo.summaryreport.SummaryMetricResponseVO;
import com.siemens.spm.common.shared.vo.AbstractResultObject;
import com.siemens.spm.common.shared.vo.SimpleResultObject.SimpleStatusCode;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SummaryMetricResultObject extends AbstractResultObject<SummaryMetricResponseVO, SimpleStatusCode> {

    private static final long serialVersionUID = -4539997534793940250L;

    private SummaryMetricResponseVO data;

    private SimpleStatusCode statusCode;

    public SummaryMetricResultObject(SimpleStatusCode statusCode) {
        this.statusCode = statusCode;
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }

    @Override
    public String getMessage() {
        return statusCode.getMessage();
    }

    @Override
    protected SimpleStatusCode getErrorStatusValue() {
        return SimpleStatusCode.ERROR;
    }

    @Override
    public SimpleStatusCode getSuccessfulStatusValue() {
        return SimpleStatusCode.SUCCESS;
    }

    @Override
    public String getErrorFieldName() {
        return statusCode.getErrorField();
    }

}
