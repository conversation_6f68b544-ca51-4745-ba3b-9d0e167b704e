package com.siemens.spm.analysis.api.vo.request.detectorreport;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.analysis.api.vo.enums.TemplateStatus;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DetectorTemplateActivateRequestVO {

    @NotEmpty(message = "templates.template_ids_not_empty")
    @JsonProperty("template_ids")
    private List<Long> templateIds;

    @NotNull(message = "templates.status_not_empty")
    @JsonProperty("status")
    private TemplateStatus status;
}
