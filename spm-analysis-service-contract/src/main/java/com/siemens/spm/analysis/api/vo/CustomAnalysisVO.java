package com.siemens.spm.analysis.api.vo;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.analysis.vo.EventFilterVO;
import com.siemens.spm.common.shared.vo.SimpleOwnerVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Data
@EqualsAndHashCode
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CustomAnalysisVO implements Serializable {

    @JsonProperty("id")
    private Long id;

    @JsonProperty("name")
    @NotEmpty
    @NotNull
    private String name;

    @JsonProperty("agency_id")
    private Integer agencyId;

    @JsonProperty("events")
    @NotNull
    private List<EventFilterVO> eventCodeParams;

    @JsonProperty("owner")
    private SimpleOwnerVO owner;

    @JsonProperty("owner_id")
    private Long ownerId;

}
