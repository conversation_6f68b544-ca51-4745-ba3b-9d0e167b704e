package com.siemens.spm.analysis.api.intercom;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import com.siemens.spm.common.intercom.InterComController;
import com.siemens.spm.common.shared.vo.SimpleResultObject;

@RequestMapping(PMTemplateInterComController.API_ROOT)
public interface PMTemplateInterComController extends InterComController {

    String VERSION = "/v1";
    String API_ROOT = INTERNAL_API + VERSION + "/performance-metric/templates";

    @PostMapping("/scanning")
    ResponseEntity<SimpleResultObject> scanTemplates();

}
