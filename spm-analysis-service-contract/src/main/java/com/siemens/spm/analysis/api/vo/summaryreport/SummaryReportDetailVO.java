package com.siemens.spm.analysis.api.vo.summaryreport;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SummaryReportDetailVO implements Serializable {

    private static final long serialVersionUID = -2480539026953159732L;

    @JsonProperty("summary_report")
    private SummaryReportInfoVO reportInfoVO;

    @JsonProperty("report_results")
    private List<SummaryReportDataVO> reportDataVOs;

    @JsonProperty("prev_result_time")
    private Long prevResultTime;

}
