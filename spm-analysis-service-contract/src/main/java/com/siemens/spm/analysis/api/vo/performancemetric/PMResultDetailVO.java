package com.siemens.spm.analysis.api.vo.performancemetric;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PMResultDetailVO extends PMResultVO {

    private static final long serialVersionUID = -1859098762880651680L;

    @JsonProperty("agency_name")
    private String agencyName;

    @JsonProperty("timezone")
    private String timezone;

    /**
     * This field is json data which contain all information about an analysis
     */
    @JsonProperty("chart_data")
    private String chartDataLob;

}
