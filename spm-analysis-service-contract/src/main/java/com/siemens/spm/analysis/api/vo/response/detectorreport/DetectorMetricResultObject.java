package com.siemens.spm.analysis.api.vo.response.detectorreport;

import java.io.Serial;

import org.springframework.http.HttpStatus;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.siemens.spm.common.shared.vo.AbstractResultObject;
import com.siemens.spm.common.shared.vo.SimpleResultObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DetectorMetricResultObject extends AbstractResultObject<DetectorMetricResponseVO, SimpleResultObject.SimpleStatusCode> {

    @Serial
    private static final long serialVersionUID = 5263371768612417699L;

    private DetectorMetricResponseVO data;

    private SimpleResultObject.SimpleStatusCode statusCode;

    public DetectorMetricResultObject(SimpleResultObject.SimpleStatusCode statusCode) {
        this.statusCode = statusCode;
    }

    @Override
    public String getMessage() {
        return statusCode.getMessage();
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }

    @Override
    public String getErrorFieldName() {
        return statusCode.getErrorField();
    }

    @Override
    protected SimpleResultObject.SimpleStatusCode getErrorStatusValue() {
        return SimpleResultObject.SimpleStatusCode.ERROR;
    }

    @Override
    public SimpleResultObject.SimpleStatusCode getSuccessfulStatusValue() {
        return SimpleResultObject.SimpleStatusCode.SUCCESS;
    }
}
