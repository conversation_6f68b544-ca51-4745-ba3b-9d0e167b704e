package com.siemens.spm.analysis.api.boundary;

import com.siemens.spm.analysis.api.vo.TemplateScheduleVO;
import com.siemens.spm.analysis.api.vo.request.IntersectionIdsRequestVO;
import com.siemens.spm.analysis.api.vo.request.summaryreport.SummaryTemplateActivateRequestVO;
import com.siemens.spm.analysis.api.vo.request.summaryreport.SummaryTemplateCreateRequestVO;
import com.siemens.spm.analysis.api.vo.request.summaryreport.SummaryTemplateDeleteRequestVO;
import com.siemens.spm.analysis.api.vo.request.summaryreport.SummaryTemplateSearchRequestVO;
import com.siemens.spm.analysis.api.vo.request.summaryreport.SummaryTemplateUpdateCoreDataRequestVO;
import com.siemens.spm.analysis.api.vo.request.TemplateIntersectionSearchRequestVO;
import com.siemens.spm.analysis.api.vo.response.IntersectionSearchResultObject;
import com.siemens.spm.analysis.api.vo.response.summaryreport.SummaryMetricResultObject;
import com.siemens.spm.analysis.api.vo.response.summaryreport.SummaryTemplateCoreDataResultObject;
import com.siemens.spm.analysis.api.vo.response.summaryreport.SummaryTemplateDetailResultObject;
import com.siemens.spm.analysis.api.vo.response.summaryreport.SummaryTemplateManipulateResultObject;
import com.siemens.spm.analysis.api.vo.response.summaryreport.SummaryTemplateProcessResultObject;
import com.siemens.spm.analysis.api.vo.response.summaryreport.SummaryTemplateSearchResultObject;
import com.siemens.spm.analysis.api.vo.response.TemplateScheduleResultObject;
import com.siemens.spm.common.shared.vo.SimpleResultObject;

/**
 * <AUTHOR> Nguyen
 */
public interface SummaryReportTemplateService {

    /**
     * Create summary report template from request
     *
     * @param requestVO {@link SummaryTemplateCreateRequestVO}
     * @return {@link SummaryTemplateDetailResultObject}
     */
    SummaryTemplateDetailResultObject createReportTemplate(SummaryTemplateCreateRequestVO requestVO);

    /**
     * Delete multiple summary report templates
     *
     * @param deleteRequestVO {@link SummaryTemplateDeleteRequestVO} list of template ids
     * @return {@link SummaryTemplateManipulateResultObject}
     */
    SummaryTemplateManipulateResultObject deleteReportTemplates(SummaryTemplateDeleteRequestVO deleteRequestVO);

    /**
     * Update intersection(s) of a summary report template(delete or add depend on action field)
     *
     * @param templateId               id of template need to update intersection(s)
     * @param intersectionIdsRequestVO {@link IntersectionIdsRequestVO} object request
     * @return {@link SummaryTemplateManipulateResultObject}
     */
    SummaryTemplateManipulateResultObject updateIntersections(Long templateId,
                                                              IntersectionIdsRequestVO intersectionIdsRequestVO);

    /**
     * Update status of summary report template(ACTIVE or INACTIVE depend on action field) Effect with multiple report
     * templates
     *
     * @param activeRequestVO {@link SummaryTemplateActivateRequestVO}
     * @return {@link SummaryTemplateManipulateResultObject}
     */
    SummaryTemplateManipulateResultObject activateReportTemplates(SummaryTemplateActivateRequestVO activeRequestVO);

    /**
     * Get core data of a summary report template by template id
     *
     * @param templateId id of template need to retrieve core data
     * @return {@link SummaryTemplateCoreDataResultObject}
     */
    SummaryTemplateCoreDataResultObject getReportTemplateCoreData(Long templateId);

    /**
     * Search intersection(s) in a summary report template.
     *
     * @param searchRequestVO {@link TemplateIntersectionSearchRequestVO} request object
     * @return {@link IntersectionSearchResultObject}
     */
    IntersectionSearchResultObject searchTemplateIntersections(TemplateIntersectionSearchRequestVO searchRequestVO);

    /**
     * Get schedule data of a summary report template by id
     *
     * @param templateId id of template
     * @return {@link TemplateScheduleResultObject}
     */
    TemplateScheduleResultObject getReportTemplateScheduleData(Long templateId);

    /**
     * Search all report template(s) be filter in {@link SummaryTemplateSearchRequestVO} object
     *
     * @param requestVO {@link SummaryTemplateSearchRequestVO} object to filter
     * @return {@link SummaryTemplateSearchResultObject}
     */
    SummaryTemplateSearchResultObject searchReportTemplates(SummaryTemplateSearchRequestVO requestVO);

    /**
     * Update core data of a summary report template
     *
     * @param templateId      id of report template need to update core data
     * @param updateRequestVO {@link SummaryTemplateUpdateCoreDataRequestVO} data to update
     * @return {@link SummaryTemplateManipulateResultObject}
     */
    SummaryTemplateManipulateResultObject updateReportTemplateCoreData(Long templateId,
                                                                       SummaryTemplateUpdateCoreDataRequestVO updateRequestVO);

    /**
     * Update schedule data of a summary report template by id
     *
     * @param templateId         id of report template
     * @param templateScheduleVO {@link TemplateScheduleVO} object
     * @return {@link SummaryTemplateManipulateResultObject}
     */
    SummaryTemplateManipulateResultObject updateReportTemplateScheduleData(Long templateId,
                                                                           TemplateScheduleVO templateScheduleVO);

    /**
     * Search all available intersection(s) to add in a summary report template.
     *
     * @param searchRequestVO {@link TemplateIntersectionSearchRequestVO} request object
     * @return {@link IntersectionSearchResultObject}
     */
    IntersectionSearchResultObject searchAvailableTemplateInterSections(TemplateIntersectionSearchRequestVO searchRequestVO);

    /**
     * Scanning the report template
     *
     * @return
     */
    SimpleResultObject scanReportTemplate();

    /**
     * Run manually a summary report template
     *
     * @param templateId
     * @return
     */
    SummaryTemplateProcessResultObject runReportTemplate(Long templateId);

    /**
     * Get all metrics available for template setting
     *
     * @return {@link SummaryMetricResultObject}
     */
    SummaryMetricResultObject getAllMetrics();

}
