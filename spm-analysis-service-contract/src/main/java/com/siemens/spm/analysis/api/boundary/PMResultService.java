package com.siemens.spm.analysis.api.boundary;

import java.util.List;

import com.siemens.spm.analysis.api.vo.request.performancemetric.PMResultSearchRequestVO;
import com.siemens.spm.analysis.api.vo.response.performancemetric.PMDetailResultObject;
import com.siemens.spm.analysis.api.vo.response.performancemetric.PMResultManipulateResultObject;
import com.siemens.spm.analysis.api.vo.response.performancemetric.PMResultSearchResultObject;

public interface PMResultService {

    PMResultSearchResultObject search(PMResultSearchRequestVO pmResultSearchRequestVO);

    PMDetailResultObject getPMResultDetail(Long resultId);

    PMResultManipulateResultObject deletes(List<Long> ids);

}
