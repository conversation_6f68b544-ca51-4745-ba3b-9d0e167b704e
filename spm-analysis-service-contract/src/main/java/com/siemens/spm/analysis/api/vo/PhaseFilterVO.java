/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : PhaseFilterVO.java
 * Project     : spm-analysis-service
 */
package com.siemens.spm.analysis.api.vo;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
public class PhaseFilterVO implements Serializable {

    /**
     * 
     */
    private static final long serialVersionUID = 1L;

    @JsonProperty("phase")
    private int phase;

    @JsonProperty("visisble")
    private boolean visible;

}
