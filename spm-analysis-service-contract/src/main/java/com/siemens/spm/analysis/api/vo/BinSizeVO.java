package com.siemens.spm.analysis.api.vo;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.analysis.api.vo.enums.BinSize;
import com.siemens.spm.common.message.MessageService;
import com.siemens.spm.common.shared.vo.TranslatableVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BinSizeVO implements TranslatableVO, Serializable {

    private static final long serialVersionUID = 162988629287862623L;

    @JsonProperty("key")
    private String key;

    @JsonProperty("value")
    private Integer value;

    @JsonProperty("name")
    private String name;

    @Override
    public void acceptTranslator(MessageService translator) {
        this.name = translator.getMessage(key);
    }

    public static BinSizeVO of(BinSize binSize) {
        if (binSize == null) {
            throw new IllegalArgumentException("binSize can not be null");
        }
        return BinSizeVO.builder()
                .key(binSize.getKey())
                .value(binSize.getValueInSecond())
                .build();
    }

}
