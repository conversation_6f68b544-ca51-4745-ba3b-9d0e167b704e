package com.siemens.spm.analysis.api.vo.constraint;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Constraint(validatedBy = {})
@Retention(RetentionPolicy.RUNTIME)
@Target({ ElementType.FIELD, ElementType.PARAMETER })
public @interface ValidDetectorTemplateSchedule {

    String message() default "Schedule is invalid";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
