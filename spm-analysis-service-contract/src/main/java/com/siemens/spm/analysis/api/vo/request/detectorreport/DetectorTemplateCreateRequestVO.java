package com.siemens.spm.analysis.api.vo.request.detectorreport;

import java.io.Serial;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.analysis.api.vo.constraint.ValidDetectorTemplate;
import com.siemens.spm.analysis.api.vo.constraint.ValidDetectorTemplateSchedule;
import com.siemens.spm.analysis.api.vo.detectorreport.DetectorTemplateGeneralVO;
import com.siemens.spm.analysis.api.vo.detectorreport.DetectorTemplateScheduleVO;
import com.siemens.spm.common.shared.vo.IntersectionIdsVO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DetectorTemplateCreateRequestVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1666672081103296269L;

    @Valid
    @NotNull
    @JsonProperty("general_data")
    @ValidDetectorTemplate(message = "templates.invalid_data")
    private DetectorTemplateGeneralVO generalVO;

    @Valid
    @NotNull(message = "intersection_not_null")
    @JsonProperty("intersection")
    private IntersectionIdsVO intersectionIdsVO;

    @Valid
    @NotNull(message = "templates.schedule.not_null")
    @ValidDetectorTemplateSchedule(message = "templates.schedule.invalid")
    @JsonProperty("schedule")
    private DetectorTemplateScheduleVO scheduleVO;
}
