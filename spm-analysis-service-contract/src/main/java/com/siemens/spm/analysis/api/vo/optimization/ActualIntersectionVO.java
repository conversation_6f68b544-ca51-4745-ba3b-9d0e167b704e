package com.siemens.spm.analysis.api.vo.optimization;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * This class used for serialization to persist data into DB. Be careful when have any change in this class(field,
 * annotation, ..). This can break when deserialize data fetch from DB
 *
 * <AUTHOR> Nguyen - <EMAIL>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ActualIntersectionVO implements Serializable {

    private static final long serialVersionUID = -275483283576632455L;

    @JsonProperty("int_id")
    private String intId;

    @JsonProperty("int_name")
    private String intName;

    @JsonProperty("is_disabled")
    private Boolean isDisabled = false;

    @JsonProperty("distance")
    private Double distance;

    @JsonProperty("estimated_time")
    private long estimatedTime;

    @JsonProperty("ring1")
    private ActualRingDataVO ring1;

    @JsonProperty("ring2")
    private ActualRingDataVO ring2;

    @JsonProperty("upstream_phase")
    private Integer upstreamPhase;

    @JsonProperty("downstream_phase")
    private Integer downstreamPhase;

    /**
     * Total aog hit of coordinated phase of 2 rings of intersection
     */
    @JsonProperty("aog_hit")
    private Long aogHit;

    /**
     * Total det hit of coordinated phase of 2 rings of intersection
     */
    @JsonProperty("det_hit")
    private Long detHit;

    @JsonProperty("aog_percent")
    public Double getAogPercent() {
        if (aogHit == null || detHit == null || detHit == 0) {
            return null;
        }
        return aogHit * 100.0 / detHit;
    }

    @JsonProperty("plans")
    private List<ActualPlanBlockVO> plans;
}
