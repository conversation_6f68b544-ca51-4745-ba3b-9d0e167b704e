/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AlarmRuleSearchRequestVO.java
 * Project     : SPM Platform
 */

package com.siemens.spm.rule.api.vo.request;

import java.io.Serializable;
import java.sql.Timestamp;
import java.time.DayOfWeek;
import java.time.LocalTime;
import java.util.Set;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class AlarmRuleSearchRequestVO implements Serializable {

    private static final long serialVersionUID = 1552658737950377627L;

    @JsonProperty("agency_id")
    private Integer agencyId;

    @JsonProperty("analysis_id")
    private String analysisId;

    @JsonProperty("category_id")
    private Long categoryId;

    @JsonProperty("week_days")
    private Set<DayOfWeek> weekDays;

    @JsonProperty("from_time")
    private LocalTime fromTime;

    @JsonProperty("to_time")
    private LocalTime toTime;

    @JsonProperty("created_at_from")
    private Timestamp createdAtFrom;

    @JsonProperty("created_at_to")
    private Timestamp createdAtTo;

    @JsonProperty("order_by_columns")
    private String[] orderByColumns;

    @JsonProperty("owner_id")
    private Long ownerId;

    @JsonProperty("page")
    private Integer page;

    @JsonProperty("size")
    private Integer size;

    @JsonProperty("status")
    private String status;

    @JsonProperty("text")
    private String text;

}
