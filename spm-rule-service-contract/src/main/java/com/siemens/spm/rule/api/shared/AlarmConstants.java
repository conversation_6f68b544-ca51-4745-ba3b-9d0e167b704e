/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AlarmConstants.java
 * Project     : SPM Platform
 */

package com.siemens.spm.rule.api.shared;

import com.siemens.spm.common.shared.resource.UUIDConstants;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

/**
 * Define all constants in Alarm Service.
 */
public final class AlarmConstants {

    private AlarmConstants() {
    }

    public static final int RULE_NAME_MAX_LENGTH = 100;

    public static final int RULE_DESCRIPTION_MAX_LENGTH = 500;

    // at present, support maximum 20 intersections per rule only
    // 36 characters/intersection id -> 20 intersections = 720 characters
    // 30 characters for commas, ...
    public static final int RULE_INTERSECTION_LIST_MAX_LENGTH = 750;

    public static final int RULE_TIME_RANGE_MAX_LENGTH = 300;

    public static final int RECORD_NAME_MAX_LENGTH = RULE_NAME_MAX_LENGTH;

    public static final int RECORD_DESCRIPTION_MAX_LENGTH = RULE_DESCRIPTION_MAX_LENGTH;

    public static final int TRAFFIC_METRIC_MAX_LENGTH = 60;

    public static final int TRAFFIC_METRIC_UNIT_MAX_LENGTH = 50;

    public static final String ALL_INTERSECTIONS_INDICATOR = "*";

    private static final String COMMA = ",";

    public static final String INTERSECTION_SEPARATOR = COMMA;

    public static final String SUBSCRIBER_SEPARATOR = COMMA;

    public static final String REGEXP_FOR_1_INTERSECTION_ID = UUIDConstants.REGEXP_FOR_1_UUID_V4;
    private static final String REGEXP_FOR_SPECIFIC_INTERSECTIONS = "^" + REGEXP_FOR_1_INTERSECTION_ID + "(" + INTERSECTION_SEPARATOR + REGEXP_FOR_1_INTERSECTION_ID + ")*$";
    private static final String REGEXP_FOR_ALL_INTERSECTIONS = "^[" + ALL_INTERSECTIONS_INDICATOR + "]$";

    public static final String REGEXP_FOR_RULE_INTERSECTION_LIST = REGEXP_FOR_SPECIFIC_INTERSECTIONS + "|" + REGEXP_FOR_ALL_INTERSECTIONS;

    public static final String REGEXP_FOR_EMPTY_STRING = "^$";
    // subscriber list can be empty
    public static final String REGEXP_FOR_RULE_SUBSCRIBER_LIST = "^[0-9]+(" + SUBSCRIBER_SEPARATOR + "[0-9]+)*$" + "|" + REGEXP_FOR_EMPTY_STRING;

    // default rule chunk size
    public static final int DEFAULT_RULE_EVALUATION_JOB_CHUNK_SIZE = 10;

    // default maximum number of alarm rule evaluation job redos
    public static final long DEFAULT_MAX_NUMBER_OF_RULE_EVALUTION_JOB_REDOS = 2;

    // default minimum time (in seconds) between two consecutive rule evaluation job redos
    public static final long DEFAULT_MIN_TIME_BETWEEN_RULE_EVALUATION_JOB_REDO = 900;

    public static final int DEFAULT_HOUR_OF_DAY_TO_SEND_ALARM_RECORD_SUMMARY_EMAIL = 17;

    @AllArgsConstructor
    public enum RuleStatus {
        ACTIVE("ACTIVE", "is active"),
        INACTIVE("INACTIVE", "is inactive");

        @Setter
        @Getter
        private String value;

        @Setter
        @Getter
        private String description;

    }

    @Getter
    @AllArgsConstructor
    public enum RuleScopeIntersection {
        ALL_INTERSECTIONS("ALL_INTERSECTIONS",
                "Alarm rule's scope is for all intersections. It will be triggered by all intersections"),
        SPECIFIC_INTERSECTIONS("SPECIFIC_INTERSECTIONS",
                "Alarm rule's scope is for specific intersections. It will be triggered by specific intersections");

        private final String value;

        private final String description;
    }

    public enum ActionOnRule {
        CREATE,
        ACTIVATE,
        DEACTIVATE,
        UPDATE,
        SUBSCRIBE_NO_EMAIL,
        SUBSCRIBE_WITH_EMAIL,
        UNSUBSCRIBE,
        DELETE,
    }

    @AllArgsConstructor
    public enum RequestAction {
        ADD("ADD", "add"),
        REMOVE("REMOVE", "remove"),
        SUBSCRIBE("SUBSCRIBE", "subscribe"),
        SUBSCRIBE_NO_EMAIL("SUBSCRIBE_NO_EMAIL", "subscribe with no email"),
        SUBSCRIBE_WITH_EMAIL("SUBSCRIBE_WITH_EMAIL", "subscribe and receive email"),
        UNSUBSCRIBE("UNSUBSCRIBE", "unsubscribe");

        @Setter
        @Getter
        private String value;

        @Setter
        @Getter
        private String description;
    }

}
