/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AlarmRuleSearchSubscribersRequestVO.java
 * Project     : SPM Platform
 */

package com.siemens.spm.rule.api.vo.request;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class SubscriberSearchRequestVO implements Serializable {

    private static final long serialVersionUID = -5706277124127618703L;

    @JsonProperty("agency_id")
    private Integer agencyId;

    @JsonProperty("order_by_columns")
    private String[] orderByColumns;

    @JsonProperty("rule_id")
    private Long ruleId;

    @JsonProperty("exclude_ids")
    private Long[] excludeIds;

    @JsonProperty("page")
    private Integer page;

    @JsonProperty("size")
    private Integer size;

    @JsonProperty("status")
    private String status;

    @JsonProperty("text")
    private String text;

}
