/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AlarmRecordDetailVO.java
 * Project     : SPM Platform
 */

package com.siemens.spm.rule.api.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import groovy.transform.EqualsAndHashCode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
public class AlarmRecordDetailVO extends AlarmRecordCoreDataVO {

    private static final long serialVersionUID = 7767316388941166581L;

    @JsonProperty("agency_id")
    private Integer agencyId;

    @JsonProperty("agency_name")
    private String agencyName;

    @JsonProperty("analysis_name")
    private String analysisName;

    @JsonProperty("category_name")
    private String categoryName;

    @JsonProperty("intersection_name")
    private String intersectionName;

    @JsonProperty("rule_condition")
    private String ruleCondition;

}
