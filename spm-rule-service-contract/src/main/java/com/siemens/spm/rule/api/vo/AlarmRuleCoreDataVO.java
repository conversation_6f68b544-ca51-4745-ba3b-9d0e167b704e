/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AlarmRuleCoreDataVO.java
 * Project     : SPM Platform
 */

package com.siemens.spm.rule.api.vo;

import java.io.Serializable;
import java.sql.Timestamp;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.shared.domaintype.alarm.timerange.TimeRange;
import com.siemens.spm.common.shared.vo.AlarmCategoryVO;
import com.siemens.spm.common.shared.vo.AnalysisTypeVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class AlarmRuleCoreDataVO implements Serializable {

    private static final long serialVersionUID = -4463040331191430913L;

    @JsonProperty("id")
    private Long id;

    @JsonProperty("name")
    private String name;

    @JsonProperty("status")
    private String status;

    @JsonProperty("agency_id")
    private Integer agencyId;

    @JsonProperty("created_at")
    private Timestamp createdAt;

    @JsonProperty("analysis")
    private AnalysisTypeVO analysis;

    @JsonProperty("category")
    private AlarmCategoryVO category;

    @JsonProperty("owner")
    private RuleOwnerVO owner;

    @JsonProperty("owner_id")
    private Long ownerId;

    @JsonProperty("time_range")
    private TimeRange timeRange;

}
