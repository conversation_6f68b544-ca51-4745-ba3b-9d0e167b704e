/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AlarmRecordSearchVO.java
 * Project     : SPM Platform
 */

package com.siemens.spm.rule.api.vo;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.util.DateTimeUtils;
import groovy.transform.EqualsAndHashCode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
public class AlarmRecordSearchVO extends AlarmRecordCoreDataVO {

    private static final long serialVersionUID = 7767316388941166581L;

    @JsonProperty("analysis_name")
    private String analysisName;

    @JsonProperty("category_name")
    private String categoryName;

    @JsonProperty("intersection_name")
    private String intersectionName;

    @JsonProperty("from_time")
    @JsonFormat(pattern = DateTimeUtils.ISO_8601_LOCAL_DATE_TIME)
    private LocalDateTime fromTime;

}
