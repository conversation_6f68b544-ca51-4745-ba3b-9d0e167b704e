/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AnalysisTypeListResponseVO.java
 * Project     : SPM Platform
 */

package com.siemens.spm.rule.api.vo.response;

import java.io.Serializable;
import java.util.List;

import org.springframework.http.HttpStatus;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.siemens.spm.common.shared.vo.AbstractResultObject;
import com.siemens.spm.common.shared.vo.AnalysisTypeVO;
import com.siemens.spm.rule.api.vo.response.AnalysisTypeListResponseVO.ResponseData;
import com.siemens.spm.rule.api.vo.response.AnalysisTypeListResponseVO.StatusCode;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class AnalysisTypeListResponseVO extends AbstractResultObject<ResponseData, StatusCode> {

    private static final long serialVersionUID = -4063069562882700501L;

    private ResponseData data;

    private StatusCode statusCode;

    /**
     * Constructor for SUCCESS case
     *
     * @param data
     */
    public AnalysisTypeListResponseVO(ResponseData data) {
        super(data);
    }

    @Override
    public ResponseData getData() {
        return data;
    }

    @Override
    public String getMessage() {
        return statusCode.getMessage();
    }

    /**
     * Constructor to set status to other than success
     *
     * @param data
     * @param statusCode
     */
    public AnalysisTypeListResponseVO(ResponseData data, StatusCode statusCode) {
        super(data, statusCode);
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }

    @Override
    public StatusCode getStatusCode() {
        return statusCode;
    }

    @Override
    protected void setData(ResponseData value) {
        this.data = value;
    }

    @Override
    public String getErrorFieldName() {
        return statusCode.getErrorField();
    }

    @Override
    protected void setStatusCode(StatusCode value) {
        this.statusCode = value;
    }


    @Override
    protected StatusCode getErrorStatusValue() {
        return StatusCode.ERROR;
    }

    @Getter
    @AllArgsConstructor
    public enum StatusCode {

        ERROR("unknown_exception", HttpStatus.INTERNAL_SERVER_ERROR),
        SUCCESS("success", HttpStatus.OK),
        NO_DATA("no_data", HttpStatus.NO_CONTENT);

        private final String errorField;
        private final String message;
        private final HttpStatus httpStatus;

        StatusCode(String message, HttpStatus httpStatus) {
            this(null, message, httpStatus);
        }
    }
    @Override
    public StatusCode getSuccessfulStatusValue() {
        return StatusCode.SUCCESS;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(Include.NON_NULL)
    public static class ResponseData implements Serializable {

        private static final long serialVersionUID = 7698468888713681432L;

        @JsonProperty("total_count")
        private Long totalCount;

        @JsonProperty("analyses")
        private List<AnalysisTypeVO> analyses;

    }

}
