package com.siemens.spm.rule.api.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class AlarmRuleSearchVO extends AlarmRuleCoreDataVO {

    private static final long serialVersionUID = -3188747393477399217L;

    @JsonProperty("no_triggered_alarms")
    private Long noTriggeredAlarms;

    @JsonProperty("is_subscribed")
    private Boolean isSubscribed;

    @JsonProperty("email_receive")
    private Boolean emailReceive;

}
