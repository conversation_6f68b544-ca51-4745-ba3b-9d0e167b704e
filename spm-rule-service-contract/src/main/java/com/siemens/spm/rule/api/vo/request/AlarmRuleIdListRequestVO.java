/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AlarmRuleIdListVO.java
 * Project     : SPM Platform
 */

package com.siemens.spm.rule.api.vo.request;

import java.io.Serializable;
import java.util.List;

import jakarta.validation.constraints.NotEmpty;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
public class AlarmRuleIdListRequestVO implements Serializable {

    private static final long serialVersionUID = 6394998750795611009L;

    @NotEmpty(message = "alarm_rule.rule_ids_not_empty")
    @JsonProperty("rule_ids")
    private List<Long> ruleIds;
}
