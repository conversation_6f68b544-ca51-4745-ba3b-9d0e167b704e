package com.siemens.spm.datahub.api.vo;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.time.QueryTime;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public final class DataHubTspQuery {

    @JsonProperty("intersection_id")
    private String intersectionId;

    @JsonProperty("query_times")
    private List<QueryTime> queryTimes;

}
