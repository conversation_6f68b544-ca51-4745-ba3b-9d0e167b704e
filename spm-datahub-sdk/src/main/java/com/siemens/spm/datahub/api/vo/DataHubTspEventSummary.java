package com.siemens.spm.datahub.api.vo;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DataHubTspEventSummary implements Serializable {

    private static final long serialVersionUID = 1873086524719523402L;

    @JsonProperty("intersection_uuid")
    private String intersectionUUID;

    @JsonProperty("check_in")
    private Integer checkIn;

    @JsonProperty("check_out")
    private Integer checkOut;

    @JsonProperty("adjust_early")
    private Integer adjustEarly;

    @JsonProperty("adjust_late")
    private Integer adjustLate;

    @JsonProperty("priority_request_enable")
    private Integer priorityRequestEnable;

    @JsonProperty("priority_request_cancel")
    private Integer priorityRequestCancel;

    @JsonProperty("last_check_in")
    private LocalDateTime lastCheckIn;

    @JsonProperty("last_check_out")
    private LocalDateTime lastCheckOut;

    @JsonIgnore
    public boolean isValid() {
        return intersectionUUID != null;
    }

}
