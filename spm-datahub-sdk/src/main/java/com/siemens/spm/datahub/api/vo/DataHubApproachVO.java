package com.siemens.spm.datahub.api.vo;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DataHubApproachVO implements Serializable {

    private static final long serialVersionUID = -7178937740207169421L;

    @JsonProperty("Direction")
    private String direction;

    @JsonProperty("ApproachSpeed")
    private Double approachSpeed;

    @JsonProperty("UpstreamEntityID")
    private String upstreamEntityId;

    @JsonProperty("UpstreamDistance")
    private Double upstreamDistance;

    @JsonProperty("DownstreamEntityID")
    private String downstreamEntityId;

    @JsonProperty("DownstreamDistance")
    private Double downstreamDistance;

    @JsonProperty("Phases")
    private List<DataHubPhaseVO> phases;

}
