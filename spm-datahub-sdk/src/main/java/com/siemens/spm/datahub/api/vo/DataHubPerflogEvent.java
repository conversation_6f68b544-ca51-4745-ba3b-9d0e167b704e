package com.siemens.spm.datahub.api.vo;

import java.sql.Timestamp;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class DataHubPerflogEvent {

    @JsonProperty("parameter")
    private Integer parameter;

    @JsonProperty("datetime")
    private Timestamp dateTime;

    @JsonProperty("event")
    private Integer event;

    @JsonIgnore
    public boolean isValid() {
        return parameter != null && dateTime != null && event != null;
    }

}
