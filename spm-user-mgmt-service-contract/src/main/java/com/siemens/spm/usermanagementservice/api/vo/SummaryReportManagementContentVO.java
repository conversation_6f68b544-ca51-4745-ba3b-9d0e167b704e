package com.siemens.spm.usermanagementservice.api.vo;

import java.util.ArrayList;
import java.util.List;

import org.springframework.util.StringUtils;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.message.MessageService;
import com.siemens.spm.common.shared.domaintype.notification.DynamicContentType;
import com.siemens.spm.common.shared.domaintype.notification.NotificationContentVO;
import com.siemens.spm.common.shared.vo.DynamicContentVO;
import com.siemens.spm.common.util.BeanFinder;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SummaryReportManagementContentVO implements NotificationContentVO {

    private static final long serialVersionUID = 3983372132662474365L;

    @JsonProperty("template_name")
    private String templateName;

    @JsonProperty("template_description")
    private String templateDescription;

    @Override
    @JsonIgnore
    public List<DynamicContentVO> getDynamicContents() {
        List<DynamicContentVO> labelValueItems = new ArrayList<>();

        MessageService messageService = BeanFinder.getDefaultMessageService();

        if (StringUtils.hasText(templateName)) {
            DynamicContentVO templateNameContent = DynamicContentVO.builder()
                    .type(DynamicContentType.TEXT)
                    .label(messageService.getMessage("summary-report.templates.name"))
                    .values(templateName)
                    .build();
            labelValueItems.add(templateNameContent);
        }

        if (StringUtils.hasText(templateDescription)) {
            DynamicContentVO templateDescContent = DynamicContentVO.builder()
                    .type(DynamicContentType.TEXT)
                    .label(messageService.getMessage("summary-report.templates.description"))
                    .values(templateDescription)
                    .build();
            labelValueItems.add(templateDescContent);
        }

        return labelValueItems;
    }

}
