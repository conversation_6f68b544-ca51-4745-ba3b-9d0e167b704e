/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AccountUpdatedRequestVO.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.api.vo.request;

import java.io.Serializable;

import org.springframework.web.multipart.MultipartFile;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class AccountUpdatedRequestVO implements Serializable {

    private static final long serialVersionUID = -8579966700859037078L;

    @SuppressWarnings("java:S1948")
    private transient MultipartFile avatar;

}
