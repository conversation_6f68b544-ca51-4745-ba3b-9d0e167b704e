package com.siemens.spm.usermanagementservice.api.vo;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.shared.domaintype.alarm.condition.Operator;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * This class used for serialization to persist data into DB. Be careful when have any change in this class(field,
 * annotation, ..). This can break when deserialize data fetch from DB
 *
 * <AUTHOR> Ng<PERSON>en - <EMAIL>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WarningConditionVO implements Serializable {

    private static final long serialVersionUID = -401283691566900104L;

    @JsonProperty("operator")
    private Operator operator;

    @JsonProperty("value")
    private Double value;

}
