/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : CorridorIntersectionCreateRequestVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.api.vo.request;

import java.io.Serializable;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.usermanagementservice.api.vo.constraint.ValidStreamNumber;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
public class CorridorIntersectionCreateRequestVO implements Serializable {

    private static final long serialVersionUID = -4685095693251619424L;

    @NotNull(message = "number_required")
    @JsonProperty("number")
    private Integer number;

    @NotNull(message = "distance_required")
    @JsonProperty("distance")
    private Double distance;

    @JsonProperty("speed")
    @NotNull(message = "corridor_intersection_speed_required")
    private Double speed;

    @ValidStreamNumber(message = "upstream_is_between_1_and_16")
    @NotNull(message = "corridor_intersection_upstream_required")
    @JsonProperty("upstream")
    private Integer upstream;

    @ValidStreamNumber(message = "downstream_is_between_1_and_16")
    @NotNull(message = "corridor_intersection_downstream_required")
    @JsonProperty("downstream")
    private Integer downstream;

    @NotBlank(message = "intersection_id_required")
    @JsonProperty("intersection_id")
    private String intersectionId;
}
