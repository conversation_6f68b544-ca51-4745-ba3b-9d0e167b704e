package com.siemens.spm.usermanagementservice.api.vo.response;

import org.springframework.http.HttpStatus;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.siemens.spm.common.shared.vo.AbstractResultObject;
import com.siemens.spm.usermanagementservice.api.vo.DashboardSettingVO;
import com.siemens.spm.usermanagementservice.api.vo.response.WidgetsSettingUpdatedResultObject.WidgetSettingStatusCode;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@EqualsAndHashCode
public class WidgetsSettingUpdatedResultObject
        extends AbstractResultObject<DashboardSettingVO, WidgetSettingStatusCode> {

    private static final long serialVersionUID = 4181264476204525466L;

    private DashboardSettingVO data;

    private WidgetSettingStatusCode statusCode;

    public WidgetsSettingUpdatedResultObject(WidgetSettingStatusCode statusCode) {
        this.statusCode = statusCode;
    }

    public WidgetsSettingUpdatedResultObject(DashboardSettingVO data) {
        this.data = data;
        this.statusCode = WidgetSettingStatusCode.SUCCESS;
    }

    @Override
    public String getMessage() {
        return statusCode.getMessage();
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }

    @Override
    public String getErrorFieldName() {
        return null;
    }

    @Override
    protected WidgetSettingStatusCode getErrorStatusValue() {
        return WidgetSettingStatusCode.UNKNOWN_ERROR;
    }

    @Override
    public WidgetSettingStatusCode getSuccessfulStatusValue() {
        return WidgetSettingStatusCode.SUCCESS;
    }

    @Getter
    @AllArgsConstructor
    public enum WidgetSettingStatusCode {
        /** OK */
        SUCCESS("success", HttpStatus.OK),

        /** Bad Request */
        INVALID_AGENCY_ID(WidgetSettingField.AGENCY_ID, "agency_id_invalid", HttpStatus.BAD_REQUEST),
        INVALID_WIDGET(WidgetSettingField.WIDGET, "user.setting.dashboard.widget.invalid", HttpStatus.BAD_REQUEST),
        INVALID_MAPVIEW(WidgetSettingField.WIDGET, "user.setting.dashboard.mapview.invalid", HttpStatus.BAD_REQUEST),
        INVALID_OPEN_ALARM(WidgetSettingField.WIDGET, "user.setting.dashboard.open_alarm.invalid",
                HttpStatus.BAD_REQUEST),
        INVALID_TOP_INT_OPEN_ALARM(WidgetSettingField.WIDGET, "user.setting.dashboard.top_int_open_alarm.invalid",
                HttpStatus.BAD_REQUEST),
        INVALID_SUMMARY_REPORT(WidgetSettingField.WIDGET, "user.setting.dashboard.summary_report.invalid",
                HttpStatus.BAD_REQUEST),
        INVALID_VOLUME_REPORT(WidgetSettingField.WIDGET, "user.setting.dashboard.volume_report.invalid",
                HttpStatus.BAD_REQUEST),
        INVALID_NOTIFICATION(WidgetSettingField.WIDGET, "user.setting.dashboard.notification.invalid",
                HttpStatus.BAD_REQUEST),
        INTERSECTION_NOT_UNDER_AGENCY(WidgetSettingField.INTERSECTION_IDS, "intersection_not_under_agency_management",
                HttpStatus.BAD_REQUEST),

        /** Internal Server Error */
        UNKNOWN_ERROR("unknown_exception", HttpStatus.INTERNAL_SERVER_ERROR);

        private final String errorField;
        private final String message;
        private final HttpStatus httpStatus;

        WidgetSettingStatusCode(String message, HttpStatus httpStatus) {
            this(null, message, httpStatus);
        }
    }

    public static class WidgetSettingField {
        private WidgetSettingField() {
        }

        public static final String WIDGET = "widget";

        public static final String AGENCY_ID = "agency_id";

        public static final String INTERSECTION_IDS = "intersection_ids";
    }
}
