/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : IntersectionAvailableSearchRequestVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.api.vo.request;

import java.io.Serializable;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.shared.resource.UUIDConstants;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class IntersectionAvailableSearchRequestVO implements Serializable {

    private static final long serialVersionUID = 279004952158271370L;

    @JsonProperty("agency_id")
    @NotNull(message = "agency_id_not_null")
    private Integer agencyId;

    @JsonProperty("corridor_id")
    @Pattern(regexp = UUIDConstants.REGEXP_FOR_1_CORRIDOR_ID, message = "corridor_id_invalid")
    private String corridorId;

    @JsonProperty("text")
    private String text;

    @JsonProperty("order_by_columns")
    private String[] orderByColumns;

    @JsonProperty("page")
    private Integer page;

    @JsonProperty("size")
    private Integer size;

}
