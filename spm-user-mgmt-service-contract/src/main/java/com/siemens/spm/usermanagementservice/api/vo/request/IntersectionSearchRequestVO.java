/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : IntersectionSearchRequestVO.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.api.vo.request;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class IntersectionSearchRequestVO implements Serializable {

    private static final long serialVersionUID = 5721610510273498272L;

    @JsonProperty("agency_id")
    private Integer agencyId;

    @JsonProperty("exclusionary_ids")
    private List<String> exclusionaryIds;

    @JsonProperty("intersection_ids")
    private List<String> intersectionIds;

    @JsonProperty("status")
    private String status;

    @JsonProperty("text")
    private String text;

    @JsonProperty("top_left")
    private Double[] topLeft;

    @JsonProperty("bottom_right")
    private Double[] bottomRight;

    @JsonProperty("order_by_columns")
    private String[] orderByColumns;

    @JsonProperty("should_paginate")
    private Boolean shouldPaginate;

    @JsonProperty("page")
    private Integer page;

    @JsonProperty("size")
    private Integer size;

}
