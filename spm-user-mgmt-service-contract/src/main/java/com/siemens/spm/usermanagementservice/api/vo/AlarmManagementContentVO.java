/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AlarmManagementContentVO.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.api.vo;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.message.MessageService;
import com.siemens.spm.common.shared.domaintype.notification.DynamicContentType;
import com.siemens.spm.common.shared.domaintype.notification.NotificationContentVO;
import com.siemens.spm.common.shared.vo.DynamicContentVO;
import com.siemens.spm.common.util.BeanFinder;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class AlarmManagementContentVO implements NotificationContentVO {

    private static final long serialVersionUID = -2699051900047835108L;

    @JsonProperty("rule_name")
    private String ruleName;

    @JsonProperty("rule_description")
    private String ruleDescription;

    @Override
    @JsonIgnore
    public List<DynamicContentVO> getDynamicContents() {
        List<DynamicContentVO> dynamicContents = new ArrayList<>();

        MessageService messageService = BeanFinder.getDefaultMessageService();

        DynamicContentVO ruleNameContent = DynamicContentVO.builder()
                .type(DynamicContentType.TEXT)
                .label(messageService.getMessage("alarm.rule.name"))
                .values(ruleName)
                .build();
        dynamicContents.add(ruleNameContent);

        DynamicContentVO ruleDescContent = DynamicContentVO.builder()
                .type(DynamicContentType.TEXT)
                .label(messageService.getMessage("alarm.rule.description"))
                .values(ruleDescription)
                .build();

        dynamicContents.add(ruleDescContent);

        return dynamicContents;
    }

}
