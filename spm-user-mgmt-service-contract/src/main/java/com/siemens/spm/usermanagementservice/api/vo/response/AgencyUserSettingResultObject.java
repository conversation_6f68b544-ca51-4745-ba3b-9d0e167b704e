package com.siemens.spm.usermanagementservice.api.vo.response;

import org.springframework.http.HttpStatus;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.siemens.spm.common.shared.vo.AbstractResultObject;
import com.siemens.spm.usermanagementservice.api.vo.AgencyUserSettingVO;
import com.siemens.spm.usermanagementservice.api.vo.response.AgencyUserSettingResultObject.AgencyUserSettingStatusCode;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AgencyUserSettingResultObject
        extends AbstractResultObject<AgencyUserSettingVO, AgencyUserSettingStatusCode> {

    private static final long serialVersionUID = 4833301506506613740L;

    private AgencyUserSettingVO data;

    private AgencyUserSettingStatusCode statusCode;

    public AgencyUserSettingResultObject(AgencyUserSettingStatusCode statusCode) {
        this.statusCode = statusCode;
    }

    public AgencyUserSettingResultObject(AgencyUserSettingVO data) {
        this.data = data;
        this.statusCode = AgencyUserSettingStatusCode.SUCCESS;
    }

    @Override
    public String getErrorFieldName() {
        return statusCode.getErrorField();
    }

    @Override
    protected AgencyUserSettingStatusCode getErrorStatusValue() {
        return AgencyUserSettingStatusCode.UNKNOWN_ERROR;
    }

    @Override
    public AgencyUserSettingStatusCode getSuccessfulStatusValue() {
        return AgencyUserSettingStatusCode.SUCCESS;
    }

    @Getter
    @AllArgsConstructor
    public enum AgencyUserSettingStatusCode {
        /**
         * OK
         */
        SUCCESS("success", HttpStatus.OK),
        NO_CONTENT("success", HttpStatus.NO_CONTENT),

        /**
         * Bad request
         */
        INVALID_AGENCY_ID("agency_id", "agency_id_invalid", HttpStatus.BAD_REQUEST),

        USER_NOT_FOUND("user_not_found", HttpStatus.UNAUTHORIZED),

        /**
         * Internal Server Error
         */
        UNKNOWN_ERROR("unknown_exception", HttpStatus.INTERNAL_SERVER_ERROR);

        private final String errorField;
        private final String message;
        private final HttpStatus httpStatus;

        AgencyUserSettingStatusCode(String message, HttpStatus httpStatus) {
            this(null, message, httpStatus);
        }
    }

    @Override
    public String getMessage() {
        return statusCode.getMessage();
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }
}
