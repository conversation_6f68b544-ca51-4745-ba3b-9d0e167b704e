package com.siemens.spm.usermanagementservice.api.vo;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class WidgetSettingUpdatedVO implements Serializable {

    private static final long serialVersionUID = 1742504200444252585L;

    @JsonProperty("target")
    private String target;

    @JsonProperty("setting")
    private AbstractWidgetVO setting;

}
