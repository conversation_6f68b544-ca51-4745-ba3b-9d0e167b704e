/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : CorridorService.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.api.boundary;

import com.siemens.spm.usermanagementservice.api.vo.request.CorridorCreateRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.CorridorListRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.CorridorSearchRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.CorridorStatusUpdateRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.CorridorUpdateRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.request.IntersectionAvailableSearchRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.CorridorInternalSearchResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.CorridorListResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.CorridorResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionInternalSearchResultObject;

/**
 * Corridor Management Service Interface
 */
public interface CorridorService {

    /**
     * Create a new corridor
     *
     * @param corridorVO
     * @return CorridorResultObject
     */
    CorridorResultObject createCorridor(CorridorCreateRequestVO corridorVO);

    /**
     * Update an existing corridor
     *
     * @param corridorVO
     * @return CorridorResultObject
     */
    CorridorResultObject updateCorridor(String corridorId, CorridorUpdateRequestVO corridorVO);

    /**
     * Get a corridor detail
     *
     * @param corridorId
     * @return CorridorResultObject
     */
    CorridorResultObject getCorridorDetail(String corridorId);

    /**
     * Get corridors
     *
     * @param corridorListRequestVO
     * @return CorridorListResultObject
     */
    CorridorListResultObject getCorridors(CorridorListRequestVO corridorListRequestVO);

    /**
     * Update corridor status
     *
     * @param requestVO
     * @return CorridorResultObject
     */
    CorridorResultObject updateCorridorStatus(CorridorStatusUpdateRequestVO requestVO);

    /**
     * Search available intersections
     *
     * @param requestVO
     * @return IntersectionInternalSearchResultObject
     */
    IntersectionInternalSearchResultObject searchAvailableIntersections(IntersectionAvailableSearchRequestVO requestVO);

    /**
     * Get all corridors by filter for other service(s)
     *
     * @param searchRequest criteria for searching
     * @return CorridorInternalSearchResultObject
     */
    CorridorInternalSearchResultObject getAllCorridorsByFilterInternal(CorridorSearchRequestVO searchRequest);

}
