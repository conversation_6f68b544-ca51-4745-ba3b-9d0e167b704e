// package com.siemens.spm.usermanagementservice.api.controller;

// import org.springframework.http.HttpStatus;
// import org.springframework.http.ResponseEntity;
// import org.springframework.web.bind.annotation.GetMapping;
// import org.springframework.web.bind.annotation.RequestMapping;
// import org.springframework.web.bind.annotation.RequestParam;
// import org.springframework.web.bind.annotation.ResponseStatus;

// import com.siemens.spm.common.api.PublicController;
// import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionListForTspDashboardMapResultObject;
// import io.swagger.v3.oas.annotations.Operation;
// import io.swagger.v3.oas.annotations.Parameter;
// import io.swagger.v3.oas.annotations.enums.ParameterIn;
// import io.swagger.v3.oas.annotations.responses.ApiResponse;
// import io.swagger.v3.oas.annotations.responses.ApiResponses;
// import io.swagger.v3.oas.annotations.tags.Tag;

// /**
//  * <AUTHOR> Mai
//  */
// @RequestMapping(TspDashboardController.URL_ROOT)
// @Tag(name = "tsp-dashboard", description = "Tsp Dashboard API")
// public interface TspDashboardController extends PublicController {

//     String VERSION = "/v1";
//     String TSP_DASHBOARD_RESOURCE = "/tsp-dashboard";
//     String MAP_INTERSECTIONS_RESOURCE = "/map/intersections";
//     String URL_ROOT = PUBLIC_API + VERSION + TSP_DASHBOARD_RESOURCE;

//     /**
//      * Get intersections for map of tsp dashboard GET :
//      * /tsp-dashboard/map/intersections?agency={agencyID}&top_left={latitude,longitude}
//      * &bottom_right={latitude,longitude}&sort={firstColumn,secondColumn}&page={page}&size={size}
//      *
//      * @return ResponseEntity<IntersectionListForTspDashboardMapResultObject>
//      */
//     @Operation(summary = "Get intersection for map of tsp dashboard")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request"),
//             @ApiResponse(responseCode = "404", description = "Not Found") })
//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true,
//             in = ParameterIn.HEADER, description = "Bearer Token type")
//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping(MAP_INTERSECTIONS_RESOURCE)
//     ResponseEntity<IntersectionListForTspDashboardMapResultObject> getIntersectionsForTspMap(
//             @Parameter(name = "agency_id", description = "Agency ID")
//             @RequestParam(value = "agency_id")
//             Integer agencyId,

//             @Parameter(name = "top_left",
//                     description = "The top left location [latitude, longitude] of geographical area where to retrieve intersection information.")
//             @RequestParam(value = "top_left")
//             Double[] topLeft,

//             @Parameter(name = "bottom_right",
//                     description = "The bottom right location [latitude, longitude] of geographical area where to retrieve intersection information.")
//             @RequestParam(value = "bottom_right")
//             Double[] bottomRight,

//             @Parameter(name = "sort", description = "Sortable columns are: {latitude, longitude}. Default order is ascending.")
//             @RequestParam(value = "sort", required = false)
//             String[] orderByColumns,

//             @Parameter(name = "page", description = "Page number. If page is empty, it will be 0 (default).")
//             @RequestParam(value = "page", required = false)
//             Integer page,

//             @Parameter(name = "size", description = "Size of page. If size is empty, it will be 20 (default)")
//             @RequestParam(value = "size", required = false)
//             Integer size
//     );

// }
