package com.siemens.spm.usermanagementservice.api.vo.response;

import org.springframework.http.HttpStatus;

import com.siemens.spm.common.shared.vo.AbstractResultObject;
import com.siemens.spm.usermanagementservice.api.vo.TacticIntegrationVO;
import com.siemens.spm.usermanagementservice.api.vo.response.TacticIntegrationResultObject.TacticIntegrationStatusCode;

import lombok.AllArgsConstructor;
import lombok.Getter;

public class TacticIntegrationResultObject
        extends AbstractResultObject<TacticIntegrationVO, TacticIntegrationStatusCode> {

    private static final long serialVersionUID = 3152346505062896129L;

    private TacticIntegrationVO data;

    private TacticIntegrationStatusCode statusCode;

    public TacticIntegrationResultObject() {
        super();
    }

    public TacticIntegrationResultObject(TacticIntegrationVO data, TacticIntegrationStatusCode statusCode) {
        super();
        this.data = data;
        this.statusCode = statusCode;
    }

    @Override
    public TacticIntegrationVO getData() {
        return data;
    }

    @Override
    public String getMessage() {
        return statusCode.getMessage();
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }

    @Override
    public TacticIntegrationStatusCode getStatusCode() {
        return statusCode;
    }

    @Override
    protected void setData(TacticIntegrationVO value) {
        this.data = value;
    }

    @Override
    public String getErrorFieldName() {
        return statusCode.getErrorField();
    }

    @Override
    protected void setStatusCode(TacticIntegrationStatusCode value) {
        this.statusCode = value;
    }

    @Override
    protected TacticIntegrationStatusCode getErrorStatusValue() {
        return TacticIntegrationStatusCode.ERROR;
    }

    @Override
    public TacticIntegrationStatusCode getSuccessfulStatusValue() {
        return TacticIntegrationStatusCode.SUCCESS;
    }

    @AllArgsConstructor
    @Getter
    public enum TacticIntegrationStatusCode {
        /** OK */
        SUCCESS("success", HttpStatus.OK),

        /** Internal Server Error */
        ERROR("unknown_exception", HttpStatus.INTERNAL_SERVER_ERROR),

        /** Others */
        INVALID_API_KEY("invalid_api_key", "invalid_api_key", HttpStatus.BAD_REQUEST),
        AGENCY_NOT_FOUND("agency_not_found", "agency_not_found", HttpStatus.BAD_REQUEST),
        USER_DID_NOT_HAVE_ACCESS_TO_AGENCY("user_did_not_have_access_to_the_given_agency",
                "user_did_not_have_access_to_the_given_agency", HttpStatus.BAD_REQUEST);

        private final String errorField;
        private final String message;
        private final HttpStatus httpStatus;

        TacticIntegrationStatusCode(String message, HttpStatus httpStatus) {
            this(null, message, httpStatus);
        }
    }
}
