/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : UserKeyResultObject.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.api.vo.response;

import org.springframework.http.HttpStatus;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.siemens.spm.common.shared.vo.AbstractResultObject;
import com.siemens.spm.usermanagementservice.api.vo.UserKeyVO;

import lombok.AllArgsConstructor;
import lombok.Getter;

@JsonInclude(Include.NON_NULL)
public class UserKeyResultObject extends
        AbstractResultObject<UserKeyVO, UserKeyResultObject.UserKeyStatusCode> {

    private static final long serialVersionUID = -9205084441657959286L;

    private UserKeyVO data;

    private UserKeyStatusCode statusCode;

    /**
     * Constructor for ERROR case
     */
    public UserKeyResultObject() {

    }

    @Override
    protected void setStatusCode(UserKeyStatusCode value) {
        this.statusCode = value;
    }

    @Override
    public UserKeyStatusCode getStatusCode() {
        return statusCode;
    }

    /**
     * Constructor to set status to other than success
     *
     * @param data
     * @param statusCode
     */
    public UserKeyResultObject(UserKeyVO data, UserKeyStatusCode statusCode) {
        super(data, statusCode);
    }

    public String getMessage() {
        return statusCode.getMessage();
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }

    /**
     * Constructor for SUCCESS case
     *
     * @param data
     */
    public UserKeyResultObject(UserKeyVO data) {
        super(data);
    }

    @Override
    public UserKeyVO getData() {
        return data;
    }

    @Override
    public UserKeyStatusCode getSuccessfulStatusValue() {
        return UserKeyStatusCode.SUCCESS;
    }

    @Override
    protected void setData(UserKeyVO value) {
        this.data = value;

    }

    @Override
    public String getErrorFieldName() {
        return this.statusCode.getErrorField();
    }

    @Override
    protected UserKeyStatusCode getErrorStatusValue() {

        return UserKeyStatusCode.ERROR;
    }

    @AllArgsConstructor
    @Getter
    public enum UserKeyStatusCode {
        /** OK */
        SUCCESS("success", HttpStatus.OK),
        CREATED("created", HttpStatus.CREATED),
        NO_CONTENT("no_content", HttpStatus.NO_CONTENT),

        /** Internal Server Error */
        ERROR("unknown_exception", HttpStatus.INTERNAL_SERVER_ERROR),

        /** Others */
        USER_NOT_FOUND("user_not_found", HttpStatus.NOT_FOUND),
        INCORRECT_CURRENT_PASSWORD("current_password", "incorrect_current_password", HttpStatus.BAD_REQUEST),
        USER_KEY_NOT_EXIST("user_key", "user_key_not_exist", HttpStatus.BAD_REQUEST),
        USER_KEY_EXPIRED("user_key", "user_key_expired", HttpStatus.BAD_REQUEST);

        private final String errorField;
        private final String message;
        private final HttpStatus httpStatus;

        UserKeyStatusCode(String message, HttpStatus httpStatus) {
            this(null, message, httpStatus);
        }
    }
}
