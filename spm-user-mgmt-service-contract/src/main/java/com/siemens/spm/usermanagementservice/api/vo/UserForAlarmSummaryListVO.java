package com.siemens.spm.usermanagementservice.api.vo;

import java.io.Serializable;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserForAlarmSummaryListVO implements Serializable {

    private static final long serialVersionUID = 6534008517434643165L;

    private List<UserForAlarmSummaryVO> userForAlarmSummaryVOList;

}
