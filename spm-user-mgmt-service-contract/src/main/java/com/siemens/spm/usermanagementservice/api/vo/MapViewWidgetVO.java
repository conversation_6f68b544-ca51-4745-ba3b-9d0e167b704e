package com.siemens.spm.usermanagementservice.api.vo;

import java.sql.Timestamp;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.shared.domaintype.MetricConstants;
import com.siemens.spm.usermanagementservice.api.constant.PeriodType;
import com.siemens.spm.usermanagementservice.api.constant.WidgetIds;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import static com.siemens.spm.usermanagementservice.api.constant.DashboardValidation.MapView;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MapViewWidgetVO extends AbstractWidgetVO {

    private static final long serialVersionUID = -6106279779361758060L;

    @NotNull
    @JsonProperty("period_type")
    private PeriodType periodType;

    /**
     * Only in case period is custom
     */
    @JsonProperty("from_time")
    private Timestamp fromTime;

    /**
     * Only in case period is custom
     */
    @JsonProperty("to_time")
    private Timestamp toTime;

    @NotNull
    @Min(MapView.MIN_ZOOM_LEVEL)
    @Max(MapView.MAX_ZOOM_LEVEL)
    @JsonProperty("zoom_level")
    private Float zoomLevel;

    @NotNull
    @JsonProperty(value = "default_metric", required = true)
    private String defaultMetric = MetricConstants.OPEN_ALARM_NOTI_COUNT_ID;

    @NotNull
    @JsonProperty("center_pointer")
    private LocationVO centerPoint;

    @NotNull
    @Min(MapView.MIN_RADIUS)
    @Max(MapView.MAX_RADIUS)
    @JsonProperty("normal_map_radius")
    private Integer normalMapRadius;

    @NotNull
    @Min(MapView.MIN_RADIUS)
    @Max(MapView.MAX_RADIUS)
    @JsonProperty("heat_map_radius")
    private Integer heatMapRadius;

    @NotNull
    @Min(MapView.MIN_BLUR)
    @Max(MapView.MAX_BLUR)
    @JsonProperty("heat_map_blur")
    private Integer heatMapBlur;

    @Override
    public String getWidgetId() {
        return WidgetIds.MAP_VIEW;
    }

}
