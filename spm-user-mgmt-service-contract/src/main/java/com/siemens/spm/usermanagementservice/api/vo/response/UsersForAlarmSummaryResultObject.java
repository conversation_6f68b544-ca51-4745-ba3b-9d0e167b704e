package com.siemens.spm.usermanagementservice.api.vo.response;

import org.springframework.http.HttpStatus;

import com.siemens.spm.common.shared.vo.AbstractResultObject;
import com.siemens.spm.common.shared.vo.SimpleResultObject;
import com.siemens.spm.usermanagementservice.api.vo.UserForAlarmSummaryListVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class UsersForAlarmSummaryResultObject
        extends AbstractResultObject<UserForAlarmSummaryListVO, SimpleResultObject.SimpleStatusCode> {

    private static final long serialVersionUID = -2946837302116374143L;

    private UserForAlarmSummaryListVO data;

    private SimpleResultObject.SimpleStatusCode statusCode;

    @Override
    public String getMessage() {
        return statusCode.getMessage();
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }

    @Override
    public String getErrorFieldName() {
        return statusCode.getErrorField();
    }

    @Override
    protected SimpleResultObject.SimpleStatusCode getErrorStatusValue() {
        return SimpleResultObject.SimpleStatusCode.ERROR;
    }

    @Override
    public SimpleResultObject.SimpleStatusCode getSuccessfulStatusValue() {
        return SimpleResultObject.SimpleStatusCode.SUCCESS;
    }

}
