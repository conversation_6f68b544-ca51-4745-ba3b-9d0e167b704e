package com.siemens.spm.usermanagementservice.api.vo.response;

import org.springframework.http.HttpStatus;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.siemens.spm.common.shared.vo.AbstractResultObject;
import com.siemens.spm.usermanagementservice.api.vo.NotiSettingVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;

@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@RequiredArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class NotiSettingResultObject
        extends AbstractResultObject<NotiSettingVO, AgencyUserSettingResultObject.AgencyUserSettingStatusCode> {

    private static final long serialVersionUID = -4328650939277637961L;

    private NotiSettingVO data;

    @NonNull
    private AgencyUserSettingResultObject.AgencyUserSettingStatusCode statusCode;

    @Override
    public String getMessage() {
        return statusCode.getMessage();
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }

    @Override
    public String getErrorFieldName() {
        return statusCode.getErrorField();
    }

    @Override
    protected AgencyUserSettingResultObject.AgencyUserSettingStatusCode getErrorStatusValue() {
        return AgencyUserSettingResultObject.AgencyUserSettingStatusCode.UNKNOWN_ERROR;
    }

    @Override
    public AgencyUserSettingResultObject.AgencyUserSettingStatusCode getSuccessfulStatusValue() {
        return AgencyUserSettingResultObject.AgencyUserSettingStatusCode.SUCCESS;
    }

}
