package com.siemens.spm.usermanagementservice.api.constant;

import com.siemens.spm.usermanagementservice.api.vo.LocationVO;

public final class DashboardValidation {

    private DashboardValidation() {
    }

    public static class MapView {
        private MapView() {
        }

        public static final PeriodType DEFAULT_PERIOD_TYPE = PeriodType.LAST_24_HOURS;

        public static final int MIN_ZOOM_LEVEL = 1;
        public static final int MAX_ZOOM_LEVEL = 19;
        public static final float DEFAULT_ZOOM_LEVEL = 2;
        public static final float CITY_ZOOM_LEVEL = 14;
        public static final LocationVO DEFAULT_CENTER_POINT = LocationVO.NULL_ISLAND;

        public static final int MIN_RADIUS = 1;
        public static final int MAX_RADIUS = 40;
        public static final int DEFAULT_RADIUS = 10;

        public static final int MIN_BLUR = 1;
        public static final int MAX_BLUR = 20;

        public static final int DEFAULT_BLUR = 10;
    }

    public static class OpenAlarm {
        private OpenAlarm() {
        }

        public static final IntersectionOption DEFAULT_INTERSECTION_OPTION = IntersectionOption.INCLUDE_ALL;
    }

    public static class TopIntOpenAlarm {
        private TopIntOpenAlarm() {
        }

        public static final IntersectionOption DEFAULT_INTERSECTION_OPTION = IntersectionOption.INCLUDE_ALL;
    }

}
