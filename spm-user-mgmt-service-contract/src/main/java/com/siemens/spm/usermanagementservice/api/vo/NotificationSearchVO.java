/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : NotificationSearchVO.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.api.vo;

import java.io.Serializable;
import java.sql.Timestamp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(value = { "content" })
public class NotificationSearchVO implements Serializable {

    private static final long serialVersionUID = 379473753273926440L;

    @JsonProperty("id")
    private Long id;

    @SuppressWarnings("java:S1948")
    @JsonProperty("name")
    private Object name;

    @SuppressWarnings("java:S1948")
    @JsonProperty("description")
    private Object description;

    @JsonProperty("message")
    private String message;

    @JsonProperty("content")
    private String content;

    @JsonProperty("type")
    private String type;

    @JsonProperty("created_at")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Timestamp createdAt;

    @JsonProperty("read_status")
    private String readStatus;

    @JsonProperty("flag_status")
    private String flagStatus;

    @SuppressWarnings("java:S1948")
    @JsonProperty("action")
    private JsonNode action;

    @JsonProperty("intersection_name")
    private String intersectionName;

    @JsonProperty("last_modified_at")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    protected Timestamp lastModifiedAt;
}
