/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : IntersectionForDashboardTopRankVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.api.vo;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
public class IntersectionForDashboardTopRankVO implements Serializable {

    private static final long serialVersionUID = 4488342076696142039L;

    @JsonProperty("name")
    private String name;

    @JsonProperty("open_alarm_count")
    private Long openAlarmCount;

}
