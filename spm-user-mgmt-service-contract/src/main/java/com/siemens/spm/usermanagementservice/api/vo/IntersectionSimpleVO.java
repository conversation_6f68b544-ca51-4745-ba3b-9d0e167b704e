/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : IntersectionSimpleVO.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.api.vo;

import java.io.Serializable;
import java.util.TimeZone;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
public class IntersectionSimpleVO implements Serializable {

    private static final long serialVersionUID = 6742439752033482610L;

    @JsonProperty("id")
    private String id;

    @JsonProperty("latitude")
    private Double latitude;

    @JsonProperty("longitude")
    private Double longitude;

    @JsonProperty("name")
    private String name;

    @JsonProperty("number")
    private String number;

    /**
     * Timezone of intersection. This property will be serialized as a String field in JSON document
     */
    @JsonProperty("timezone")
    private TimeZone timeZone;

}
