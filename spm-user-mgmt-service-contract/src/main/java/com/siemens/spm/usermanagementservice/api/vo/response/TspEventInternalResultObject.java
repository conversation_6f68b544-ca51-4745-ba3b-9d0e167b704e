package com.siemens.spm.usermanagementservice.api.vo.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.siemens.spm.common.shared.vo.AbstractSimpleResultObject;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.http.HttpStatus;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class TspEventInternalResultObject extends AbstractSimpleResultObject<TspEventInternalResultObject.StatusCode> {

    private static final long serialVersionUID = -7546919399548604262L;

    private StatusCode statusCode;

    @Override
    public String getMessage() {
        return statusCode.getMessage();
    }

    public TspEventInternalResultObject() {
        super();
    }

    public TspEventInternalResultObject(StatusCode statusCode) {
        super(statusCode);
    }

    @Override
    public StatusCode getStatusCode() {
        return statusCode;
    }

    @Override
    protected StatusCode getErrorStatusValue() {
        return StatusCode.ERROR;
    }

    @Override
    public StatusCode getSuccessfulStatusValue() {
        return StatusCode.SUCCESS;
    }

    @Override
    protected void setStatusCode(StatusCode value) {
        statusCode = value;
    }

    @Override
    public String getErrorFieldName() {
        return statusCode.getErrorField();
    }

    @AllArgsConstructor
    @Getter
    public enum StatusCode {
        /**
         * OK
         */
        SUCCESS("success", HttpStatus.OK),

        /**
         * Internal Server Error
         */
        ERROR("unknown_exception", HttpStatus.INTERNAL_SERVER_ERROR);

        private final String errorField;
        private final String message;
        private final HttpStatus httpStatus;

        StatusCode(String message, HttpStatus httpStatus) {
            this(null, message, httpStatus);
        }
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }

}
