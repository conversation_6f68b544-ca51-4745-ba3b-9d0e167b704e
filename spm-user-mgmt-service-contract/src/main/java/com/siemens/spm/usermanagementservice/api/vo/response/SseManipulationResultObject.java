package com.siemens.spm.usermanagementservice.api.vo.response;

import org.springframework.http.HttpStatus;

import com.siemens.spm.common.shared.vo.AbstractSimpleResultObject;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SseManipulationResultObject extends AbstractSimpleResultObject<SseManipulationResultObject.StatusCode> {

    private StatusCode statusCode;

    @Override
    public String getMessage() {
        return statusCode.message;
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.httpStatus;
    }

    @Override
    public String getErrorFieldName() {
        return statusCode.errorField;
    }

    @Override
    protected StatusCode getErrorStatusValue() {
        return StatusCode.ERROR;
    }

    @Override
    public StatusCode getSuccessfulStatusValue() {
        return StatusCode.SUCCESS;
    }

    @AllArgsConstructor
    @Getter
    public enum StatusCode {
        /**
         * OK
         */
        SUCCESS("success", HttpStatus.OK),
        CREATED("created", HttpStatus.CREATED),

        INVALID_TOPIC("topics", "invalid_topic", HttpStatus.BAD_REQUEST),
        CONNECTION_NOT_FOUND("connection_id", "connection_not_found", HttpStatus.BAD_REQUEST),

        /**
         * Internal Server Error
         */
        ERROR("unknown_exception", HttpStatus.INTERNAL_SERVER_ERROR);

        private final String errorField;
        private final String message;
        private final HttpStatus httpStatus;

        StatusCode(String message, HttpStatus httpStatus) {
            this(null, message, httpStatus);
        }
    }

}
