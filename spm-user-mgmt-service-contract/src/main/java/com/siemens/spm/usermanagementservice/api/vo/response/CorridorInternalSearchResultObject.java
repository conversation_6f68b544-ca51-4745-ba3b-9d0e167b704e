package com.siemens.spm.usermanagementservice.api.vo.response;

import org.springframework.http.HttpStatus;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.siemens.spm.common.shared.vo.AbstractResultObject;
import com.siemens.spm.usermanagementservice.api.vo.CorridorInternalListVO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CorridorInternalSearchResultObject
        extends AbstractResultObject<CorridorInternalListVO, CorridorInternalSearchResultObject.StatusCode> {

    private static final long serialVersionUID = 268609489472689225L;

    private CorridorInternalListVO data;

    private StatusCode statusCode;

    @Override
    public String getMessage() {
        return statusCode.getMessage();
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }

    @Override
    public String getErrorFieldName() {
        return statusCode.getErrorField();
    }

    @Override
    protected StatusCode getErrorStatusValue() {
        return StatusCode.ERROR;
    }

    @Override
    public StatusCode getSuccessfulStatusValue() {
        return StatusCode.SUCCESS;
    }

    @Getter
    @AllArgsConstructor
    public enum StatusCode {
        /** OK */
        SUCCESS("success", HttpStatus.OK),

        /** Internal Server Error */
        ERROR("unknown_exception", HttpStatus.INTERNAL_SERVER_ERROR);

        private final String errorField;
        private final String message;
        private final HttpStatus httpStatus;

        StatusCode(String message, HttpStatus httpStatus) {
            this(null, message, httpStatus);
        }
    }

}
