/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AgencyInterComController.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.api.intercom;

import com.siemens.spm.common.intercom.InterComController;
import com.siemens.spm.common.util.RestUtils;
import com.siemens.spm.usermanagementservice.api.vo.request.IntersectionsInAgencyVerifyRequestVO;
import com.siemens.spm.usermanagementservice.api.vo.response.AgencySettingsResultObject;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionsInAgencyVerifyResultObject;
import jakarta.validation.constraints.NotNull;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * Internal REST interface providing information to other services. This interface is implemented by the actual running
 * service.
 *
 * <AUTHOR> Nguyen
 */
@RequestMapping(AgencyInterComController.API_ROOT)
public interface AgencyInterComController extends InterComController {

    String API_ROOT = INTERNAL_API + "/v1";

    String AGENCIES_RESOURCE = "/agencies";
    String AGENCIES_VERIFICATION_INTERSECTIONS_RESOURCE = "/agencies/verification/intersections";
    String AGENCIES_SETTINGS_RESOURCE = "/agencies/{agency_id}/settings";

    /**
     * Verify intersections in agency
     *
     * @param verifyRequest: IntersectionsInAgencyVerifyRequestVO
     * @return IntersectionsInAgencyVerifyResultObject
     */
    @PostMapping(AGENCIES_VERIFICATION_INTERSECTIONS_RESOURCE)
    ResponseEntity<IntersectionsInAgencyVerifyResultObject> verifyIntersectionsInAgency(
        @NotNull @RequestBody IntersectionsInAgencyVerifyRequestVO verifyRequest);

    /**
     * invoke verifyIntersectionsInAgency()
     *
     * @param endpoint:     endpoint of target service
     * @param verifyRequest IntersectionsInAgencyVerifyRequestVO
     * @return IntersectionsInAgencyVerifyResultObject
     */
    static ResponseEntity<IntersectionsInAgencyVerifyResultObject> invokeVerifyIntersectionsInAgency(String endpoint,
        IntersectionsInAgencyVerifyRequestVO verifyRequest) {
        String url = endpoint + API_ROOT + AGENCIES_VERIFICATION_INTERSECTIONS_RESOURCE;
        Integer agencyId = verifyRequest.getAgencyId();

        return RestUtils.postWithAgencyHeader(url, agencyId.toString(), verifyRequest,
            IntersectionsInAgencyVerifyResultObject.class);
    }

    /**
     * Get agency setting of specific agency
     *
     * @param agencyId id of agency
     * @return {@link AgencySettingsResultObject}
     */
    @GetMapping(AGENCIES_SETTINGS_RESOURCE)
    ResponseEntity<AgencySettingsResultObject> getAgencySettings(@PathVariable(value = "agency_id") Integer agencyId);

    /**
     * Invoke getAgencySettings()
     *
     * @param endpoint endpoint of target service
     * @param agencyId id of agency
     * @return {@link AgencySettingsResultObject}
     */
    static ResponseEntity<AgencySettingsResultObject> invokeGetAgencySettings(String endpoint, Integer agencyId) {
        String url = endpoint + API_ROOT + AGENCIES_SETTINGS_RESOURCE.replace("{agency_id}", String.valueOf(agencyId));

        return RestUtils.getWithAgencyHeader(url, agencyId.toString(), AgencySettingsResultObject.class);
    }

    @GetMapping(AGENCIES_RESOURCE + "/{agencyId}/licenses/ids")
    ResponseEntity<List<String>> getLicenseIds(@PathVariable Integer agencyId);

}
