/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AgencySettingsResultObject.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.api.vo.response;

import org.springframework.http.HttpStatus;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.siemens.spm.common.shared.vo.AbstractResultObject;
import com.siemens.spm.usermanagementservice.api.vo.AgencySettingsVO;
import com.siemens.spm.usermanagementservice.api.vo.response.AgencySettingsResultObject.AgencySettingsStatusCode;
import lombok.AllArgsConstructor;
import lombok.Getter;

@JsonInclude(Include.NON_NULL)
public class AgencySettingsResultObject extends AbstractResultObject<AgencySettingsVO, AgencySettingsStatusCode> {

    private static final long serialVersionUID = -2474106372120969056L;

    private AgencySettingsVO data;

    private AgencySettingsStatusCode statusCode;

    /**
     * Default constructor
     */
    public AgencySettingsResultObject() {
        super();
    }

    /**
     * Constructor to set status to other than success
     *
     * @param data
     * @param statusCode
     */
    public AgencySettingsResultObject(AgencySettingsVO data, AgencySettingsStatusCode statusCode) {
        super(data, statusCode);
    }

    @Override
    public AgencySettingsVO getData() {
        return data;
    }

    /**
     * Constructor for SUCCESS case
     *
     * @param data
     */
    public AgencySettingsResultObject(AgencySettingsVO data) {
        super(data);
    }

    @Override
    protected void setStatusCode(AgencySettingsStatusCode value) {
        this.statusCode = value;
    }

    @Override
    protected AgencySettingsStatusCode getErrorStatusValue() {
        return AgencySettingsStatusCode.UNKNOWN_ERROR;
    }

    @Override
    protected void setData(AgencySettingsVO value) {
        this.data = value;
    }

    public String getMessage() {
        return statusCode.getMessage();
    }

    @Override
    public AgencySettingsStatusCode getStatusCode() {
        return statusCode;
    }

    @Override
    public String getErrorFieldName() {
        return statusCode.getErrorField();
    }

    @AllArgsConstructor
    @Getter
    public enum AgencySettingsStatusCode {
        /** OK */
        SUCCESS("success", HttpStatus.OK),
        NO_CONTENT("success", HttpStatus.NO_CONTENT),

        /** Internal Server Error */
        UNKNOWN_ERROR("unknown_exception", HttpStatus.INTERNAL_SERVER_ERROR),

        /** Bad Request */
        INVALID_AGENCY_SETTINGS("invalid_agency_settings", HttpStatus.BAD_REQUEST);

        /** Not found */

        private final String errorField;
        private final String message;
        private final HttpStatus httpStatus;

        AgencySettingsStatusCode(String message, HttpStatus httpStatus) {
            this(null, message, httpStatus);
        }
    }

    @Override
    public AgencySettingsStatusCode getSuccessfulStatusValue() {
        return AgencySettingsStatusCode.SUCCESS;
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }

}
