/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : ColorSettingVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.api.vo;

import java.io.Serializable;
import java.util.Comparator;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ColorSettingVO implements Serializable {

    public static class FromValueComparator implements Comparator<ColorSettingVO> {

        @Override
        public int compare(ColorSettingVO o1, ColorSettingVO o2) {
            Double from1 = o1.getFromValue();
            Double from2 = o2.getFromValue();

            if (from1 == null) {
                if (from2 == null) {
                    return 0;
                } else {
                    return -1;
                }
            }

            if (from2 == null) {
                return 1;
            }

            return from1.compareTo(from2);
        }

    }

    private static final long serialVersionUID = -3772023655139355363L;

    public static FromValueComparator getFromValueComparator() {
        return new FromValueComparator();
    }

    @JsonProperty("from_value")
    private Double fromValue;

    /**
     * Color hex string
     */
    @JsonProperty("color")
    private String color;

}
