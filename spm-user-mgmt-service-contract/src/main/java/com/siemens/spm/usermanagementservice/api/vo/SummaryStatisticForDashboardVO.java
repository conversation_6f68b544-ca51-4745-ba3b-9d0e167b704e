package com.siemens.spm.usermanagementservice.api.vo;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SummaryStatisticForDashboardVO implements Serializable {

    private static final long serialVersionUID = -7593065183346207007L;

    @JsonProperty("int_statistics")
    private List<SummaryIntersectionStatisticVO> intersectionStatisticVOList;

    @JsonProperty("total_count")
    private Long totalCount;

}
