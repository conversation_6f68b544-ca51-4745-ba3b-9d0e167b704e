// /*
//  * Copyright (C) Siemens.  All Rights Reserved.
//  *
//  * Source      : NotificationController.java
//  * Project     : SPM Platform
//  */

// package com.siemens.spm.usermanagementservice.api.controller;

// import java.util.List;

// import jakarta.validation.Valid;
// import jakarta.validation.constraints.NotEmpty;
// import jakarta.validation.constraints.NotNull;

// import org.springframework.http.HttpStatus;
// import org.springframework.http.ResponseEntity;
// import org.springframework.web.bind.annotation.DeleteMapping;
// import org.springframework.web.bind.annotation.GetMapping;
// import org.springframework.web.bind.annotation.PatchMapping;
// import org.springframework.web.bind.annotation.PathVariable;
// import org.springframework.web.bind.annotation.RequestBody;
// import org.springframework.web.bind.annotation.RequestMapping;
// import org.springframework.web.bind.annotation.RequestParam;
// import org.springframework.web.bind.annotation.ResponseStatus;

// import com.siemens.spm.common.api.PublicController;
// import com.siemens.spm.usermanagementservice.api.vo.request.NotificationUpdatedRequestVO;
// import com.siemens.spm.usermanagementservice.api.vo.response.NotificationDetailsResultObject;
// import com.siemens.spm.usermanagementservice.api.vo.response.NotificationLatestUnreadResultObject;
// import com.siemens.spm.usermanagementservice.api.vo.response.NotificationManipulateResultObject;
// import com.siemens.spm.usermanagementservice.api.vo.response.NotificationSearchResultObject;
// import com.siemens.spm.usermanagementservice.api.vo.response.NotificationTypeListResultObject;
// import io.swagger.v3.oas.annotations.Operation;
// import io.swagger.v3.oas.annotations.Parameter;
// import io.swagger.v3.oas.annotations.enums.ParameterIn;
// import io.swagger.v3.oas.annotations.responses.ApiResponse;
// import io.swagger.v3.oas.annotations.responses.ApiResponses;
// import io.swagger.v3.oas.annotations.tags.Tag;

// @RequestMapping(NotificationController.URL_ROOT)
// @Tag(name = "notification", description = "Notification API")
// public interface NotificationController extends PublicController {

//     String VERSION = "/v1";
//     String NOTIFICATION_RESOURCE = "/notifications";
//     String URL_ROOT = PUBLIC_API + VERSION + NOTIFICATION_RESOURCE;

//     String MARK_READ_ALL_NOTIFICATIONS = "/as-read";
//     String LATEST_UNREAD_NOTIFICATIONS = "/lastest-unread";
//     String ALL_NOTIFICATION_TYPES = "/types";

//     /**
//      * Get all notification types: /notifications/types/all
//      *
//      * @return ResponseEntity<NotificationTypeListResultObject>
//      */
//     @Operation(summary = "Retrieve all notification types")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "200", description = "Success") })

//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER, description = "Bearer Token type")

//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping(ALL_NOTIFICATION_TYPES)
//     ResponseEntity<NotificationTypeListResultObject> getAllNotificationTypes();

//     /**
//      * Get notification in detail: /notifications/{notification_id}
//      *
//      * @param notificationId
//      * @return ResponseEntity<NotificationDetailsResultObject>
//      */
//     @Operation(summary = "Retrieve an notification in detail by id")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request"),
//             @ApiResponse(responseCode = "404", description = "Not Found")
//     })

//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER, description = "Bearer Token type")

//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping("{notification_id}")
//     ResponseEntity<NotificationDetailsResultObject> getNotificationDetails(
//             @Parameter(description = "Id of the notification need to retrieve")
//             @Valid @NotNull(message = "notification.notication_id_not_empty")
//             @PathVariable("notification_id")
//             Long notificationId
//     );

//     /**
//      * Get the latest unread notifications by page and size: GET /notifications/latest-unread
//      *
//      * @param page
//      * @param size
//      * @return NotificationLatestUnreadResultObject
//      */
//     @Operation(summary = "Get latest unread notifications by page and size")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "200", description = "Success"),
//             @ApiResponse(responseCode = "400", description = "Bad Request")
//     })

//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER, description = "Bearer Token type")

//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping(LATEST_UNREAD_NOTIFICATIONS)
//     ResponseEntity<NotificationLatestUnreadResultObject> getLatestUnreadNotifications(
//             @Parameter(description = "Agency id of the notification which belong to")
//             @RequestParam(value = "agency_id", required = false)
//             Integer agencyId,

//             @Parameter(description = "Page is used to page the returning result")
//             @RequestParam(value = "page", required = false)
//             Integer page,

//             @Parameter(description = "Total of notification will get")
//             @RequestParam(value = "size", required = false)
//             Integer size
//     );

//     /**
//      * Filter/search notifications GET /notifications?fromDate=...
//      *
//      * @param text
//      * @param fromDate
//      * @param toDate
//      * @param typeId
//      * @param readStatus
//      * @param flagStatus
//      * @param orderByColumns
//      * @param page
//      * @param size
//      * @return ResponseEntity<NotificationSearchResultObject>
//      */
//     @Operation(summary = "search / filter notifications")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request"),
//             @ApiResponse(responseCode = "404", description = "Not Found"),
//             @ApiResponse(responseCode = "200", description = "Success")
//     })

//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER, description = "Bearer Token type")

//     @ResponseStatus(HttpStatus.OK)
//     @GetMapping
//     ResponseEntity<NotificationSearchResultObject> searchNotifications(
//             @Parameter(description = "Agency id of the notification which belong to")
//             @RequestParam(value = "agency_id", required = false)
//             Integer agencyId,

//             @Parameter(description = "Id or name of intersection that the notifications belong to")
//             @RequestParam(value = "intersection", required = false)
//             String intersection,

//             @Parameter(description = "text to search")
//             @RequestParam(value = "text", required = false)
//             String text,

//             @Parameter(description = "This day must be lower than created_at")
//             @RequestParam(value = "from_date", required = false)
//             Long fromDate,

//             @Parameter(description = "This day must be greater than created_at")
//             @RequestParam(value = "to_date", required = false)
//             Long toDate,

//             @Parameter(description = "Id of type that the notifications belong to")
//             @RequestParam(value = "type_id", required = false)
//             Long typeId,

//             @Parameter(description = "read status of the searched notifications (read or unread)")
//             @RequestParam(value = "read_status", required = false)
//             String readStatus,

//             @Parameter(description = "flag status of the searched notifications (flag or unflag)")
//             @RequestParam(value = "flag_status", required = false)
//             String flagStatus,

//             @Parameter(description = "Fields will be used to sort returning data")
//             @RequestParam(value = "sort", required = false)
//             String[] orderByColumns,

//             @Parameter(description = "Page is used to page the returning result")
//             @RequestParam(value = "page", required = false)
//             Integer page,

//             @Parameter(description = "Size is used to size the returning result")
//             @RequestParam(value = "size", required = false)
//             Integer size
//     );

//     /**
//      * Update notifications: PATH /notifications
//      *
//      * @param updateRequest
//      * @return ResponseEntity<NotificationManipulateResultObject>
//      */
//     @Operation(summary = "Update notifications")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "400", description = "Bad Request"),
//             @ApiResponse(responseCode = "204", description = "No Content")
//     })

//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER, description = "Bearer Token type")

//     @ResponseStatus(HttpStatus.NO_CONTENT)
//     @PatchMapping
//     ResponseEntity<NotificationManipulateResultObject> updateNotifications(
//             @Parameter(description = "The request body which keeps information of the request")
//             @Valid
//             @RequestBody
//             NotificationUpdatedRequestVO updateRequest);

//     /**
//      * Mark all Notifications as read (for current user): PATCH /notifications/as-read
//      *
//      * @return ResponseEntity<NotificationManipulateResultObject>
//      */
//     @Operation(summary = "Mark all Notifications as read (for current user)")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "204", description = "No Content")
//     })

//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER, description = "Bearer Token type")

//     @ResponseStatus(HttpStatus.NO_CONTENT)
//     @PatchMapping(MARK_READ_ALL_NOTIFICATIONS)
//     ResponseEntity<NotificationManipulateResultObject> markAllNotificationsAsRead(
//             @Parameter(description = "Agency id of the notification which belong to")
//             @RequestParam(value = "agency_id", required = false) Integer agencyId
//     );

//     /**
//      * Delete Notifications: DELETE /notifications
//      *
//      * @return ResponseEntity<NotificationManipulateResultObject>
//      */
//     @Operation(summary = "Delete Notifications by Ids")
//     @ApiResponses(value = {
//             @ApiResponse(responseCode = "204", description = "No Content")
//     })

//     @Parameter(name = "Authorization", example = "Bearer {jwt}", required = true, in = ParameterIn.HEADER, description = "Bearer Token type")

//     @ResponseStatus(HttpStatus.NO_CONTENT)
//     @DeleteMapping
//     ResponseEntity<NotificationManipulateResultObject> deleteNotifications(
//             @Parameter(description = "List of id of alarm rules to be deleted")
//             @Valid @NotEmpty(message = "notification.noti_ids_not_empty")
//             @RequestParam(value = "noti_ids")
//             List<Long> notiIds);

// }
