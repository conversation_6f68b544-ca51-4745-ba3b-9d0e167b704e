/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : IntersectionDetailResultObject.java
 * Project     : SPM Platform
 */
package com.siemens.spm.usermanagementservice.api.vo.response;

import org.springframework.http.HttpStatus;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.siemens.spm.common.shared.vo.AbstractResultObject;
import com.siemens.spm.usermanagementservice.api.vo.IntersectionDetailVO;
import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionDetailResultObject.StatusCode;

import lombok.AllArgsConstructor;
import lombok.Getter;

@JsonInclude(Include.NON_NULL)
public class IntersectionDetailResultObject extends AbstractResultObject<IntersectionDetailVO, StatusCode> {

    private static final long serialVersionUID = -9205084441657959286L;

    private IntersectionDetailVO data;

    private StatusCode statusCode;

    /**
     * Constructor for ERROR case
     */
    public IntersectionDetailResultObject() {

    }

    /**
     * Constructor to set status to other than success
     *
     * @param data
     * @param statusCode
     */
    public IntersectionDetailResultObject(IntersectionDetailVO data, StatusCode statusCode) {
        super(data, statusCode);
    }

    @Override
    public IntersectionDetailVO getData() {
        return data;
    }

    @Override
    protected void setData(IntersectionDetailVO value) {
        this.data = value;

    }

    /**
     * Constructor for SUCCESS case
     *
     * @param data
     */
    public IntersectionDetailResultObject(IntersectionDetailVO data) {
        super(data);
    }

    @Override
    public String getErrorFieldName() {
        return this.statusCode.getErrorField();
    }

    @Override
    public StatusCode getStatusCode() {
        return statusCode;
    }

    @Override
    protected StatusCode getErrorStatusValue() {

        return StatusCode.ERROR;
    }

    @Override
    protected void setStatusCode(StatusCode value) {
        this.statusCode = value;
    }

    @AllArgsConstructor
    @Getter
    public enum StatusCode {

        /** Internal Server Error */
        ERROR("unknown_exception", HttpStatus.INTERNAL_SERVER_ERROR),

        /** OK */
        SUCCESS("success", HttpStatus.OK),

        /** Not found */
        USER_NOT_FOUND("user_not_found", HttpStatus.NOT_FOUND),
        AGENCY_NOT_FOUND("agency_not_found", HttpStatus.NOT_FOUND),

        /** Bad Request */
        INTERSECTION_NOT_FOUND("intersection_id", "intersection_not_found", HttpStatus.BAD_REQUEST);

        private final String errorField;
        private final String message;
        private final HttpStatus httpStatus;

        StatusCode(String message, HttpStatus httpStatus) {
            this(null, message, httpStatus);
        }
    }

    public String getMessage() {
        return statusCode.getMessage();
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }

    @Override
    public StatusCode getSuccessfulStatusValue() {
        return StatusCode.SUCCESS;
    }

}
