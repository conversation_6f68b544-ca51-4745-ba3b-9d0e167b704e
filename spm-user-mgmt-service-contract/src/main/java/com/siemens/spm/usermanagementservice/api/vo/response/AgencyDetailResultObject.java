/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AgencyDetailResultObject.java
 * Project     : SPM Platform
 */

package com.siemens.spm.usermanagementservice.api.vo.response;

import org.springframework.http.HttpStatus;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.siemens.spm.common.shared.vo.AbstractResultObject;
import com.siemens.spm.usermanagementservice.api.vo.AgencyDetailVO;
import com.siemens.spm.usermanagementservice.api.vo.response.AgencyDetailResultObject.StatusCode;

import lombok.AllArgsConstructor;
import lombok.Getter;

@JsonInclude(Include.NON_NULL)
public class AgencyDetailResultObject extends AbstractResultObject<AgencyDetailVO, StatusCode> {

    private static final long serialVersionUID = 8768555996334511078L;

    private static final String FIELD_EMAIL = "email";

    private AgencyDetailVO data;

    private StatusCode statusCode;

    /**
     * Constructor for ERROR case
     */
    public AgencyDetailResultObject() {
    }

    /**
     * Constructor to set status to other than success
     *
     * @param data
     * @param statusCode
     */
    public AgencyDetailResultObject(AgencyDetailVO data, StatusCode statusCode) {
        super(data, statusCode);
    }

    @Override
    public StatusCode getSuccessfulStatusValue() {
        return StatusCode.SUCCESS;
    }

    @Override
    public StatusCode getStatusCode() {
        return statusCode;
    }

    /**
     * Constructor for SUCCESS case
     *
     * @param data
     */
    public AgencyDetailResultObject(AgencyDetailVO data) {
        super(data);
    }

    public String getMessage() {
        return statusCode.getMessage();
    }

    @Override
    protected StatusCode getErrorStatusValue() {
        return StatusCode.ERROR;
    }

    @Override
    public AgencyDetailVO getData() {
        return data;
    }

    @Override
    protected void setData(AgencyDetailVO value) {
        this.data = value;
    }

    @Override
    protected void setStatusCode(StatusCode value) {
        this.statusCode = value;
    }

    @AllArgsConstructor
    @Getter
    public enum StatusCode {
        /** OK */
        SUCCESS("success", HttpStatus.OK),
        CREATED("created", HttpStatus.CREATED),
        NO_CONTENT("no_content", HttpStatus.NO_CONTENT),

        /** Internal Server Error */
        ERROR("unknown_exception", HttpStatus.INTERNAL_SERVER_ERROR),
        // Internal error but returning 400 so that front-end can show error
        CANNOT_REQUEST_AGENCY_ID("id_can_not_generated", HttpStatus.BAD_REQUEST),

        /** Bad Request */
        AGENCYNAME_ALREADY_USED("name", "name_already_used", HttpStatus.BAD_REQUEST),
        AGENCYNAME_CANNOT_UPDATED("name", "name_can_not_updated", HttpStatus.FORBIDDEN),
        ORGANIZATION_CANNOT_UPDATED("organization", "organization_can_not_updated", HttpStatus.FORBIDDEN),
        USER_EXISTED_IN_AGENCY(FIELD_EMAIL, "user_existed_in_agency", HttpStatus.BAD_REQUEST),
        INVALID_ACCOUNT(FIELD_EMAIL, "invalid_account", HttpStatus.BAD_REQUEST),
        INVALID_EMAIL(FIELD_EMAIL, "invalid_email", HttpStatus.BAD_REQUEST),
        INVALID_ROLE_NAME("role_name", "invalid_role_name", HttpStatus.BAD_REQUEST),
        INVALID_AGENCY_NAME("name", "invalid_name", HttpStatus.BAD_REQUEST),

        /** Not found */
        AGENCY_NOT_FOUND("agency_not_found", HttpStatus.NOT_FOUND),
        USER_NOT_FOUND("user_not_found", HttpStatus.NOT_FOUND);

        private final String errorField;
        private final String message;
        private final HttpStatus httpStatus;

        StatusCode(String message, HttpStatus httpStatus) {
            this(null, message, httpStatus);
        }
    }

    @Override
    public String getErrorFieldName() {
        return statusCode.getErrorField();
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }

}
