/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : InvalidPerfLogException.java
 * Project     : spm-analysis-service
 */
package com.siemens.spm.analysis.exception;

/**
 * Invalid or malformed PerfLog
 */
public class InvalidPerfLogException extends Exception {

    private static final long serialVersionUID = -3679969438947515132L;

    public InvalidPerfLogException() {
        super();
    }

    public InvalidPerfLogException(String message, Throwable cause) {
        super(message, cause);
    }

    public InvalidPerfLogException(String message) {
        super(message);
    }

    public InvalidPerfLogException(Throwable cause) {
        super(cause);
    }

}
