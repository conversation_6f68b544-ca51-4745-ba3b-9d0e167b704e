package com.siemens.spm.analysis.factory.queuelength;

import com.siemens.spm.analysis.aggregator.LaneQueueLengthAggregator;
import com.siemens.spm.analysis.vo.queuelength.QueueLengthChartVO;

/**
 * <AUTHOR> Ng<PERSON>en - <EMAIL>
 */
public class QueueLengthLaneChartBuilder extends QueueLengthChartBuilder {

    public QueueLengthLaneChartBuilder(QueueLengthChartVO chartVO, int binSize, int laneIdx) {
        super(chartVO, binSize);

        aggregator = new LaneQueueLengthAggregator(chartVO.getFromTime(), chartVO.getToTime(), binSize,
                phase.getPhaseNum(), laneIdx);
    }

}
