package com.siemens.spm.analysis.aggregator;

import com.siemens.spm.analysis.domain.Cycle;
import com.siemens.spm.analysis.vo.Plan;
import com.siemens.spm.analysis.vo.moe.PhaseCycleMOEVO;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;

@Slf4j
public class PhaseTrackerAggregator extends PhaseEventAggregator {

    private Cycle cycle;

    private final int pattern;

    @Setter
    private IntersectionConfigVO configVO;

    @Getter
    private PhaseCycleMOEVO phaseCycleMOEVO;

    public PhaseTrackerAggregator(int phase, int pattern, IntersectionConfigVO configVO) {
        super.init(phase);
        this.pattern = pattern;
        this.configVO = configVO;
    }

    @Override
    public void putEvent(PerfLogEventVO eventVO) {
        PerfLogEventVO.Event event = eventVO.getEvent();
        switch (event) {
            case PHASE_BEGIN_GREEN -> {
                if (phaseCycleMOEVO == null || cycle == null) {
                    cycle = new Cycle(eventVO.getDateTime(), phase.getPhaseNum());
                    phaseCycleMOEVO = new PhaseCycleMOEVO();
                    return;
                }
                cycle.updateState(eventVO);
            }
            case PHASE_GREEN_TERMINATION,
                 PHASE_BEGIN_YELLOW_CLEARANCE,
                 PHASE_END_YELLOW_CLEARANCE,
                 PHASE_BEGIN_RED_CLEARANCE -> cycle.updateState(eventVO);
            case PHASE_END_RED_CLEARANCE -> calAndPutCycleLength(eventVO.getDateTime());

            // phase termination: take the last event only
            case PHASE_GAP_OUT,
                 PHASE_FORCE_OFF,
                 PHASE_MAX_OUT -> {
                if (phaseCycleMOEVO != null) {
                    phaseCycleMOEVO.getPhaseStatusArray()[0] = event.name();
                }
            }
            case PEDESTRIAN_CALL_REGISTERED -> {
                if (phaseCycleMOEVO != null) {
                    phaseCycleMOEVO.getPhaseStatusArray()[1] = event.name();
                }
            }
            default -> {
                // not use
            }
        }
    }

    private void calAndPutCycleLength(LocalDateTime eventTime) {
        if (phaseCycleMOEVO == null || cycle == null) {
            cycle = new Cycle(eventTime, phase.getPhaseNum());
            phaseCycleMOEVO = new PhaseCycleMOEVO();
            return;
        }
        cycle.endCycle(eventTime);
        phaseCycleMOEVO.setProgrammedLength(getPhaseProgrammedLength());
        phaseCycleMOEVO.setPhase(phase.getPhaseNum());
        phaseCycleMOEVO.setActualLength((int) cycle.cycleDuration(false).toSeconds());
    }

    private int getPhaseProgrammedLength() {
        if (pattern == Plan.FREE_PLAN || pattern == Plan.UNKNOWN_PLAN) {
            return 0;
        }
        int[] splitTimesOfPattern = configVO.getPatternInfo().getSplitTimeOfPattern(pattern - 1);
        return splitTimesOfPattern[phase.getPhaseNum() - 1];
    }

}
