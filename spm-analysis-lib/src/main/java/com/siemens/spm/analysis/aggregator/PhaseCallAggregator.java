/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : PhaseCallAggregator.java
 * Project     : SPM Platform
 */
package com.siemens.spm.analysis.aggregator;

import com.siemens.spm.analysis.domain.Phase;
import com.siemens.spm.analysis.util.IntersectionConfigUtils;
import com.siemens.spm.analysis.vo.VolumeBinVO;
import com.siemens.spm.common.shared.domaintype.alarm.trafficmetric.MetricSpecifier;
import com.siemens.spm.common.shared.domaintype.alarm.trafficmetric.TrafficMetric;
import com.siemens.spm.perflog.vo.DetectorInfoVO;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * Aggregate DETECTOR_ON events calling a specific phase to bins and categorize them as AoR, AoY, AoG
 */
public class PhaseCallAggregator extends AbstractPhaseCallAggregator {

    private static final int MAX_PHASE_NUM = 16;

    private static final List<TrafficMetric> SUPPORTED_METRIC_LIST;

    static {
        SUPPORTED_METRIC_LIST = Arrays.asList(
                TrafficMetric.PHASE_CALL,
                TrafficMetric.AOG,
                TrafficMetric.AOG_VOLUME,
                TrafficMetric.AOG_PERCENT,
                TrafficMetric.AOY,
                TrafficMetric.AOY_VOLUME,
                TrafficMetric.AOY_PERCENT,
                TrafficMetric.AOR,
                TrafficMetric.AOR_VOLUME,
                TrafficMetric.AOR_PERCENT);
    }

    public static List<TrafficMetric> staticGetSupportedMetric() {
        return SUPPORTED_METRIC_LIST;
    }

    /**
     * Sub aggregators used to aggregate data of all phases (unspecified target phase)
     */
    private PhaseCallAggregator[] subAggrArr;

    private DetectorOnHolder detectorOnHolder;

    public PhaseCallAggregator() {
        init(Phase.UNSPECIFIED_PHASE_NUM, null, null, VolumeBinVO.DEFAULT_BIN_SIZE);
    }

    /**
     * @param phaseNum      Phase number
     * @param fromTime      Beginning time for the aggregation
     * @param binSize       Size of volume bins in seconds, from {@link VolumeBinVO}
     * @param detCallPhases Detector configuration from {@link DetectorInfoVO}
     */
    public PhaseCallAggregator(int phaseNum, LocalDateTime fromTime, int binSize, int[][] detCallPhases) {
        init(phaseNum, detCallPhases, fromTime, binSize);
    }

    @Override
    public void setTargetIdentifier(String targetIdentifier) {
        super.setTargetIdentifier(targetIdentifier);

        initSubAggregators();
    }

    @Override
    public void setBinSize(int binSize) {
        super.setBinSize(binSize);
        if (phase.getPhaseNum() == Phase.UNSPECIFIED_PHASE_NUM && subAggrArr != null) {
            for (int i = 0; i < MAX_PHASE_NUM; i++) {
                subAggrArr[i].setBinSize(binSize);
            }
        }
    }

    @Override
    public void setFromTime(LocalDateTime fromTime) {
        super.setFromTime(fromTime);
        if (phase.getPhaseNum() == Phase.UNSPECIFIED_PHASE_NUM && subAggrArr != null) {
            for (int i = 0; i < MAX_PHASE_NUM; i++) {
                subAggrArr[i].setFromTime(fromTime);
            }
        }
    }

    @Override
    public void setToTime(LocalDateTime toTime) {
        super.setToTime(toTime);
        if (phase.getPhaseNum() == Phase.UNSPECIFIED_PHASE_NUM && subAggrArr != null) {
            for (int i = 0; i < MAX_PHASE_NUM; i++) {
                subAggrArr[i].setToTime(toTime);
            }
        }
    }

    @Override
    public void setConfig(IntersectionConfigVO configVO) {
        super.setConfig(configVO);
        if (phase.getPhaseNum() == Phase.UNSPECIFIED_PHASE_NUM && subAggrArr != null) {
            for (int i = 0; i < MAX_PHASE_NUM; i++) {
                subAggrArr[i].setConfig(configVO);
            }
        }

        detectorOnHolder
                .setProjectedTimes(IntersectionConfigUtils.identifyAdvanceDetectorProjectedTime(configVO, phase));
    }

    /**
     * {@inheritDoc} Accepting additional events:</br>
     * <ul>
     * <li>DETECTOR_ON</li>
     * </ul>
     */
    @Override
    public void putEvent(PerfLogEventVO eventVO) {
        super.putEvent(eventVO);

        if (eventVO == null)
            return;

        // Fill empty bins
        fillBins(eventVO.getDateTime());

        // Make sure event is still in current currentBin. Ignore event outside current bin
        if (!eventDateTimeGuard(eventVO)) {
            return;
        }

        // Update phase state at first, before any aggregation
        if (eventVO.isPhaseEvent()
                && phase.getPhaseNum() != Phase.UNSPECIFIED_PHASE_NUM
                && eventVO.getParameter() == phase.getPhaseNum()) {
            // Only care about phase event if target phase is specified
            putPhaseEvent(eventVO);
            detectorOnHolder.putEvent(eventVO, currentBin, phase);
        }

        if (eventVO.getEvent() == PerfLogEventVO.Event.DETECTOR_ON) {
            putDetectorOn(eventVO);
        }

        // Forward events to sub-aggregators if target phase is unspecified
        if (phase.getPhaseNum() == Phase.UNSPECIFIED_PHASE_NUM && subAggrArr != null) {
            for (int i = 0; i < MAX_PHASE_NUM; i++)
                subAggrArr[i].putEvent(eventVO);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<TrafficMetric> getSupportedMetric() {
        return staticGetSupportedMetric();
    }

    /**
     * Supported metrics:<br/>
     *
     * <ul>
     * <li>PHASE_CALL</li>
     * <li>AOG</li>
     * <li>AOG_VOLUME</li>
     * <li>AOG_PERCENT</li>
     * <li>AOY</li>
     * <li>AOY_VOLUME</li>
     * <li>AOY_PERCENT</li>
     * <li>AOR</li>
     * <li>AOR_VOLUME</li>
     * <li>AOR_PERCENT</li>
     * </ul>
     * List of supported metrics can be retrieved by
     * {@link PhaseCallAggregator#getSupportedMetric()}
     */
    @Override
    public Double getAggregatedValue(MetricSpecifier metricSpecifier) {
        TrafficMetric metric = TrafficMetric.getById(metricSpecifier.getMetricID());

        switch (metric) {
        case PHASE_CALL:
            return (double) getTotalPhaseCalls();
        case AOG:
            return (double) getTotalAog();
        case AOG_VOLUME:
            return getOverallAogVolume();
        case AOG_PERCENT:
            return getOverallAogPercent();
        case AOY:
            return (double) getTotalAoy();
        case AOY_VOLUME:
            return getOverallAoyVolume();
        case AOY_PERCENT:
            return getOverallAoyPercent();
        case AOR:
            return (double) getTotalAor();
        case AOR_VOLUME:
            return getOverallAorVolume();
        case AOR_PERCENT:
            return getOverallAorPercent();
        default:
            return null;
        }
    }

    @Override
    protected void init(int phaseNum, int[][] detCallPhases, LocalDateTime fromTime, int binSize) {
        super.init(phaseNum, detCallPhases, fromTime, binSize);
        initSubAggregators();

        this.detectorOnHolder = new DetectorOnHolder();
    }

    private void initSubAggregators() {
        if (phase.getPhaseNum() == Phase.UNSPECIFIED_PHASE_NUM && subAggrArr == null) {
            subAggrArr = new PhaseCallAggregator[MAX_PHASE_NUM];
            for (int i = 0; i < MAX_PHASE_NUM; i++) {
                subAggrArr[i] = new PhaseCallAggregator(i + 1, fromTime, binSize, null);
                // NOTE detCallPhases is set in setConfig()
            }
        }
    }

    /**
     * Should be called in {@link AbstractPhaseCallAggregator#putEvent(PerfLogEventVO)} for expected detector event. If
     * the corresponding flag in callingDetArr is true, detector event will be counted to current bin.
     *
     * @param eventVO {@link PerfLogEventVO} object
     */
    private void putDetectorOn(PerfLogEventVO eventVO) {
        // Only consider relevant detectors
        // Detector on event parameter is detector number, checking detector index
        // (detNum - 1) with callingDetArr
        // if (!callingDetArr[(int) eventVO.getParameter() - 1]) {
        //     return;
        // }

        if (!callingDetSet.contains((int)eventVO.getParameter())) {
            return;
        }

        detectorOnHolder.putEvent(eventVO, currentBin, phase);
    }

    /**
     * @return Overall duration hours between from time and to time
     */
    private double getOverallDurationHours() {
        return Duration.between(fromTime, toTime).toSeconds() / 3600.0;
    }

    public int getTotalPhaseCalls() {
        if (phase.getPhaseNum() == Phase.UNSPECIFIED_PHASE_NUM)
            return getTotalPhaseCallsAllPhases();

        int totalDetHits = 0;
        for (VolumeBinVO bin : binList) {
            totalDetHits += bin.getTotalDetHits();
        }
        return totalDetHits;
    }

    private int getTotalPhaseCallsAllPhases() {
        if (subAggrArr == null)
            return 0;

        int totalPhaseCallsAllPhases = 0;
        for (int i = 0; i < MAX_PHASE_NUM; i++)
            totalPhaseCallsAllPhases += subAggrArr[i].getTotalPhaseCalls();
        return totalPhaseCallsAllPhases;
    }

    public int getTotalAog() {
        if (phase.getPhaseNum() == Phase.UNSPECIFIED_PHASE_NUM)
            return getTotalAogAllPhases();

        int totalAog = 0;
        for (VolumeBinVO bin : binList) {
            totalAog += bin.getAogDetHits();
        }
        return totalAog;
    }

    private int getTotalAogAllPhases() {
        if (subAggrArr == null)
            return 0;

        int totalAogAllPhases = 0;
        for (int i = 0; i < MAX_PHASE_NUM; i++)
            totalAogAllPhases += subAggrArr[i].getTotalAog();
        return totalAogAllPhases;
    }

    private Double getOverallAogVolume() {
        int totalAog = getTotalAog();
        double overallDurationHours = getOverallDurationHours();
        return (overallDurationHours > 0 ? totalAog / overallDurationHours : null);
    }

    private Double getOverallAogPercent() {
        int totalAog = getTotalAog();
        int totalDetHits = getTotalPhaseCalls();
        return totalDetHits > 0 ? (double) totalAog / totalDetHits * 100.0 : null;
    }

    private int getTotalAoy() {
        if (phase.getPhaseNum() == Phase.UNSPECIFIED_PHASE_NUM)
            return getTotalAoyAllPhases();

        int totalAoy = 0;
        for (VolumeBinVO bin : binList) {
            totalAoy += bin.getAoyDetHits();
        }
        return totalAoy;
    }

    private int getTotalAoyAllPhases() {
        if (subAggrArr == null)
            return 0;

        int totalAoyAllPhases = 0;
        for (int i = 0; i < MAX_PHASE_NUM; i++)
            totalAoyAllPhases += subAggrArr[i].getTotalAoy();
        return totalAoyAllPhases;
    }

    private Double getOverallAoyVolume() {
        int totalAoy = getTotalAoy();
        double overallDurationHours = getOverallDurationHours();
        return (overallDurationHours > 0 ? totalAoy / overallDurationHours : null);
    }

    private Double getOverallAoyPercent() {
        int totalAoy = getTotalAoy();
        int totalDetHits = getTotalPhaseCalls();
        return totalDetHits > 0 ? (double) totalAoy / totalDetHits * 100.0 : null;
    }

    public int getTotalAor() {
        if (phase.getPhaseNum() == Phase.UNSPECIFIED_PHASE_NUM)
            return getTotalAorAllPhases();

        int totalAor = 0;
        for (VolumeBinVO bin : binList) {
            totalAor += bin.getAorDetHits();
        }
        return totalAor;
    }

    private int getTotalAorAllPhases() {
        if (subAggrArr == null)
            return 0;

        int totalAorAllPhases = 0;
        for (int i = 0; i < MAX_PHASE_NUM; i++)
            totalAorAllPhases += subAggrArr[i].getTotalAor();
        return totalAorAllPhases;
    }

    private Double getOverallAorVolume() {
        int totalAor = getTotalAor();
        double overallDurationHours = getOverallDurationHours();
        return (overallDurationHours > 0 ? totalAor / overallDurationHours : null);
    }

    private Double getOverallAorPercent() {
        int totalAor = getTotalAor();
        int totalDetHits = getTotalPhaseCalls();
        return totalDetHits > 0 ? (double) totalAor / totalDetHits * 100.0 : null;
    }

}
