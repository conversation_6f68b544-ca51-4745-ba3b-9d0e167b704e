package com.siemens.spm.analysis.aggregator;

import com.siemens.spm.analysis.util.IntersectionConfigUtils;
import com.siemens.spm.analysis.vo.Plan;
import com.siemens.spm.analysis.vo.moe.CycleMOEVO;
import com.siemens.spm.analysis.vo.moe.MOEAnalysisDetailViewVO;
import com.siemens.spm.analysis.vo.moe.PhaseCycleMOEVO;
import com.siemens.spm.common.shared.domaintype.alarm.trafficmetric.MetricSpecifier;
import com.siemens.spm.common.shared.domaintype.alarm.trafficmetric.TrafficMetric;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class MOECycleRingAggregator implements PerfLogEventAggregator {

    private static final List<Integer> TRANSITION_STATE_PARAM = List.of(2, 3, 4);

    private static final Map<Integer, String> TRANSITION_MAP = Map.of(
            2, "Add", 3, "Subtract", 4, "Dwell"
    );

    protected LocalDateTime fromTime;

    protected LocalDateTime toTime;

    private IntersectionConfigVO configVO;

    private final int ring;
    private Integer currentPattern;

    private int coordinatedPhase;

    @Getter
    private MOEAnalysisDetailViewVO currentDetailView;

    @Getter
    private final List<MOEAnalysisDetailViewVO> moeAnalysisDetailViewVOList;

    private final Map<Integer, PhaseTrackerAggregator> phaseTrackerAggregatorMap;

    private final Map<Integer, CoordinatePhaseTrackerAggregator> coordinatePhaseTrackerAggregatorMap;

    public MOECycleRingAggregator(int ring) {
        this.ring = ring;
        this.moeAnalysisDetailViewVOList = new ArrayList<>();
        this.phaseTrackerAggregatorMap = new HashMap<>();
        this.coordinatePhaseTrackerAggregatorMap = new HashMap<>();
    }

    @Override
    public void setConfig(IntersectionConfigVO configVO) {
        this.configVO = configVO;
        this.phaseTrackerAggregatorMap.forEach((k, v) -> v.setConfigVO(configVO));
        this.coordinatePhaseTrackerAggregatorMap.forEach((k, v) -> v.setConfigVO(configVO));
    }

    @Override
    public void putEvent(PerfLogEventVO eventVO) {
        PerfLogEventVO.Event event = eventVO.getEvent();

        switch (event) {
            // keep old logic for pattern change: 
            // pattern 3 -> pattern 254 -> pattern 2
            // ignore pattern 254 in between, allow to combine begin green between pattern 3 and 2
            case COORD_PATTERN_CHANGE -> this.currentPattern = getPattern(eventVO);

            case COORD_CYCLE_STATE_CHANGE -> {
                if (this.currentDetailView == null || 
                    this.currentPattern == null ||
                    this.currentPattern == Plan.FREE_PLAN) 
                    return;

                if (TRANSITION_STATE_PARAM.contains((int) eventVO.getParameter())) {
                    this.currentDetailView.setTransition(eventVO.getParameter() + "-" + TRANSITION_MAP.get((int) eventVO.getParameter()));
                }
            }
            case PHASE_BEGIN_GREEN -> handleNewCycleCase(eventVO);
            case PHASE_GREEN_TERMINATION,
                 PHASE_BEGIN_YELLOW_CLEARANCE,
                 PHASE_END_YELLOW_CLEARANCE,
                 PHASE_BEGIN_RED_CLEARANCE,
                 PHASE_END_RED_CLEARANCE,
                 PHASE_FORCE_OFF,
                 PHASE_GAP_OUT,
                 PHASE_MAX_OUT,
                 PEDESTRIAN_CALL_REGISTERED -> putEventToMap(eventVO);
            default -> handleTspAndPreEvent(eventVO);
        }
    }

    private void handleTspAndPreEvent(PerfLogEventVO eventVO) {
       if (this.currentDetailView == null || 
           this.currentPattern == null ||
           this.currentPattern == Plan.FREE_PLAN) 
           return;

        if (eventVO.isPriorityEvent()) {
            this.currentDetailView.setTsp(true);
        }
        if (eventVO.isPreemptionEvent()) {
            this.currentDetailView.setPreemption(true);
        }
    }

    private void putEventToMap(PerfLogEventVO eventVO) {
        if (this.currentPattern == null || this.currentPattern == Plan.FREE_PLAN) {
            return;
        }

        int currentPhase = (int) eventVO.getParameter();
        if (IntersectionConfigUtils.isCoordinatedPhase((int) eventVO.getParameter(), configVO)) {
            coordinatePhaseTrackerAggregatorMap.computeIfPresent(currentPhase, (k, v) -> {
                v.putEvent(eventVO);
                return v;
            });
        } else {
            phaseTrackerAggregatorMap.computeIfPresent(currentPhase, (k, v) -> {
                v.putEvent(eventVO);
                return v;
            });
        }
    }

    // keep it for now, but commented out
    // private void handlePatternChange(PerfLogEventVO eventVO) {
    //     // TODO: confirm with Doan if this is correct
    //     int newPattern = getPattern(eventVO);
    //     if (currentPattern != null && currentPattern != Plan.FREE_PLAN && currentPattern != newPattern) {
    //         // free or another non-free plan started, close current cycle
    //         // example: current plan/pattern = 3, new pattern = 6
    //         //          current plan/pattern = 3, new pattern = 254
    //         coordinatePhaseTrackerAggregatorMap
    //             .keySet()
    //             .forEach(phase -> {
    //                 // create fake event to close the cycle
    //                 PerfLogEventVO closeEvent = PerfLogEventVO.builder()
    //                         .event(PerfLogEventVO.Event.PHASE_BEGIN_GREEN)
    //                         .parameter(phase)
    //                         .dateTime(eventVO.getDateTime())
    //                         .build();
    //                 handleNewCycleCase(closeEvent);
    //             });
            
    //         phaseTrackerAggregatorMap
    //             .keySet()
    //             .forEach(phase -> {
    //                 // create fake event to close the cycle
    //                 PerfLogEventVO closeEvent = PerfLogEventVO.builder()
    //                         .event(PerfLogEventVO.Event.PHASE_BEGIN_GREEN)
    //                         .parameter(phase)
    //                         .dateTime(eventVO.getDateTime())
    //                         .build();
    //                 handleNewCycleCase(closeEvent);
    //             });
    //     }
       
    //     this.currentPattern = newPattern;
    // }

    private void handleNewCycleCase(PerfLogEventVO eventVO) {
        if (this.currentPattern == null || this.currentPattern == Plan.FREE_PLAN) {
            return;
        }

        int currentPhase = (int) eventVO.getParameter();
        List<Integer> phaseListByRing = IntersectionConfigUtils.scanPhasesInRing(configVO, this.currentPattern, ring);
        if (!phaseListByRing.contains(currentPhase)) {
            return;
        }
        if (IntersectionConfigUtils.isCoordinatedPhase(currentPhase, configVO)) {
            if (!this.coordinatePhaseTrackerAggregatorMap.isEmpty() && this.currentDetailView != null) {
                coordinatePhaseTrackerAggregatorMap.forEach((k,v) -> v.putEvent(eventVO));
                fillCurrentDetailView();
                this.moeAnalysisDetailViewVOList.add(getCurrentDetailView());
                this.currentDetailView = null;
            }
            initCycle(eventVO, currentPhase);
            coordinatePhaseTrackerAggregatorMap.putIfAbsent(currentPhase,
                    new CoordinatePhaseTrackerAggregator(currentPhase, this.currentPattern, this.configVO));
            coordinatePhaseTrackerAggregatorMap.get(currentPhase).putEvent(eventVO);
        } else {
            if (isValidPhase(currentPhase, eventVO.getDateTime())) {
                if (phaseTrackerAggregatorMap.containsKey(currentPhase)) {
                    phaseTrackerAggregatorMap.computeIfPresent(currentPhase, (k, v) -> {
                        v.putEvent(eventVO);
                        return v;
                    });
                } else {
                    PhaseTrackerAggregator phaseTrackerAggregator = new PhaseTrackerAggregator(currentPhase,
                            this.currentPattern,
                            this.configVO);
                    phaseTrackerAggregator.putEvent(eventVO);
                    phaseTrackerAggregatorMap.put(currentPhase, phaseTrackerAggregator);
                }
            }
        }
    }

    private boolean isValidPhase(int phase, LocalDateTime dateTime) {
        return phase == this.coordinatedPhase || (this.fromTime != null && dateTime.isAfter(this.fromTime));
    }

    private void initCycle(PerfLogEventVO eventVO, int coordinatedPhase) {
        this.fromTime = eventVO.getDateTime();
        this.coordinatedPhase = coordinatedPhase;
        this.currentDetailView = new MOEAnalysisDetailViewVO();
    }

    private int getPattern(PerfLogEventVO eventVO) {
        return (int) eventVO.getParameter();
    }

    @Override
    public void setFromTime(LocalDateTime fromTime) {
        this.fromTime = fromTime;
    }

    @Override
    public void setToTime(LocalDateTime toTime) {
        this.toTime = toTime;
    }

    private int getProgCycleGreenLength() {
        return this.configVO.getPatternInfo().getCycleLength()[currentPattern - 1];
    }

    private void fillCurrentDetailView() {
        this.currentDetailView.setPattern(this.currentPattern);
        this.currentDetailView.setFromTime(this.fromTime);
        this.toTime = coordinatePhaseTrackerAggregatorMap.values()
                .stream()
                .map(CoordinatePhaseTrackerAggregator::getPhaseCycleMOEVO)
                .map(PhaseCycleMOEVO::getToTime)
                .max(LocalDateTime::compareTo)
                .orElse(this.fromTime);
        this.currentDetailView.setToTime(toTime);
        this.currentDetailView.setPhaseCycle(new ArrayList<>(coordinatePhaseTrackerAggregatorMap.values()
                .stream()
                .map(CoordinatePhaseTrackerAggregator::getPhaseCycleMOEVO)
                .toList()));
        this.currentDetailView.addPhaseCycle(phaseTrackerAggregatorMap.values()
                .stream()
                .map(PhaseTrackerAggregator::getPhaseCycleMOEVO)
                .toList());
        this.currentDetailView.setCycle(createCycleMOEVO());
        this.phaseTrackerAggregatorMap.clear();
        this.coordinatePhaseTrackerAggregatorMap.clear();
    }

    private CycleMOEVO createCycleMOEVO() {
        CycleMOEVO cycleMOEVO = new CycleMOEVO();
        cycleMOEVO.setProgrammedLength(getProgCycleGreenLength());
        cycleMOEVO.setActualLength((int) Duration.between(this.fromTime, this.toTime).toSeconds());
        return cycleMOEVO;
    }

    private boolean isValidPattern(int pattern) {
        return pattern >= 0 && pattern <= 253;
    }

    @Override
    public void setTargetIdentifier(String targetIdentifier) {
        // not use
    }

    @Override
    public void setBinSize(int binSize) {
        // not use
    }

    @Override
    public List<TrafficMetric> getSupportedMetric() {
        return Collections.emptyList();
    } // not use

    @Override
    public Double getAggregatedValue(MetricSpecifier metricSpecifier) {
        return null;
    } // not use
}
