package com.siemens.spm.analysis.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

@Data
@EqualsAndHashCode(callSuper = false)
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AppDelayLaneChartVO extends AppDelayChartVO {

    private static final long serialVersionUID = 8088455861935569479L;

    @JsonProperty("lane_no")
    public int getLaneNo() {
        return laneIdx + 1;
    }

    @JsonProperty("movement")
    private String movement;

    @JsonIgnore
    private int laneIdx;

}
