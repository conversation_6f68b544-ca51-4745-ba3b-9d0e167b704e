package com.siemens.spm.analysis.vo.turningmovement;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.analysis.vo.Plan;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TurningMovementPlanStatVO implements Serializable {

    private static final long serialVersionUID = -2845886773186426297L;

    public TurningMovementPlanStatVO(int planNum, LocalDateTime fromTime) {
        plan = new Plan(planNum);
        this.fromTime = fromTime;
        this.total = 0L;
        this.detectorEvents = new ConcurrentHashMap<>();
        this.detectorCounts = new ConcurrentHashMap<>();
    }

    @JsonIgnore
    private Plan plan;

    @JsonProperty("plan")
    public String getPlanName() {
        return plan.getPlanName();
    }

    @JsonProperty("from_time")
    private LocalDateTime fromTime;

    @JsonProperty("to_time")
    private LocalDateTime toTime;

    @JsonProperty("total")
    private Long total;

    @JsonIgnore
    private transient Map<Integer, List<PerfLogEventVO>> detectorEvents;

    @JsonIgnore
    private transient Map<Integer, Integer> detectorCounts;

    @JsonProperty("detector_counts")
    private List<TurningMovementDetectorCountVO> detCounts;

    public void increase() {
        if (total == null) {
            total = 0L;
        }
        total++;
    }

    /**
     * Process events for this plan within its time range
     * @param events List of events to process
     * @param detectorIds Set of detector IDs to filter by
     */
    public void processEvents(List<PerfLogEventVO> events, Set<Integer> detectorIds) {
        if (events == null || events.isEmpty() || detectorIds == null || detectorIds.isEmpty()) {
            return;
        }

        // Reset counts
        this.total = 0L;
        this.detectorEvents.clear();
        this.detectorCounts.clear();

        // Pre-filter events by time range and detector IDs
        events.stream()
            .filter(event -> {
                LocalDateTime eventTime = event.getDateTime();
                return event.getEvent() == PerfLogEventVO.Event.DETECTOR_ON &&
                       !eventTime.isBefore(fromTime) &&
                       (toTime == null || eventTime.isBefore(toTime)) &&
                       detectorIds.contains((int) event.getParameter());
            })
            .forEach(event -> {
                int detectorId = (int) event.getParameter();
                
                // Add event to detector's event list
                detectorEvents.computeIfAbsent(detectorId, k -> new ArrayList<>())
                    .add(event);
                
                // Increment detector count
                detectorCounts.merge(detectorId, 1, Integer::sum);
                
                // Increment total
                this.total++;
            });

        // Convert detector counts to VO list
        this.detCounts = detectorCounts.entrySet().stream()
            .map(entry -> TurningMovementDetectorCountVO.builder()
                .detectorKey(entry.getKey())
                .detectorValue(entry.getValue())
                .build())
            .toList();
    }

}
