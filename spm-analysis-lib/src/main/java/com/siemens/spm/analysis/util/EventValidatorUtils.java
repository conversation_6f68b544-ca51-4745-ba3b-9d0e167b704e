package com.siemens.spm.analysis.util;

import java.time.LocalDateTime;

import com.siemens.spm.perflog.vo.PerfLogEventVO;

public class EventValidatorUtils {

    private EventValidatorUtils() {
    }

    /**
     * Validate {@link PerfLogEventVO} is valid or not. A event is valid if eventVO, eventVO.Event and event time not
     * null
     *
     * @param eventVO eventVO need to validate
     * @return {@code true} if event is valid. Other wise, return {@code false}
     */
    public static boolean isValid(PerfLogEventVO eventVO) {
        return eventVO != null && eventVO.getEvent() != null && eventVO.getDateTime() != null;
    }

    /**
     * Check if a event is candidate or not. A event is candidate if it is valid event and it between a range of time
     * <p>
     * NOTE: [event)
     *
     * @param eventVO  eventVO
     * @param fromTime fromTime of range
     * @param toTime   toTime of range
     * @return {@code true} if event is candidate. Other wise, return {@code false}
     * @see EventValidatorUtils#isValid(PerfLogEventVO)
     */
    public static boolean isCandidateInRange(PerfLogEventVO eventVO, LocalDateTime fromTime, LocalDateTime toTime) {
        if (!isValid(eventVO)) {
            return false;
        }

        LocalDateTime eventTime = eventVO.getDateTime();
        return !eventTime.isBefore(fromTime) && eventTime.isBefore(toTime);
    }

}
