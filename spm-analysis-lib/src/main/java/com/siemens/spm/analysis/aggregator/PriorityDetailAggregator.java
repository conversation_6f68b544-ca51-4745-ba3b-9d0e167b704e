package com.siemens.spm.analysis.aggregator;

import com.siemens.spm.analysis.domain.PriorityCycle;
import com.siemens.spm.analysis.vo.pp.PriorityCycleVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;

import javax.annotation.Nullable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Aggregator used to aggregate priority events (TSP events)
 *
 * <AUTHOR> Nguyen - <EMAIL>
 */
public class PriorityDetailAggregator extends PpAggregator {

    private final int priorityNumber;

    private final List<PriorityCycle> cycles;
    private PriorityCycle currentCycle;

    public PriorityDetailAggregator(int priorityNumber) {
        this.priorityNumber = priorityNumber;

        cycles = new ArrayList<>();
    }

    @Override
    public void putEvent(PerfLogEventVO eventVO) {
        if (!joinEventGuard(eventVO)) {
            return;
        }

        LocalDateTime eventTime = eventVO.getDateTime();
        PerfLogEventVO.Event event = eventVO.getEvent();

        // NOTE: Current implementation assume that only have 1 TspAdjEarlyGreen and 1 TspExtendGreen event in 1 cycle
        switch (event) {
        case TSP_CHECK_IN: {
            // End current cycle without TSP Check-Out
            endCurrentCycle(null);
            startCycle(eventTime);
            break;
        }
        case TSP_ADJUSTMENT_TO_EARLY_GREEN: {
            if (currentCycle != null) {
                currentCycle.setTspAdjEarlyGreen(eventTime);
            }
            break;
        }
        case TSP_ADJUSTMENT_TO_EXTEND_GREEN: {
            if (currentCycle != null) {
                currentCycle.setTspAdjExtendGreen(eventTime);
            }
            break;
        }
        case TSP_CHECK_OUT: {
            endCurrentCycle(eventTime);
            break;
        }
        default:
            // Do nothing
        }
    }

    public List<PriorityCycleVO> getCycleVOList() {
        endCurrentCycle(null);

        List<PriorityCycleVO> cycleVOList = new ArrayList<>();
        for (PriorityCycle cycle : cycles) {
            PriorityCycleVO cycleVO = PriorityCycleVO.builder()
                    .tspCheckIn(cycle.getTspCheckIn())
                    .adjEarlyGreen(cycle.getTspAdjEarlyGreen())
                    .adjExtendGreen(cycle.getTspAdjExtendGreen())
                    .tspCheckOut(cycle.getTspCheckOut())
                    .build();
            cycleVOList.add(cycleVO);
        }

        return cycleVOList;
    }

    private boolean joinEventGuard(PerfLogEventVO eventVO) {
        return eventVO != null && eventVO.isPriorityEvent() && eventVO.getParameter() == priorityNumber;
    }

    private void startCycle(LocalDateTime time) {
        currentCycle = new PriorityCycle();
        currentCycle.setTspCheckIn(time);
    }

    private void endCurrentCycle(@Nullable LocalDateTime tspCheckoutTime) {
        if (currentCycle == null) {
            return;
        }

        currentCycle.setTspCheckOut(tspCheckoutTime);

        cycles.add(currentCycle);

        currentCycle = null;
    }

}
