/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : PhaseEventVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.analysis.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Event occurs during a phase
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PhaseEventVO implements Serializable {

    private static final long serialVersionUID = -5468632062412095203L;

    @JsonIgnore
    protected PerfLogEventVO perfLogEventVO;

    /**
     * Seconds from beginning of phase
     */
    @JsonProperty("phase_time")
    protected int phaseTime;

    public PhaseEventVO(PerfLogEventVO perfLogEventVO) {
        if (perfLogEventVO == null)
            throw new IllegalArgumentException("perfLogEventVO cannot be null");
        this.perfLogEventVO = perfLogEventVO;
    }

    @JsonProperty("datetime")
    public LocalDateTime getDatetime() {
        return perfLogEventVO.getDateTime();
    }

    @JsonProperty("event")
    public PerfLogEventVO.Event getEvent() {
        return perfLogEventVO.getEvent();
    }

}
