/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : PhaseSplitAggregator.java
 * Project     : SPM Platform
 */
package com.siemens.spm.analysis.aggregator;

import com.siemens.spm.analysis.domain.Phase;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import lombok.Getter;

import java.time.Duration;

@Getter
public class PhaseSplitAggregator extends PhaseEventAggregator {

    private int phaseSplitNum;
    private long totalPhaseSplit;

    public PhaseSplitAggregator() {
        init(Phase.UNSPECIFIED_PHASE_NUM);
    }

    /**
     * @param phaseNum
     */
    public PhaseSplitAggregator(int phaseNum) {
        init(phaseNum);
    }
    
    /**
     * {@inheritDoc}
     */
    @Override
    public void putEvent(PerfLogEventVO eventVO) {
        super.putEvent(eventVO);

        if (eventVO == null)
            return;

        if (eventVO.getEvent() == PerfLogEventVO.Event.PHASE_GREEN_TERMINATION) {
            putPhaseGreenTermination(eventVO);
        }
    }

    @Override
    protected void init(int phaseNum) {
        super.init(phaseNum);
        this.phaseSplitNum = 0;
        this.totalPhaseSplit = 0;
    }

    /**
     * Only effective if there is a prior PHASE_ON event.
     * 
     * @param eventVO
     */
    private void putPhaseGreenTermination(PerfLogEventVO eventVO) {
        if (eventVO.getParameter() != phase.getPhaseNum())
            return;

        // Don't consider phase split when PHASE_ON event is not available
        // because phaseSplitNum will not be correct
        if (phase.getOnTime() == null)
            return;

        // Actual phase split from PHASE_ON
        Duration duration = Duration.between(phase.getOnTime(), eventVO.getDateTime());
        addPhaseSplit(duration.getSeconds());
    }

    /**
     * Add an implicit phase split. This method can be used when there is
     * PHASE_GREEN_TERMINATION but without prior PHASE_ON event.
     * 
     * @param phaseSplit
     */
    private void addPhaseSplit(long phaseSplit) {
        phaseSplitNum++;
        totalPhaseSplit += phaseSplit;
    }

}
