/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : BaseChartVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.analysis.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class BaseChartVO implements Serializable {

    private static final long serialVersionUID = -7215081354879666839L;

    @JsonProperty("from_time")
    protected LocalDateTime fromTime;

    @JsonProperty("to_time")
    protected LocalDateTime toTime;

}
