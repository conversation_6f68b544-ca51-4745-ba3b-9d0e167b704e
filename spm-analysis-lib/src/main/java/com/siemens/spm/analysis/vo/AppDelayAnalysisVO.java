package com.siemens.spm.analysis.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.siemens.spm.common.shared.domaintype.analysis.AnalysisType;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AppDelayAnalysisVO extends AbstractAnalysisVO<AppDelayChartVO> {

    private static final long serialVersionUID = 2579657645589552077L;

    @Override
    protected String getAnalysisType() {
        return AnalysisType.APPROACH_DELAY.getId();
    }

}
