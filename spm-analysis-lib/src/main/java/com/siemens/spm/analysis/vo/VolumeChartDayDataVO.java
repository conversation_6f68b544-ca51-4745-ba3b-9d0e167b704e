package com.siemens.spm.analysis.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VolumeChartDayDataVO implements Serializable {

    private static final long serialVersionUID = 3213072282833216238L;

    @JsonProperty("date")
    private LocalDate date;

    /**
     * Total detector hits. unit is raw count
     */
    @JsonProperty("det_hits")
    private Integer totalDetHits;

    /**
     * Average detector hits per hour. unit is raw count
     */
    @JsonIgnore
    private Integer avgDetHitPerHour;

    @JsonProperty("charts_data")
    private List<VolumeBinVO> chartDataList;

}
