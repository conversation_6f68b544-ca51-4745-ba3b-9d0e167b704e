package com.siemens.spm.analysis.aggregator;

import com.siemens.spm.analysis.domain.PedPhase;
import com.siemens.spm.analysis.domain.PedPhase.PedState;
import com.siemens.spm.analysis.util.Constants;
import com.siemens.spm.analysis.vo.PedDelayVO;
import com.siemens.spm.common.shared.domaintype.alarm.trafficmetric.MetricSpecifier;
import com.siemens.spm.common.shared.domaintype.alarm.trafficmetric.TrafficMetric;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class PedDelayAggregator extends PedPhaseEventAggregator {

    private static final List<TrafficMetric> SUPPORTED_METRIC_LIST;

    static {
        SUPPORTED_METRIC_LIST = Arrays.asList(TrafficMetric.MAX_PED_DELAY, TrafficMetric.AVG_PED_DELAY);
    }

    public static List<TrafficMetric> staticGetSupportedMetric() {
        return SUPPORTED_METRIC_LIST;
    }

    /**
     * Sub aggregators used to aggregate data of all phases (unspecified target phase)
     */
    private PedDelayAggregator[] subAggrArr;

    private final List<PedDelayVO> pedDelayVOList = new ArrayList<>();

    private PedState pedState = PedState.UNKNOWN;

    private LocalDateTime firstPushButtonTime;

    /**
     * Constructor for unspecified phase number
     */
    public PedDelayAggregator() {
        super();
        initSubAggrArr();
    }

    /**
     * Constructor for specified phase number
     */
    public PedDelayAggregator(int phaseNum) {
        super(phaseNum);
        initSubAggrArr();
    }

    @Override
    public void setConfig(IntersectionConfigVO configVO) {
        super.setConfig(configVO);

        // Set config for sub-aggregators
        if (pedPhase.getPhaseNum() == PedPhase.UNSPECIFIED_PHASE_NUM && subAggrArr != null) {
            for (var aggregator : subAggrArr) {
                aggregator.setConfig(configVO);
            }
        }
    }

    @Override
    public void putEvent(PerfLogEventVO eventVO) {
        super.putEvent(eventVO);

        // Handle events if target phase is specified
        if (pedPhase.getPhaseNum() != PedPhase.UNSPECIFIED_PHASE_NUM) {
            // Handle ped phase events
            if (eventVO.isPedestrianEvent()
                    && eventVO.getParameter() == pedPhase.getPhaseNum()) {
                putPedPhaseEvent(eventVO);
            }

            // Handle detector events
            switch (eventVO.getEvent()) {
            case PED_DETECTOR_ON:
                handlePedDetectorOn(eventVO);
                break;
            case DETECTOR_ON:
                handleDetectorOn(eventVO);
                break;
            default:
                break;
            }
        } else if (subAggrArr != null) {
            // Forward events to sub-aggregators if target phase is unspecified
            for (int i = 0; i < Constants.MAX_PHASE_NUM; i++)
                subAggrArr[i].putEvent(eventVO);
        }

    }

    private void putPedPhaseEvent(PerfLogEventVO eventVO) {
        switch (eventVO.getEvent()) {
        case PEDESTRIAN_BEGIN_WALK:
            handlePedBeginWalk(eventVO);
            break;
        case PEDESTRIAN_BEGIN_NOT_WALK, PEDESTRIAN_BEGIN_CLEARANCE, PEDESTRIAN_DARK:
            handlePedStop();
            break;
        default:
            break;
        }
    }

    private void handleDetectorOn(PerfLogEventVO eventVO) {
        // Check detector is ped detector mode or not?
        int detNumber = (int) eventVO.getParameter();
        if (detMode[detNumber - 1] == Constants.PED_DETECTOR_MODE) {
            if (pedPhase.getPhaseNum() != PedPhase.UNSPECIFIED_PHASE_NUM) {
                int[] phaseNums = detCallPhases[detNumber - 1];
                // Check phase number contains phaseNumbers or not?
                if (!validPhaseNumber(pedPhase.getPhaseNum(), phaseNums)) {
                    // Do nothing
                    return;
                }
            }
            handlePedPushButton(eventVO);
        }
    }

    private void handlePedDetectorOn(PerfLogEventVO eventVO) {
        // In case specific phase num
        if (pedPhase.getPhaseNum() != PedPhase.UNSPECIFIED_PHASE_NUM) {
            int detNumber = (int) eventVO.getParameter() + Constants.MAX_VEHICLE_PHYSICAL_DET_ID;
            int[] phaseNums = detCallPhases[detNumber - 1];
            // Check phase number contains phaseNumbers or not?
            if (!validPhaseNumber(pedPhase.getPhaseNum(), phaseNums)) {
                // Do nothing
                return;
            }
        }
        // Handle ped push button
        handlePedPushButton(eventVO);
    }

    private boolean validPhaseNumber(int inputPhase, int[] phaseNums) {
        for (int phaseNum : phaseNums) {
            if (inputPhase == phaseNum) {
                return true;
            }
        }

        return false;
    }

    @Override
    public List<TrafficMetric> getSupportedMetric() {
        return SUPPORTED_METRIC_LIST;
    }

    @Override
    public Double getAggregatedValue(MetricSpecifier metricSpecifier) {
        TrafficMetric metric = TrafficMetric.getById(metricSpecifier.getMetricID());

        switch (metric) {
        case MAX_PED_DELAY:
            return getMaxPedDelay();
        case AVG_PED_DELAY:
            return getAvgPedDelay();
        default:
            return null;
        }
    }

    @Override
    public void setTargetIdentifier(String targetIdentifier) {
        super.setTargetIdentifier(targetIdentifier);
        initSubAggrArr();
    }

    private void initSubAggrArr() {
        if (pedPhase.getPhaseNum() == PedPhase.UNSPECIFIED_PHASE_NUM && subAggrArr == null) {
            subAggrArr = new PedDelayAggregator[Constants.MAX_PHASE_NUM];
            for (int i = 0; i < Constants.MAX_PHASE_NUM; i++) {
                subAggrArr[i] = new PedDelayAggregator(i + 1);
            }
        }
    }

    private double getAvgPedDelay() {
        if (pedPhase.getPhaseNum() == PedPhase.UNSPECIFIED_PHASE_NUM) {
            return getTotalAvgPedDelay();
        }

        return calculateAvgPedDelay(pedDelayVOList);
    }

    private double getTotalAvgPedDelay() {
        List<PedDelayVO> totalPedDelayVOList = new ArrayList<>();
        if(subAggrArr != null) {
            for (var aggregator : subAggrArr) {
                totalPedDelayVOList.addAll(aggregator.getPedDelayVOs());
            }
        }

        return calculateAvgPedDelay(totalPedDelayVOList);
    }

    private double calculateAvgPedDelay(List<PedDelayVO> pedDelayVOs) {
        double avgPedDelay = 0;
        if (!pedDelayVOs.isEmpty()) {
            long sumPedDelay = 0;

            for (PedDelayVO pedDelayVO : pedDelayVOs) {
                sumPedDelay += pedDelayVO.getPedDelayDuration();
            }

            avgPedDelay = (double) sumPedDelay / pedDelayVOs.size();
        }
        return avgPedDelay;
    }

    private double getMaxPedDelay() {
        if (pedPhase.getPhaseNum() == PedPhase.UNSPECIFIED_PHASE_NUM) {
            return getTotalMaxPedDelay();
        }

        double result = 0; // default value = 0 if have no ped delay information
        if (!pedDelayVOList.isEmpty()) {
            result = pedDelayVOList.stream()
                    .mapToLong(PedDelayVO::getPedDelayDuration)
                    .max()
                    .orElse(0);
        }

        return result;
    }

    private double getTotalMaxPedDelay() {
        double maxValue = 0;
        if (subAggrArr != null) {
            for (var aggregator : subAggrArr) {
                if (maxValue < aggregator.getMaxPedDelay()) {
                    maxValue = aggregator.getMaxPedDelay();
                }
            }
        }
        return maxValue;
    }

    private void handlePedStop() {
        pedState = PedState.PED_STOP;
    }

    private void handlePedPushButton(PerfLogEventVO eventVO) {
        // Only handle the first button push
        if (pedState != PedState.PED_WALK && firstPushButtonTime == null) {
            firstPushButtonTime = eventVO.getDateTime();
        }

    }

    /**
     * Retrieve all pedDelayVOs in a phase
     *
     * @return
     */
    public List<PedDelayVO> getPedDelayVOs() {
        return pedDelayVOList;
    }

    private void handlePedBeginWalk(PerfLogEventVO eventVO) {
        if (pedState != PedState.PED_WALK && firstPushButtonTime != null) {
            // NOTE: Pedestrian Delay = Start of Walk Interval Time – First Button Push
            // Time.
            PedDelayVO pedDelayVO = PedDelayVO.builder()
                    .pedDelayTime(eventVO.getDateTime())
                    .pedDelayDuration(Duration.between(firstPushButtonTime, eventVO.getDateTime()).getSeconds())
                    .build();
            pedDelayVOList.add(pedDelayVO);

            // Update pedestrian status and reset first push button time
            pedState = PedState.PED_WALK;
            firstPushButtonTime = null;
        }
    }

    /**
     * Retrieve all pedDelayVOs in a plan of a phase
     *
     * @param fromTime
     * @param toTime
     * @return
     */
    public List<PedDelayVO> getPedDelayInPlan(LocalDateTime fromTime, LocalDateTime toTime) {
        return pedDelayVOList.stream()
                .filter(pedDelayVO -> !pedDelayVO.getPedDelayTime().isBefore(fromTime)
                        && !pedDelayVO.getPedDelayTime().isAfter(toTime))
                .toList();
    }

}
