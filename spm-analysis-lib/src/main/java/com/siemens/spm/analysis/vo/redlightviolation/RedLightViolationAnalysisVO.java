package com.siemens.spm.analysis.vo.redlightviolation;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.siemens.spm.analysis.vo.AbstractAnalysisVO;
import com.siemens.spm.common.shared.domaintype.analysis.AnalysisType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RedLightViolationAnalysisVO extends AbstractAnalysisVO<RedLightViolationChartVO> {
    @Override
    protected String getAnalysisType() {
        return AnalysisType.RED_LIGHT_VIOLATION.getId();
    }
}
