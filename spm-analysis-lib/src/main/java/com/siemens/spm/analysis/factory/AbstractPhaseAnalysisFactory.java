/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AorAnalysisFactory.java
 * Project     : SPM Platform
 */
package com.siemens.spm.analysis.factory;

import com.siemens.spm.analysis.exception.ChartBuilderInitializationException;
import com.siemens.spm.analysis.exception.ChartInitializationException;
import com.siemens.spm.analysis.exception.InvalidPerfLogException;
import com.siemens.spm.analysis.util.IntersectionConfigUtils;
import com.siemens.spm.analysis.vo.AbstractAnalysisVO;
import com.siemens.spm.analysis.vo.PhaseChartVO;
import com.siemens.spm.analysis.vo.splitmonitor.SkippedInterval;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.PerfLogBundleVO;
import com.siemens.spm.perflog.vo.PerfLogChunkVO;
import com.siemens.spm.perflog.vo.PerfLogGapVO;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.InvocationTargetException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
public abstract class AbstractPhaseAnalysisFactory<T_ANALYSIS extends AbstractAnalysisVO<T_CHART>, T_CHART extends PhaseChartVO>
        extends AbstractAnalysisFactory<T_ANALYSIS, T_CHART> {
    
    @Getter
    @Setter
    protected Long initialPattern;

    protected Map<Integer, LinkedList<SkippedInterval>> skippedIntervalsMap = Map.of();

    @Override
    protected void buildCharts(LocalDateTime fromTime,
                               LocalDateTime toTime,
                               PerfLogBundleVO perfLogBundleVO,
                               Class<T_CHART> chartClass)
            throws InvalidPerfLogException, ChartBuilderInitializationException, ChartInitializationException {
        if (perfLogBundleVO == null || analysisVO == null)
            throw new IllegalArgumentException();

        Map<String, IntersectionConfigVO> intConfigVOMap = perfLogBundleVO.getIntConfigs();

        List<PerfLogChunkVO> perfLogChunkVOList = perfLogBundleVO.getPerfLogChunks();
        if (intConfigVOMap == null) {
            throw new InvalidPerfLogException("No intersection config");
        }

        if (perfLogChunkVOList == null) {
            throw new InvalidPerfLogException("No PerfLog chunk");
        }

        // All available phases
        Set<Integer> phaseSet = IntersectionConfigUtils.scanPhases(intConfigVOMap.values());

        List<PerfLogGapVO> perfLogGaps = analysisVO.getPerfLogGapList();

        if(!CollectionUtils.isEmpty(perfLogGaps)){
            List<Pair<LocalDateTime, LocalDateTime>> gapsRanges = perfLogGaps.stream()
                    .map(g -> Pair.of(g.getFromTime(), g.getToTime())).toList();
            if (initialPattern != null) {
                skippedIntervalsMap = SkippedPhasesScanner.scan(perfLogBundleVO, initialPattern, phaseSet, gapsRanges);
            }
        }
        // Create charts
        createCharts(fromTime, toTime, perfLogChunkVOList, intConfigVOMap, phaseSet, chartClass);
    }

    protected void createCharts(LocalDateTime fromTime, LocalDateTime toTime, List<PerfLogChunkVO> perfLogChunkVOList,
                                Map<String, IntersectionConfigVO> intConfigVOMap, Set<Integer> phaseSet,
                                Class<T_CHART> chartClass)
            throws ChartInitializationException, ChartBuilderInitializationException {
        // Create chartVOMap: phase number -> chart VO
        HashMap<Integer, T_CHART> chartVOMap = new HashMap<>();

        // Set time range to each chart
        for (Integer phaseNum : phaseSet) {
            T_CHART chartVO;
            try {
                chartVO = chartClass.getDeclaredConstructor().newInstance();
            } catch (InstantiationException | IllegalAccessException |
                     InvocationTargetException | NoSuchMethodException e) {
                log.error("Error occur when creating instance of ChartVO class", e);
                throw new ChartInitializationException(e);
            }

            chartVO.setPhase(phaseNum);
            chartVO.setFromTime(fromTime);
            chartVO.setToTime(toTime);
            chartVOMap.put(phaseNum, chartVO);
        }

        // Scan PerfLog to build other chart data
        scanPerfLog(fromTime, toTime, perfLogChunkVOList, intConfigVOMap, chartVOMap);

        for (T_CHART chartVO : chartVOMap.values()) {
            analysisVO.addChart(chartVO);
        }
    }

    /**
     * Scan perflog from list of {@link PerfLogChunkVO} and config then create chart corresponding each phase and put
     * into {@code Map<Integer, T_CHART> chartVOMap}>
     *
     * @param fromTime           start time scan perflog event
     * @param toTime             to time scan perflog event
     * @param perfLogChunkVOList list of {@link PerfLogChunkVO}
     * @param intConfigVOMap     {@code Map<String, IntersectionConfigVO> chartVOMap} corresponding intersectionConfigId
     *                           and intersectionConfig
     * @param chartVOMap         phase number -> chartVO
     * @throws ChartBuilderInitializationException if got error while building chartBuilder
     */
    protected abstract void scanPerfLog(LocalDateTime fromTime,
                                        LocalDateTime toTime,
                                        List<PerfLogChunkVO> perfLogChunkVOList,
                                        Map<String, IntersectionConfigVO> intConfigVOMap,
                                        Map<Integer, T_CHART> chartVOMap) throws ChartBuilderInitializationException;

}
