package com.siemens.spm.analysis.aggregator;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import com.siemens.spm.analysis.domain.PedPhase;
import com.siemens.spm.analysis.domain.Phase;
import com.siemens.spm.analysis.domain.PhaseColor;
import com.siemens.spm.analysis.util.Constants;
import com.siemens.spm.analysis.util.EventValidatorUtils;
import com.siemens.spm.analysis.vo.redlightviolation.RedLightViolationBinVO;
import com.siemens.spm.common.shared.domaintype.alarm.trafficmetric.MetricSpecifier;
import com.siemens.spm.common.shared.domaintype.alarm.trafficmetric.TrafficMetric;
import com.siemens.spm.common.util.ListUtil;
import com.siemens.spm.perflog.vo.DetectorVO.DetectorType;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class RedLightViolationPhaseAggregator extends PhaseEventAggregator {

    private static final List<TrafficMetric> SUPPORTED_METRICS = List.of(
            TrafficMetric.RED_LIGHT_VIOLATION_COUNT
    );

    public static List<TrafficMetric> staticGetSupportedMetric() {
        return SUPPORTED_METRICS;
    }

    private static final int BIN_SIZE_900 = 900;

    private LocalDateTime fromTime;

    private int binSize;

    private RedLightViolationBinVO currentBin;

    @Getter
    private List<RedLightViolationBinVO> binVOList;

    private Set<Integer> rlvDetNumbers = Set.of();
    private Set<Integer> stopBarDetNumbers = Set.of();

    private RedLightViolationPhaseAggregator[] subAggregatorArr;

    public RedLightViolationPhaseAggregator() {
        super(Phase.UNSPECIFIED_PHASE_NUM);
        initSubAggregatorArr();

        binVOList = new ArrayList<>();
    }

    public RedLightViolationPhaseAggregator(LocalDateTime fromTime, int binSize, int phaseNum) {
        super(phaseNum);

        this.fromTime = fromTime;
        this.binSize = binSize;

        binVOList = new ArrayList<>();
        currentBin = new RedLightViolationBinVO(fromTime, binSize);
        binVOList.add(currentBin);
    }

    public RedLightViolationPhaseAggregator(int binSize, int phaseNum) {
        super(phaseNum);
        this.binSize = binSize;
        binVOList = new ArrayList<>();
    }

    private void initSubAggregatorArr() {
        if (phase.getPhaseNum() == PedPhase.UNSPECIFIED_PHASE_NUM && subAggregatorArr == null) {
            subAggregatorArr = new RedLightViolationPhaseAggregator[Constants.MAX_PHASE_NUM];
            for (int i = 0; i < Constants.MAX_PHASE_NUM; i++) {
                subAggregatorArr[i] = new RedLightViolationPhaseAggregator(BIN_SIZE_900, i + 1);
            }
        }
    }

    @Override
    public void setConfig(IntersectionConfigVO configVO) {
        if (ListUtil.hasNoItem(Objects.requireNonNull(configVO).getApproaches())) {
            log.error("No approaches in configVO, cannot set config for RedLightViolationPhaseAggregator, configId: {}",
                    configVO.getConfigID());

            return;
        }

        // Set config for sub-aggregators
        if (phase.getPhaseNum() == Phase.UNSPECIFIED_PHASE_NUM && subAggregatorArr != null) {
            for (var aggregator : subAggregatorArr) {
                aggregator.setConfig(configVO);
            }
        } else {
            rlvDetNumbers = configVO.detNumsForPhaseFromTopo(phase.getPhaseNum(), DetectorType.RED_LIGHT_VIOLATION);
            stopBarDetNumbers = configVO.detNumsForPhaseFromTopo(phase.getPhaseNum(), DetectorType.STOP_BAR);
        }
    }

    @Override
    public void putEvent(PerfLogEventVO eventVO) {
        if (!EventValidatorUtils.isValid(eventVO)) {
            return;
        }

        // Handle events if target phase is specified
        if (phase.getPhaseNum() != Phase.UNSPECIFIED_PHASE_NUM) {
            super.putEvent(eventVO);

            fillEmptyBins(eventVO);

            if (isRedLightViolation(eventVO)) {
                currentBin.increaseViolationCount();
            }
        } else if (subAggregatorArr != null) {
            // Forward events to sub-aggregators if target phase is unspecified
            for (int i = 0; i < Constants.MAX_PHASE_NUM; i++)
                subAggregatorArr[i].putEvent(eventVO);
        }
    }

    public boolean isRedLightViolation(PerfLogEventVO eventVO) {
        // TODO: Logic to check if the event is a red light violation should be update here
        if (eventVO.getEvent() == PerfLogEventVO.Event.DETECTOR_ON) {
            int detNum = (int) eventVO.getParameter();

            // for the case 1 detector is used for multiple phases
            // red light violation detector or if there is no red light violation detector, then stop bar detector
            boolean isValidDet = rlvDetNumbers.contains(detNum) 
                                    || (rlvDetNumbers.isEmpty() && stopBarDetNumbers.contains(detNum));
            return phase.getColor() == PhaseColor.RED && isValidDet;
        }

        return false;
    }

    private void fillEmptyBins(PerfLogEventVO eventVO) {
        if (currentBin == null) {
            createNewBin(fromTime);
        }

        while (!currentBin.getToTime().isAfter(eventVO.getDateTime())) {
            createNewBin(currentBin.getToTime());
        }
    }

    private void createNewBin(LocalDateTime fromTime) {
        currentBin = new RedLightViolationBinVO(fromTime, binSize);
        binVOList.add(currentBin);
    }

    private int getTotalViolationCount() {
        if (phase.getPhaseNum() == Phase.UNSPECIFIED_PHASE_NUM && subAggregatorArr != null) {
            int ret = 0;
            for (var aggregator : subAggregatorArr) {
                ret += aggregator.getTotalViolationCount();
            }
            return ret;
        }
        return binVOList.stream()
                .map(RedLightViolationBinVO::getViolationCount)
                .filter(Objects::nonNull)
                .mapToInt(Integer::intValue)
                .sum();
    }

    @Override
    public Double getAggregatedValue(MetricSpecifier metricSpecifier) {
        if (metricSpecifier == null || metricSpecifier.getMetricID() == null) {
            return null;
        }

        TrafficMetric metric = TrafficMetric.getById(metricSpecifier.getMetricID());
        if (metric == TrafficMetric.RED_LIGHT_VIOLATION_COUNT) {
            return (double) getTotalViolationCount();
        }

        return null;
    }

    @Override
    public List<TrafficMetric> getSupportedMetric() {
        return staticGetSupportedMetric();
    }

    @Override
    public void setFromTime(LocalDateTime fromTime) {
        this.fromTime = fromTime;
        if (phase.getPhaseNum() == Phase.UNSPECIFIED_PHASE_NUM && subAggregatorArr != null) {
            for (var aggregator : subAggregatorArr) {
                aggregator.setFromTime(fromTime);
            }
        }
    }

    @Override
    public void setBinSize(int binSize) {
        this.binSize = binSize;
    }
}
