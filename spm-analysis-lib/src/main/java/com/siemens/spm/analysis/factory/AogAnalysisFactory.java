package com.siemens.spm.analysis.factory;

import com.siemens.spm.analysis.exception.AnalysisInitializationException;
import com.siemens.spm.analysis.exception.ChartBuilderInitializationException;
import com.siemens.spm.analysis.exception.InvalidPerfLogException;
import com.siemens.spm.analysis.vo.AogAnalysisVO;
import com.siemens.spm.analysis.vo.AogChartVO;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.PerfLogBundleVO;
import com.siemens.spm.perflog.vo.PerfLogChunkVO;
import com.siemens.spm.perflog.vo.PerfLogGapVO;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public class AogAnalysisFactory extends ArrivalOnAnalysisFactory<AogAnalysisVO, AogChartVO, AogChartBuilder> {

    public AogAnalysisFactory(int binSize) {
        super(binSize, AogChartVO.class, AogChartBuilder.class);
    }

    public AogAnalysisVO createAnalysis(LocalDateTime fromTime,
                                        LocalDateTime toTime,
                                        PerfLogBundleVO perfLogBundleVO,
                                        List<PerfLogGapVO> perfLogGapVOList)
            throws InvalidPerfLogException, AnalysisInitializationException {

        return createAnalysis(fromTime, toTime,
                perfLogBundleVO, perfLogGapVOList,
                AogAnalysisVO.class,
                AogChartVO.class);
    }

    @Override
    protected void scanPerfLog(LocalDateTime fromTime,
                               LocalDateTime toTime,
                               List<PerfLogChunkVO> perfLogChunkVOList,
                               Map<String, IntersectionConfigVO> intConfigVOMap,
                               Map<Integer, AogChartVO> chartVOMap)
            throws ChartBuilderInitializationException {
        super.scanPerfLog(fromTime, toTime, perfLogChunkVOList, intConfigVOMap, chartVOMap);
        
        for (AogChartVO chartVO : chartVOMap.values()) {
            chartVO.setSkippedPhaseIntervals(this.skippedIntervalsMap.get(chartVO.getPhase()));   
        }
    }

}
