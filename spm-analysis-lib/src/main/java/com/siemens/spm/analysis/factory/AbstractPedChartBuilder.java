package com.siemens.spm.analysis.factory;

import com.siemens.spm.analysis.domain.PedPhase;
import com.siemens.spm.perflog.vo.PerfLogEventVO;

import java.time.Duration;

public abstract class AbstractPedChartBuilder {

    protected PedPhase pedPhase;

    protected AbstractPedChartBuilder(int phaseNum) {
        pedPhase = new PedPhase(phaseNum);
    }

    /**
     * Finalize chart building and output to referenced chartVO
     */
    public abstract void build();

    /**
     * @param eventVO
     */
    protected void putPedEvent(PerfLogEventVO eventVO) {
        pedPhase.updatePedState(eventVO);
    }

    public int computePhaseTime(PerfLogEventVO eventVO) {
        if (eventVO == null)
            throw new IllegalArgumentException("eventVO must not be null");

        Duration duration = pedPhase.getOnTime() != null
                ? Duration.between(pedPhase.getOnTime(), eventVO.getDateTime())
                : Duration.ZERO;

        return (int) duration.getSeconds();
    }

    /**
     * Handle phase event and update phase state machine
     *
     * @param eventVO
     * @throws IllegalArgumentException if eventVO is null
     */
    public void putEvent(PerfLogEventVO eventVO) {
        if (eventVO == null)
            throw new IllegalArgumentException("eventVO must not be null");

        if (eventVO.isPedestrianEvent()) {
            putPedEvent(eventVO);
        }
    }

}
