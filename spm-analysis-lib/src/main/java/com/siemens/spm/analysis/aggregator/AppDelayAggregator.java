package com.siemens.spm.analysis.aggregator;

import com.siemens.spm.analysis.domain.Phase;
import com.siemens.spm.analysis.util.AppDelayUtil;
import com.siemens.spm.analysis.vo.AppDelayPlanStatisticsVO;
import com.siemens.spm.analysis.vo.AppDelayVehicleVO;
import com.siemens.spm.common.shared.domaintype.alarm.trafficmetric.MetricSpecifier;
import com.siemens.spm.common.shared.domaintype.alarm.trafficmetric.TrafficMetric;
import com.siemens.spm.perflog.vo.DetectorVO;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.LaneVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import com.siemens.spm.perflog.vo.PhaseVO;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.data.util.Pair;

/**
 * Aggregator for approach delay calculation in bin interval
 *
 * <AUTHOR> Dowideit (<EMAIL>)
 * <AUTHOR> Nguyen - <EMAIL>
 */
@Slf4j
public class AppDelayAggregator extends PhaseEventAggregator {

    private static final List<TrafficMetric> SUPPORTED_METRICS = List.of(
            TrafficMetric.AVG_APP_DELAY
    );

    public static List<TrafficMetric> staticGetSupportedMetric() {
        return SUPPORTED_METRICS;
    }

    private static final int BIN_SIZE_900 = 900;
    private static final int MAX_PHASE_NUM = 16;

    private int binSize;

    protected LocalDateTime fromTime;
    protected LocalDateTime toTime;

    protected final List<AppDelaySupporter> supporters = new ArrayList<>();

    /**
     * Sub aggregators used to aggregate data of all phases (unspecified target phase)
     */
    private AppDelayAggregator[] subAggregators;

    /**
     * No-args constructor using for Unspecified phase num and create aggregator instance using reflection
     * Used by rule evaluation service
     */
    public AppDelayAggregator() {
        init(null, null, BIN_SIZE_900, Phase.UNSPECIFIED_PHASE_NUM);

        initSubAggregators();
    }

    public AppDelayAggregator(LocalDateTime fromTime,
                              LocalDateTime toTime,
                              int binSize, int phaseNumber) {
        init(fromTime, toTime, binSize, phaseNumber);
    }

    @Override
    public void setTargetIdentifier(String targetIdentifier) {
        super.setTargetIdentifier(targetIdentifier);

        initSubAggregators();
    }

    @Override
    public void setBinSize(int binSize) {
        this.binSize = binSize;

        if (phase.getPhaseNum() == Phase.UNSPECIFIED_PHASE_NUM && subAggregators != null) {
            for (AppDelayAggregator subAggregator : subAggregators) {
                subAggregator.setBinSize(binSize);
            }
        }
    }

    @Override
    public void setFromTime(LocalDateTime fromTime) {
        this.fromTime = fromTime;

        if (phase.getPhaseNum() == Phase.UNSPECIFIED_PHASE_NUM && subAggregators != null) {
            for (AppDelayAggregator subAggregator : subAggregators) {
                subAggregator.setFromTime(fromTime);
            }
        }
    }

    @Override
    public void setToTime(LocalDateTime toTime) {
        this.toTime = toTime;

        if (phase.getPhaseNum() == Phase.UNSPECIFIED_PHASE_NUM && subAggregators != null) {
            for (AppDelayAggregator subAggregator : subAggregators) {
                subAggregator.setToTime(toTime);
            }
        }
    }

    @Override
    public void setConfig(IntersectionConfigVO configVO) {
        super.setConfig(configVO);

        Pair<PhaseVO, Double> phasePair = AppDelayUtil.identifyPhase(configVO, phase.getPhaseNum());
        if (phasePair == null) {
            return;
        }

        PhaseVO phaseVO = phasePair.getFirst();
        if (phaseVO.getLanes() != null) {
            // Default it should be phasing
            for (int laneIdx = 0; laneIdx < phaseVO.getLanes().size(); laneIdx++) {
                Optional<AppDelaySupporter> supporterOptional = createAppDelaySupporter(phaseVO, phasePair.getSecond(), laneIdx);
                if (supporterOptional.isPresent()) {
                    AppDelaySupporter supporter = supporterOptional.get();
                    supporters.add(supporter);
                    supporter.setConfig(configVO, phase, laneIdx);
                }
            }
        }

        if (phase.getPhaseNum() == Phase.UNSPECIFIED_PHASE_NUM && subAggregators != null) {
            for (AppDelayAggregator subAggregator : subAggregators) {
                subAggregator.setConfig(configVO);
            }
        }
    }

    @Override
    public List<TrafficMetric> getSupportedMetric() {
        return SUPPORTED_METRICS;
    }

    @Override
    public Double getAggregatedValue(MetricSpecifier metricSpecifier) {
        if (metricSpecifier != null &&
                metricSpecifier.getMetricID() != null &&
                TrafficMetric.getById(metricSpecifier.getMetricID()) == TrafficMetric.AVG_APP_DELAY) {
            return getAvgAppDelay();
        }

        return null;
    }

    @Override
    public void putEvent(PerfLogEventVO eventVO) {
        super.putEvent(eventVO);

        // Forward event to sub aggregator
        if (phase.getPhaseNum() == Phase.UNSPECIFIED_PHASE_NUM && subAggregators != null) {
            for (AppDelayAggregator subAggregator : subAggregators) {
                subAggregator.putEvent(eventVO);
            }
        } else {
            // Filter out events which not relevant to the target phase
            if (eventVO.isPhaseEvent()
                    && phase.getPhaseNum() != Phase.UNSPECIFIED_PHASE_NUM
                    && phase.getPhaseNum() != eventVO.getParameter()) {
                return;
            }

            supporters.forEach(supporter -> supporter.putEvent(eventVO));
        }
    }

    public void fillEndBins() {
        supporters.forEach(supporter -> supporter.fillEndBins(toTime));
    }

    public List<AppDelayVehicleVO> getAppDelayVehicleList() {
        if (supporters.isEmpty()) {
            return List.of();
        }

        Map<LocalDateTime, List<AppDelayVehicleVO>> delayVehicleMap 
                = supporters
                .stream()
                .flatMap(supporter -> supporter.getDelayVehicleList().stream())
                .collect(Collectors.groupingBy(AppDelayVehicleVO::getFromTime));

        List<AppDelayVehicleVO> delayVehicles = new ArrayList<>();
        delayVehicleMap.forEach((begin, vehicleList) -> {
            if (!vehicleList.isEmpty()) {
                double delay = 0;
                int volume = 0;

                for (AppDelayVehicleVO vehicle : vehicleList) {
                    delay += vehicle.getDelayDuration();
                    volume += vehicle.getBinVolume();
                }

                AppDelayVehicleVO first = vehicleList.get(0);
                AppDelayVehicleVO aggregatedVehicle 
                    = new AppDelayVehicleVO(begin, first.getToTime(), delay, volume);
                delayVehicles.add(aggregatedVehicle);
            }
        });

        delayVehicles.sort(Comparator.comparing(AppDelayVehicleVO::getFromTime));
        return delayVehicles;
    }

    /**
     * Get lane plan stats with maximum delay sum
     *
     * @return List of plan statistics for lane with most delay
     */
    public List<AppDelayPlanStatisticsVO> getAppDelayPlanStats() {
        return supporters.stream()
                .map(supporter -> supporter.getAppDelayPlanStats(toTime))
                .max(Comparator.comparing(
                        planStatList -> planStatList.stream()
                                .mapToDouble(AppDelayPlanStatisticsVO::getTotalTimeDelay)
                                .sum()))
                .orElse(List.of());
    }

    protected Optional<AppDelaySupporter> createAppDelaySupporter(PhaseVO phaseVO, double approachSpeedMph, int laneIdx) {
        if (phaseVO == null || phaseVO.getLanes() == null) {
            throw new IllegalArgumentException("PhaseVO needs to be not null and have a list of lanes.");
        }

        if (phaseVO.getLanes().size() <= laneIdx) {
            throw new IllegalArgumentException("PhaseVO has not enough lanes to support lane number " + laneIdx);
        }

        LaneVO laneVO = phaseVO.getLanes().get(laneIdx);

        List<DetectorVO> detectors = laneVO.getDetectors();
        if (detectors == null) {
            throw new IllegalArgumentException("Lane " + laneIdx + " has no detectors list.");
        }

        Optional<DetectorVO> advanceDetOptional = detectors.stream()
                .filter(DetectorVO::isAdvance)
                .max(Comparator.comparing(DetectorVO::getDistanceInMeters));

        if (advanceDetOptional.isEmpty()) {
            log.warn("There is no advance detector for phase {}, lane {}, movement {}", 
                phaseVO.getPhaseNumber(), 
                laneIdx, 
                laneVO.getMovement());
            return Optional.empty();
        }

        AppDelaySupporter supporter = new AppDelaySupporter(phaseVO.getPhaseNumber(),
            fromTime, binSize, approachSpeedMph, advanceDetOptional.get());

        return Optional.of(supporter);
    }

    private void init(LocalDateTime fromTime, LocalDateTime toTime,
                      int binSize, int phaseNum) {
        super.init(phaseNum);

        this.fromTime = fromTime;
        this.toTime = toTime;
        this.binSize = binSize;
    }

    private void initSubAggregators() {
        if (phase.getPhaseNum() == Phase.UNSPECIFIED_PHASE_NUM && subAggregators == null) {
            subAggregators = new AppDelayAggregator[MAX_PHASE_NUM];
            for (int phaseIdx = 0; phaseIdx < MAX_PHASE_NUM; phaseIdx++) {
                subAggregators[phaseIdx] = new AppDelayAggregator(fromTime, toTime, binSize, phaseIdx + 1);
            }
        }
    }

    private double getAvgAppDelay() {
        fillEndBins();

        if (phase.getPhaseNum() == Phase.UNSPECIFIED_PHASE_NUM && subAggregators != null) {
            return Arrays.stream(subAggregators)
                    .mapToDouble(AppDelayAggregator::getAvgAppDelay)
                    .average()
                    .orElse(0);
        }

        List<AppDelayVehicleVO> vehicleVOList = getAppDelayVehicleList();
        double delayDuration = vehicleVOList.stream()
                .mapToDouble(AppDelayVehicleVO::getDelayDuration)
                .sum();

        int totalVolume = vehicleVOList.stream()
                .mapToInt(AppDelayVehicleVO::getBinVolume)
                .sum();

        return totalVolume == 0 ? 0 : delayDuration / totalVolume;
    }

}
