package com.siemens.spm.analysis.vo.splitmonitor;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import lombok.Builder;
import lombok.Data;
import lombok.NonNull;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
public class TimeLineEventVO implements Comparable<TimeLineEventVO> {

    @JsonProperty("event")
    private String event;

    @JsonProperty("time")
    private LocalDateTime time;

    @JsonProperty("param")
    private Long param;

    @Override
    public int compareTo(@NonNull TimeLineEventVO o) {
        int compare = this.time.compareTo(o.getTime());
        if (compare == 0) {
            return param.compareTo(o.getParam());
        }
        return compare;
    }

    public enum Event {
        GAP_OUT, MAX_OUT, FORCE_OFF, PATTERN_CHANGE;

        public String name(int phase) {
            return super.name().replace("X", String.valueOf(phase));
        }

        public static String from(PerfLogEventVO.Event perfLogEvent) {
            return switch (perfLogEvent) {
                case PHASE_GAP_OUT -> GAP_OUT.name();
                case PHASE_MAX_OUT -> MAX_OUT.name();
                case PHASE_FORCE_OFF -> FORCE_OFF.name();
                case COORD_PATTERN_CHANGE -> PATTERN_CHANGE.name();
                default -> "-";
            };
        }
    }
}
