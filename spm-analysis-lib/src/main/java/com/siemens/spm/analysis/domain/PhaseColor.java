/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : PhaseColor.java
 * Project     : SPM Platform
 */
package com.siemens.spm.analysis.domain;

public enum PhaseColor {
    UNKNOWN,
    GREEN,
    YELLOW,
    RED;

    /**
     * @param phaseColor phase color
     * @return {@code true} if color is already known as {@code GREEN, YELLOW, RED}. Otherwise, return {@code false}
     */
    public static boolean isColorAlready(PhaseColor phaseColor) {
        return !UNKNOWN.equals(phaseColor);
    }

    public int getOffset(int splitTime) {
        switch (this) {
        case RED:
            return 1;
        case YELLOW:
            return 4;
        case GREEN:
        default:
            return splitTime - 5;
        }
    }

}
