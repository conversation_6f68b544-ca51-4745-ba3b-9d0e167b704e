/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : PhaseChartVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.analysis.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@EqualsAndHashCode(callSuper = false)
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public abstract class PhaseChartVO extends BaseChartVO {

    private static final long serialVersionUID = -9083115728277098737L;

    /**
     * Phase number, from 1
     */
    @JsonProperty("phase")
    private int phase;

}
