package com.siemens.spm.analysis.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

@Data
@EqualsAndHashCode(callSuper = false)
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AppDelayPhaseChartVO extends AppDelayChartVO {

    private static final long serialVersionUID = -6615679353278360885L;

}
