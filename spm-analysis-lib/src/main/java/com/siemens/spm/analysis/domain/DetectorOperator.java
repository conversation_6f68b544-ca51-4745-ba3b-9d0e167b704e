package com.siemens.spm.analysis.domain;

import lombok.Getter;

import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

@Getter
public enum DetectorOperator {
    EQ("=="),
    GT(">"),
    LT("<"),
    GTE(">="),
    LTE("<="),
    NONE("NONE");

    private final String value;

    DetectorOperator(String value) {
        this.value = value;
    }

    private static final Map<String, DetectorOperator> OPERATOR_MAP = new HashMap<>();

    static {
        for (DetectorOperator operator : DetectorOperator.values()) {
            OPERATOR_MAP.put(operator.getValue(), operator);
        }
    }

    public static DetectorOperator resolve(String value) {
        value = value.toUpperCase();
        if(!StringUtils.hasText(value)) {
            return null;
        }
        return OPERATOR_MAP.get(value);
    }
}
