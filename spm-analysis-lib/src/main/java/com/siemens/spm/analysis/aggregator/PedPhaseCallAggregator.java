/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : PedPhaseCallAggregator.java
 * Project     : SPM Platform
 */
package com.siemens.spm.analysis.aggregator;

import com.siemens.spm.analysis.domain.Phase;
import com.siemens.spm.analysis.util.Constants;
import com.siemens.spm.analysis.vo.VolumeBinVO;
import com.siemens.spm.common.shared.domaintype.alarm.trafficmetric.MetricSpecifier;
import com.siemens.spm.common.shared.domaintype.alarm.trafficmetric.TrafficMetric;
import com.siemens.spm.perflog.vo.DetectorInfoVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * Aggregate PED_DETECTOR_ON events calling a specific phase to bins
 */
public class PedPhaseCallAggregator extends AbstractPhaseCallAggregator {

    private static final List<TrafficMetric> SUPPORTED_METRIC_LIST;

    static {
        SUPPORTED_METRIC_LIST = Collections.singletonList(
                TrafficMetric.PED_PHASE_CALL);
    }

    public static List<TrafficMetric> staticGetSupportedMetric() {
        return SUPPORTED_METRIC_LIST;
    }

    public PedPhaseCallAggregator() {
        init(Phase.UNSPECIFIED_PHASE_NUM, null, null, VolumeBinVO.DEFAULT_BIN_SIZE);
    }

    /**
     * @param phaseNum      Phase number
     * @param fromTime      Beginning time for the aggregation
     * @param binSize       Size of volume bins in seconds, from {@link VolumeBinVO}
     * @param detCallPhases Detector configuration from {@link DetectorInfoVO}
     */
    public PedPhaseCallAggregator(int phaseNum, LocalDateTime fromTime, int binSize, int[][] detCallPhases) {
        init(phaseNum, detCallPhases, fromTime, binSize);
    }

    /**
     * {@inheritDoc} Accepting additional events:</br>
     * <ul>
     * <li>DETECTOR_ON</li>
     * </ul>
     */
    @Override
    public void putEvent(PerfLogEventVO eventVO) {
        super.putEvent(eventVO);

        if (eventVO == null) {
            return;
        }
        // Fill empty bins
        fillBins(eventVO.getDateTime());

        // Make sure event is still in current currentBin. Ignore event outside current bin
        // NOTE: [fromTime, toTime)
        if (!eventDateTimeGuard(eventVO)) {
            return;
        }

        if (eventVO.isPhaseEvent() && eventVO.getParameter() == phase.getPhaseNum()
                && phase.getPhaseNum() != Phase.UNSPECIFIED_PHASE_NUM) {
            // Only care about phase event if target phase is specified
            putPhaseEvent(eventVO);
        }

        if (eventVO.getEvent() == PerfLogEventVO.Event.PED_DETECTOR_ON) {
            putPedDetectorOn(eventVO);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<TrafficMetric> getSupportedMetric() {
        return staticGetSupportedMetric();
    }

    /**
     * Supported metrics:<br/>
     *
     * <ul>
     * <li>PED_PHASE_CALL</li>
     * </ul>
     * List of supported metrics can be retrieved by
     * {@link PedPhaseCallAggregator#getSupportedMetric()}
     */
    @Override
    public Double getAggregatedValue(MetricSpecifier metricSpecifier) {
        TrafficMetric metric = TrafficMetric.getById(metricSpecifier.getMetricID());

        if (metric == TrafficMetric.PED_PHASE_CALL) {
            return (double) getPedPhaseCallCount();
        }
        return null;
    }

    /**
     * NOTE: Event 90 parameter is physical pedestrian detector number, starting from 1. The pedestrian physical
     * detector number has to be added with {@link Constants#MAX_VEHICLE_PHYSICAL_DET_ID} to get logical detector
     * number, which can be used to call phases.
     *
     * @param eventVO eventVO
     */
    private void putPedDetectorOn(PerfLogEventVO eventVO) {
        int logicalDetID = (int) (eventVO.getParameter() + Constants.MAX_VEHICLE_PHYSICAL_DET_ID);
        // Only consider relevant detectors
        // if (!callingDetArr[logicalDetID - 1])
        //     return;

        if (!callingDetSet.contains(logicalDetID)) {
            return;
        }

        currentBin.incDetHits(phase.getColor());
    }

    private int getPedPhaseCallCount() {
        int totalDetHits = 0;
        for (VolumeBinVO bin : binList) {
            totalDetHits += bin.getTotalDetHits();
        }
        return totalDetHits;
    }

}
