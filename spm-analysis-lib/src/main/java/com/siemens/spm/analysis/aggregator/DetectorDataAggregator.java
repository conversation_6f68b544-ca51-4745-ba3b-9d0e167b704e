package com.siemens.spm.analysis.aggregator;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import com.siemens.spm.analysis.vo.detectorreport.DetectorDataVO;
import com.siemens.spm.analysis.vo.detectorreport.DetectorPhaseDataVO;
import com.siemens.spm.common.shared.domaintype.alarm.trafficmetric.MetricSpecifier;
import com.siemens.spm.common.shared.domaintype.alarm.trafficmetric.TrafficMetric;
import com.siemens.spm.perflog.vo.ApproachVO;
import com.siemens.spm.perflog.vo.DetectorVO;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.LaneVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import com.siemens.spm.perflog.vo.PhaseVO;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DetectorDataAggregator implements PerfLogEventAggregator {

    private final int detNum;

    private final LocalDateTime fromTime;

    private final LocalDateTime toTime;

    private IntersectionConfigVO configVO;

    @Getter
    private final DetectorDataVO detectorDataVO;

    private final Map<Integer, DetectorPhaseAggregator> detectorPhaseAggregatorMap;

    public DetectorDataAggregator(int detNum, LocalDateTime fromTime, LocalDateTime toTime) {
        this.detNum = detNum;
        this.fromTime = fromTime;
        this.toTime = toTime;
        this.detectorDataVO = new DetectorDataVO();
        this.detectorPhaseAggregatorMap = new HashMap<>();
    }

    public void putEvent(PerfLogEventVO eventVO, IntersectionConfigVO configVO) {
        if (this.configVO == null) {
            this.configVO = configVO;
        }
        PerfLogEventVO.Event event = eventVO.getEvent();
        if (event == PerfLogEventVO.Event.DETECTOR_ON || event == PerfLogEventVO.Event.DETECTOR_OFF) {
            int[][] detCallPhases = this.configVO.getDetInfo().getDetCallPhases();
            List<Integer> phaseListByDet = Arrays.stream(detCallPhases[detNum - 1]).boxed().toList();
            for (Integer phase : phaseListByDet) {
                detectorPhaseAggregatorMap.putIfAbsent(phase, new DetectorPhaseAggregator(phase, fromTime, toTime));
                detectorPhaseAggregatorMap.get(phase).putEvent(eventVO);
            }
        }
    }

    private void fillDetectorType() {
        Optional<DetectorVO> matchedDetector = configVO.getApproaches().stream()
                .filter(Objects::nonNull)
                .map(ApproachVO::getPhases)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .map(PhaseVO::getLanes)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .map(LaneVO::getDetectors)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .filter(detectorVO -> detectorVO.getDetectorNumber() != null
                        && detectorVO.getDetectorNumber().equals(detNum)
                )
                .findFirst();

        matchedDetector.ifPresent(detectorVO -> {
            detectorDataVO.setDetectorType(detectorVO.getType().getName());
            if (DetectorVO.DetectorType.ADVANCE.equals(detectorVO.getType())) {
                detectorDataVO.setDistanceToStopBar(detectorVO.getDistance());
            }
        });
    }

    public void fillDetectorDataVO(Map<Integer, List<Double>> cycleLengthMap) {
        if (configVO.getApproaches() != null) {
            fillDetectorType();
        }
        Map<Integer, DetectorPhaseDataVO> detectorPhaseDataMap = new HashMap<>();
        detectorPhaseAggregatorMap.forEach((k, v) -> {
            v.fillPhaseData(cycleLengthMap.get(k));
            detectorPhaseDataMap.put(k, v.getDetectorPhaseDataVO());
        });
        detectorDataVO.setDetectorPhaseDataMap(detectorPhaseDataMap);
    }

    @Override
    public void setFromTime(LocalDateTime fromTime) {
        //not use
    }

    @Override
    public void setToTime(LocalDateTime toTime) {
        //not use
    }

    @Override
    public void setConfig(IntersectionConfigVO configVO) {
        this.configVO = configVO;
    }

    @Override
    public void putEvent(PerfLogEventVO eventVO) {
        //not use
    }

    @Override
    public void setTargetIdentifier(String targetIdentifier) {
        //not use
    }

    @Override
    public void setBinSize(int binSize) {
        //not use
    }

    @Override
    public List<TrafficMetric> getSupportedMetric() {
        return Collections.emptyList();
    }

    @Override
    public Double getAggregatedValue(MetricSpecifier metricSpecifier) {
        return null;
    }
}
