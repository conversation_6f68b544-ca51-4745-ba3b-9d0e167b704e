package com.siemens.spm.analysis.aggregator;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import com.siemens.spm.analysis.domain.Phase;
import com.siemens.spm.analysis.util.AppDelayUtil;
import com.siemens.spm.analysis.util.DateTimeUtil;
import com.siemens.spm.analysis.vo.AppDelayCycleVO;
import com.siemens.spm.analysis.vo.AppDelayCycleVO.AppDelayCycleInfo;
import com.siemens.spm.analysis.vo.AppDelayPlanStatisticsVO;
import com.siemens.spm.analysis.vo.AppDelayVehicleVO;
import com.siemens.spm.analysis.vo.Plan;
import com.siemens.spm.perflog.vo.ApproachVO;
import com.siemens.spm.perflog.vo.DetectorVO;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * AppDelaySupporter per (phase, advance_detector).
 */
@Slf4j
public class AppDelaySupporter {

    @Getter
    private final int phaseNum;

    private final LocalDateTime fromTime;
    private final int binSize;

    private final DetectorVO advanceDet;

    @Getter
    private final long travelTimeToStopBarMills;

    private final double speedMph;

    private AppDelayCycleVO currentCycle;
    private final List<AppDelayCycleVO> cycles = new ArrayList<>();

    private final List<AppDelayVehicleVO> binList = new ArrayList<>();

    private AppDelayPlanStatisticsVO currentPlanStat;
    private final List<AppDelayPlanStatisticsVO> planStats = new ArrayList<>();

    public AppDelaySupporter(int phaseNum,
                             LocalDateTime fromTime,
                             int binSize,
                             double approachSpeedMph,
                             DetectorVO advanceDet) {
        this.phaseNum = phaseNum;
        this.fromTime = fromTime;
        this.binSize = binSize;
        this.advanceDet = advanceDet;
        this.speedMph = (approachSpeedMph < 0.1) ? 30 : approachSpeedMph;

        this.currentPlanStat = new AppDelayPlanStatisticsVO(Plan.UNKNOWN_PLAN, fromTime);

        // NOTE: Use milliseconds as the unit for travel time to stop bar to minimize the loss of precision

        // speed [feet/seconds] = speed [miles/hour] * 5280 / 3600
        // travel time [seconds] = distance / speed [feet/seconds] 
        //                       = distance / (speed [miles/hour] * 5280 / 3600) 
        //                       = distance * 3600  / (speed [miles/hour] * 5280)
        // travel time [milliseconds] = travel time [seconds] * 1000 
        //                            = distance * 3600 * 1000 / (speed [miles/hour] * 5280)
        this.travelTimeToStopBarMills = (long) ((advanceDet.getDistance() * 3600 * 1000) / (this.speedMph * 5280));
    }

    public void putEvent(PerfLogEventVO eventVO) {
        LocalDateTime eventTime = eventVO.getDateTime();

        switch (eventVO.getEvent()) {
            case PHASE_BEGIN_GREEN:
                if (currentCycle != null) {
                    currentCycle.end(eventTime);
                    cycles.add(currentCycle);
                }
                currentCycle = AppDelayCycleVO.start(phaseNum, eventTime, travelTimeToStopBarMills);
                break;

            case PHASE_BEGIN_YELLOW_CLEARANCE, PHASE_BEGIN_RED_CLEARANCE:
                if (currentCycle != null) {
                    currentCycle.updateState(eventVO);
                }
                break;

            case DETECTOR_ON:
                handleDetOn(eventVO);
                break;

            case COORD_PATTERN_CHANGE:
                currentPlanStat.setToTime(eventVO.getDateTime());
                planStats.add(currentPlanStat);
                currentPlanStat = new AppDelayPlanStatisticsVO((int) eventVO.getParameter(), eventTime);
                break;

            default:
                break;
        }
    }

    private void handleDetOn(PerfLogEventVO eventVO) {
        if (currentCycle == null) {
            // PHASE_BEGIN_GREEN might be missed in the time range
            // E.g., user selects [12:00, 13:00] but the first event is PHASE_BEGIN_GREEN at 12:02:48
            // Still consider all det_on until the first PHASE_BEGIN_GREEN as part of the cycle
            currentCycle = AppDelayCycleVO.start(phaseNum, null, travelTimeToStopBarMills);
        }

        int activatedDetNum = (int) eventVO.getParameter();
        if (advanceDet.getDetectorNumber() == activatedDetNum) {
            currentCycle.advanceDetOn(eventVO.getDateTime());
        }
    }

    public void setConfig(IntersectionConfigVO configVO, Phase phase, int laneIdx) {
        ApproachVO approachVO = AppDelayUtil.identifyApproach(configVO, phase);
        if (approachVO == null) {
            log.debug("No approach found for phase {} Using default saturation flow.", phase.getPhaseNum());
        }
    }

    public List<AppDelayPlanStatisticsVO> getAppDelayPlanStats(LocalDateTime toTime) {
        currentPlanStat.setToTime(toTime);
        planStats.add(currentPlanStat);

        for (AppDelayPlanStatisticsVO planStat : planStats) {
            List<AppDelayCycleInfo> cycleInfoList = cycles
                .stream()
                .map(cycle -> cycle.getAppDelayCycleInfo(planStat.getFromTime(), planStat.getToTime()))
                .toList();

            double delay = cycleInfoList.stream()
                    .mapToDouble(AppDelayCycleInfo::getDelaySeconds)
                    .sum();
            planStat.setTotalTimeDelay(delay);

            int planVolume = cycleInfoList.stream()
                    .mapToInt(AppDelayCycleInfo::getDelayDetCount)
                    .sum();

            planStat.setTotalVolume(planVolume);
        }

        return planStats;
    }

    public List<AppDelayVehicleVO> getDelayVehicleList() {
        return binList;
    }

    public void fillEndBins(LocalDateTime dateTime) {
        if (currentCycle != null) {
            cycles.add(currentCycle);
            currentCycle = null;
        }

        if (binSize > 0) {
            // Divide cycles into bins from fromTime to dateTime with binSize.
            LocalDateTime iterTime = fromTime;
            while (iterTime.isBefore(dateTime)) {
                final LocalDateTime binFromTime = iterTime;
                final LocalDateTime binToTime = DateTimeUtil.getTheSooner(binFromTime.plusSeconds(binSize), dateTime);

                List<AppDelayCycleInfo> cycleInfoList = cycles
                    .stream()
                    .map(cycle -> cycle.getAppDelayCycleInfo(binFromTime, binToTime))
                    .toList();

                AppDelayVehicleVO bin = createBin(binFromTime, binToTime, cycleInfoList);
                binList.add(bin);

                iterTime = binToTime;
            }
        } else {
            for (AppDelayCycleVO cycle : cycles) {
                if (cycle.getGreenBeginTime() != null && cycle.getNextGreenBeginTime() != null) {
                    binList.add(cycle.toAppDelayVehicleVO());
                }                 
            }
        }
    }

    private AppDelayVehicleVO createBin(LocalDateTime fromTime, LocalDateTime toTime, List<AppDelayCycleInfo> cycleInfoList) {              
        double delay = cycleInfoList
                .stream()
                .mapToDouble(AppDelayCycleInfo::getDelaySeconds)
                .sum();

        int volume = cycleInfoList
                .stream()
                .mapToInt(AppDelayCycleInfo::getDelayDetCount)
                .sum();

        return new AppDelayVehicleVO(fromTime, toTime, delay, volume);
    }

}
