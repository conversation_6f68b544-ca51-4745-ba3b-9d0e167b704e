package com.siemens.spm.analysis.vo.moe;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.analysis.vo.BaseChartVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.io.Serial;
import java.util.ArrayList;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
public class MOEAnalysisDetailViewVO extends BaseChartVO {

    @Serial private static final long serialVersionUID = -1719037420626182756L;

    @JsonProperty("pattern")
    private int pattern;

    @JsonProperty("cycle")
    private CycleMOEVO cycle;

    @JsonProperty("phase_cycle")
    private List<PhaseCycleMOEVO> phaseCycle;

    @JsonProperty("transition")
    private String transition;

    @JsonProperty("TSP")
    private boolean tsp;

    @JsonProperty("PE")
    private boolean preemption;

    public MOEAnalysisDetailViewVO() {
        this.cycle = new CycleMOEVO();
        this.phaseCycle = new ArrayList<>();
    }

    @JsonIgnore
    public void addPhaseCycle(List<PhaseCycleMOEVO> phaseCycleMOEVOS) {
        if (this.phaseCycle == null) {
            this.phaseCycle = new ArrayList<>();
        }
        this.phaseCycle.addAll(phaseCycleMOEVOS);
    }
    
}
