/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AorAnalysisVO.java
 * Project     : spm-analysis-service
 */
package com.siemens.spm.analysis.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.siemens.spm.common.shared.domaintype.analysis.AnalysisType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@JsonInclude(Include.NON_NULL)
public class AorAnalysisVO extends AbstractAnalysisVO<AorChartVO> {

    private static final long serialVersionUID = -463200953629675943L;

    @Override
    protected String getAnalysisType() {
        return AnalysisType.ARRIVALS_ON_RED.getId();
    }
}
