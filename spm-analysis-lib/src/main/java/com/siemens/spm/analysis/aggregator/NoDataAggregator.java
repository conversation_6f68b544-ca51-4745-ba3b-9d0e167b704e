package com.siemens.spm.analysis.aggregator;

import com.siemens.spm.common.shared.domaintype.alarm.trafficmetric.MetricSpecifier;
import com.siemens.spm.common.shared.domaintype.alarm.trafficmetric.TrafficMetric;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO.Event;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * Aggregate NO_DATA events from no data chunks
 */
public class NoDataAggregator implements PerfLogEventAggregator {

    private static final List<TrafficMetric> SUPPORTED_METRIC_LIST;

    static {
        SUPPORTED_METRIC_LIST = Arrays.asList(TrafficMetric.NO_DATA);
    }

    public static List<TrafficMetric> staticGetSupportedMetric() {
        return SUPPORTED_METRIC_LIST;
    }

    /**
     * Count number of NO_DATA event from event lob
     */
    private int noDataCount;

    /**
     * Unused
     */
    @Override
    public void setTargetIdentifier(String targetIdentifier) {
        // this function is unused
    }

    /**
     * Unused
     */
    @Override
    public void setConfig(IntersectionConfigVO configVO) {
        // this function is unused
    }

    /**
     * Unused
     */
    @Override
    public void setFromTime(LocalDateTime fromTime) {
        // this function is unused
    }

    /**
     * Unused
     */
    @Override
    public void setBinSize(int binSize) {
        // this function is unused
    }

    /**
     * Unused
     */
    @Override
    public void setToTime(LocalDateTime toTime) {
        // this function is unused
    }

    @Override
    public void putEvent(PerfLogEventVO eventVO) {
        if (Event.NO_DATA.equals(eventVO.getEvent())) {
            noDataCount++;
        }
    }

    @Override
    public List<TrafficMetric> getSupportedMetric() {
        return SUPPORTED_METRIC_LIST;
    }

    @Override
    public Double getAggregatedValue(MetricSpecifier metricSpecifier) {
        if (metricSpecifier != null && TrafficMetric.NO_DATA.getId().equals(metricSpecifier.getMetricID())) {
            return (double) noDataCount;
        }

        return null;
    }

}
