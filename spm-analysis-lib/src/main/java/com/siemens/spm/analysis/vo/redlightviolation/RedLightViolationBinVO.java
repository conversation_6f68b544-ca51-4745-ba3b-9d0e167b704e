package com.siemens.spm.analysis.vo.redlightviolation;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.analysis.vo.DataBlockVO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RedLightViolationBinVO implements DataBlockVO, Serializable {

    private static final long serialVersionUID = -2459579617320078412L;

    @JsonProperty("from_time")
    private LocalDateTime fromTime;

    @JsonProperty("to_time")
    private LocalDateTime toTime;

    @JsonProperty("violation_count")
    private Integer violationCount;

    @JsonProperty("phase_counts")
    private List<RedLightPhaseViolationVO> phaseCounts;

    @JsonProperty("avg_trlv")
    private Double avgTRLV;

    public RedLightViolationBinVO(LocalDateTime fromTime, int binSize) {
        this.fromTime = fromTime;
        this.toTime = fromTime.plusSeconds(binSize);
        violationCount = 0;
    }

    public RedLightViolationBinVO(LocalDateTime fromTime, LocalDateTime toTime) {
        this.fromTime = fromTime;
        this.toTime = toTime;
        violationCount = 0;
    }

    public void increaseViolationCount() {
        if (violationCount == null) {
            violationCount = 0;
        }
        violationCount++;
    }
}
