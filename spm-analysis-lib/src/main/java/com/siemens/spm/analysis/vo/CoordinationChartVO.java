/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : CoordinationChartVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.analysis.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.analysis.vo.splitmonitor.SkippedInterval;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(Include.NON_NULL)
public class CoordinationChartVO extends PhaseChartVO {

    private static final long serialVersionUID = 7442401970309478610L;

    @JsonProperty("phase_changes")
    private List<PhaseChangeVO> phaseChangeList;

    @JsonProperty("det_acts")
    private List<PhaseDetectorActivityVO> detActList;

    @JsonProperty("volume_bins")
    private List<CoordinationBinVO> volumeBinList;

    @JsonProperty("plan_statistics")
    private List<CoordPlanStatisticsVO> planStatisticsList;

    @JsonProperty("skipped_intervals")
    private List<SkippedInterval> skippedPhaseIntervals;

    public CoordinationChartVO() {
        phaseChangeList = new ArrayList<>();
        detActList = new ArrayList<>();
        volumeBinList = new ArrayList<>();
        planStatisticsList = new ArrayList<>();
    }

    public void addPhaseChange(PhaseChangeVO phaseChangeVO) {
        if (phaseChangeVO == null)
            throw new IllegalArgumentException("PhaseChangeVO must not be null");

        phaseChangeList.add(phaseChangeVO);
    }

    public void addPhaseDetectorActivity(PhaseDetectorActivityVO phaseDetectorActivityVO) {
        if (phaseDetectorActivityVO == null)
            throw new IllegalArgumentException("PhaseDetectorActivityVO must not be null");

        detActList.add(phaseDetectorActivityVO);
    }

    public void addPlanStatistics(CoordPlanStatisticsVO planStatisticsVO) {
        if (planStatisticsVO == null)
            throw new IllegalArgumentException("planStatisticsVO must not be null");

        planStatisticsList.add(planStatisticsVO);
    }

    public void addListPlanStatistics(List<CoordPlanStatisticsVO> planStatistics) {
        if (planStatistics == null) {
            throw new IllegalArgumentException("planStatistics must not be null");
        }
        for (CoordPlanStatisticsVO planStatisticsVO : planStatistics) {
            addPlanStatistics(planStatisticsVO);
        }
    }

}
