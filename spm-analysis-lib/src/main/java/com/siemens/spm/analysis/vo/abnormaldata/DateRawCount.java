package com.siemens.spm.analysis.vo.abnormaldata;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.concurrent.atomic.AtomicLong;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
public class DateRawCount implements Serializable {

    private static final long serialVersionUID = 6574988079640670531L;

    @JsonProperty("data")
    private LocalDate date;

    @JsonProperty("raw_count")
    private final AtomicLong rawCount = new AtomicLong(0L);

    public DateRawCount inc() {
        rawCount.incrementAndGet();
        return this;
    }

    public DateRawCount inc(Long value) {
        rawCount.addAndGet(value);
        return this;
    }

}
