package com.siemens.spm.analysis.aggregator;

import com.siemens.spm.analysis.domain.Phase;
import com.siemens.spm.analysis.vo.detectorreport.DetectorPhaseDataVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import lombok.Getter;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;

public class DetectorPhaseAggregator extends PhaseEventAggregator {

    private final LocalDateTime fromTime;

    private final LocalDateTime toTime;

    @Getter
    private final DetectorPhaseDataVO detectorPhaseDataVO;

    private LocalDateTime detectorOnTime;

    private LocalDateTime detectorOffTime;

    public DetectorPhaseAggregator(int phase, LocalDateTime fromTime, LocalDateTime toTime) {
        this.fromTime = fromTime;
        this.toTime = toTime;
        this.phase = new Phase(phase);
        this.detectorPhaseDataVO = new DetectorPhaseDataVO(phase);
    }


    @Override
    public void putEvent(PerfLogEventVO eventVO) {
        PerfLogEventVO.Event event = eventVO.getEvent();
        switch (event) {
        case DETECTOR_ON -> {
            detectorPhaseDataVO.increaseActivation();
            this.detectorOnTime = eventVO.getDateTime();
        }
        case DETECTOR_OFF -> {
            this.detectorOffTime = eventVO.getDateTime();
            handleOccupancyTime();
        }
        default -> {
            //not use
        }
        }
    }

    private void handleOccupancyTime() {
        if (detectorOnTime == null || detectorOffTime.isBefore(detectorOnTime)) {
            return;
        }
        detectorPhaseDataVO.addOccupancyTime(Duration.between(detectorOnTime, detectorOffTime).toMillis() / 1000.0);
    }

    public void fillPhaseData(List<Double> cycleLengths) {
        detectorPhaseDataVO.aggregatePhaseData(fromTime, toTime, cycleLengths);
    }
}
