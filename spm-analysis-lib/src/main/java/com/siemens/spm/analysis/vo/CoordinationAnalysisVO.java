/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : CoordinationAnalysisVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.analysis.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.siemens.spm.common.shared.domaintype.analysis.AnalysisType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@JsonInclude(Include.NON_NULL)
public class CoordinationAnalysisVO extends AbstractAnalysisVO<CoordinationChartVO> {

    private static final long serialVersionUID = 1339057152188488589L;

    @Override
    protected String getAnalysisType() {
        return AnalysisType.COORDINATION.getId();
    }

}
