package com.siemens.spm.analysis.vo.redlightviolation;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RedLightPhaseViolationVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @JsonProperty("phase_number")
    private Integer phaseNumber;

    @JsonProperty("violation_count")
    private Integer violationCount;

    public RedLightPhaseViolationVO(Integer phaseNumber) {
        this.phaseNumber = phaseNumber;
        this.violationCount = 0;
    }

    public void increaseViolationCount() {
        if (violationCount == null) {
            violationCount = 0;
        }
        violationCount++;
    }
}
