package com.siemens.spm.analysis.vo.moe;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ActualVehiclePhaseLengthVO extends BaseAvgSignalLengthVO implements Serializable {

    @Serial private static final long serialVersionUID = 7887330354924676167L;

    @JsonIgnore
    private List<Integer> greenLengthList;

    @JsonIgnore
    private List<Integer> yellowLengthList;

    @JsonIgnore
    private List<Integer> redLengthList;

    @JsonIgnore
    public void addGreenLength(Integer greenLength) {
        if (this.greenLengthList == null) {
            this.greenLengthList = new ArrayList<>();
        }
        this.greenLengthList.add(greenLength);
    }

    @JsonIgnore
    public void addYellowLength(Integer yellowLength) {
        if (this.yellowLengthList == null) {
            this.yellowLengthList = new ArrayList<>();
        }
        this.yellowLengthList.add(yellowLength);
    }

    @JsonIgnore
    public void addRedLength(Integer redLength) {
        if (this.redLengthList == null) {
            this.redLengthList = new ArrayList<>();
        }
        this.redLengthList.add(redLength);
    }


    @JsonIgnore
    public void addGreenLengthList(List<Integer> greenLengthList) {
        if (this.greenLengthList == null) {
            this.greenLengthList = new ArrayList<>();
        }
        this.greenLengthList.addAll(greenLengthList);
    }

    @JsonIgnore
    public void addYellowLengthList(List<Integer> yellowLengthList) {
        if (this.yellowLengthList == null) {
            this.yellowLengthList = new ArrayList<>();
        }
        this.yellowLengthList.addAll(yellowLengthList);
    }

    @JsonIgnore
    public void addRedLengthList(List<Integer> redLengthList) {
        if (this.redLengthList == null) {
            this.redLengthList = new ArrayList<>();
        }
        this.redLengthList.addAll(redLengthList);
    }

    @JsonIgnore
    private void calAvgGreen() {
        if (greenLengthList == null) {
            return;
        }
        this.averageGreen = greenLengthList.stream().reduce(0, Integer::sum) / (greenLengthList.size() * 1.0);
    }

    @JsonIgnore
    private void calAvgYellow() {
        if (yellowLengthList == null) {
            return;
        }
        this.averageYellow = yellowLengthList.stream().reduce(0, Integer::sum) / (yellowLengthList.size() * 1.0);
    }

    @JsonIgnore
    private void calAvgRed() {
        if (redLengthList == null) {
            return;
        }
        this.averageRed = redLengthList.stream().reduce(0, Integer::sum) / (redLengthList.size() * 1.0);
    }

    @JsonIgnore
    public void calAndFillVO() {
        calAvgGreen();
        calAvgYellow();
        calAvgRed();
    }

}
