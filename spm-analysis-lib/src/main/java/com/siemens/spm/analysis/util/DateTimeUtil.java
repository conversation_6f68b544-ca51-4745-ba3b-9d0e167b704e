package com.siemens.spm.analysis.util;

import com.siemens.spm.common.util.ListUtil;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;

@Slf4j
public final class DateTimeUtil {

    public static final String TIME_FORMAT = "HH:mm";

    public static final String DATE_FORMAT = "yyyy-MM-dd";

    public static final long NANOS_PER_SECOND = 1000000000;

    private DateTimeUtil() {
    }

    public static boolean eventDateTimeGuard(PerfLogEventVO eventVO, LocalDateTime fromTime) {
        // NOTE: (fromTime, toTime]
        if (!eventVO.getDateTime().isAfter(fromTime)) {
            String msg = "Event datetime " + eventVO.getDateTime()
                    + " is not after fromTime " + fromTime;
            log.debug(msg);
            return false;
        }

        return true;
    }

    /**
     * Utility method getting the time object which comes later.
     *
     * @param first  the first {@link LocalDateTime} object
     * @param second the second {@link LocalDateTime} object
     * @return {@link LocalDateTime} object which comes later. If one of the time object is {@code null}, the other is
     * returned. If both are {@code null}, {@code null} value will be
     */
    public static LocalDateTime getTheLater(LocalDateTime first, LocalDateTime second) {
        if (first != null && second != null) {
            if (first.isAfter(second)) {
                return first;
            } else {
                return second;
            }
        }

        return first != null ? first : second;
    }

    public static LocalTime getTheLater(LocalTime first, LocalTime second) {
        if (first != null && second != null) {
            if (first.isAfter(second)) {
                return first;
            } else {
                return second;
            }
        }

        return first != null ? first : second;
    }

    /**
     * Utility method to calculate difference between 2 {@link LocalDateTime}s
     *
     * @param fromTime the first {@link LocalDateTime} object
     * @param toTime   the second {@link LocalDateTime} object
     * @return difference between the objects in seconds
     */
    public static double calculateDurationTime(LocalDateTime fromTime, LocalDateTime toTime) {
        return (int) Duration.between(fromTime, toTime).getSeconds()
                + (double) Duration.between(fromTime, toTime).getNano() / DateTimeUtil.NANOS_PER_SECOND;
    }

    /**
     * Utility method getting the time object which comes sooner.
     *
     * @param first  the first {@link LocalDateTime} object
     * @param second the second {@link LocalDateTime} object
     * @return {@link LocalDateTime} object which comes sooner. If one of the time object is {@code null}, the other is
     * returned. If both are {@code null}, {@code null} value will be
     */
    public static LocalDateTime getTheSooner(LocalDateTime first, LocalDateTime second) {
        if (first != null && second != null) {
            if (first.isBefore(second)) {
                return first;
            } else {
                return second;
            }
        }

        return first != null ? first : second;
    }

    public static LocalTime getTheSooner(LocalTime first, LocalTime second) {
        if (first != null && second != null) {
            if (first.isBefore(second)) {
                return first;
            } else {
                return second;
            }
        }

        return first != null ? first : second;
    }

    /**
     * Filter list of date within date range provided from and to and exclude dates. filtered = [from -> to] - exclude
     *
     * @param from         start of date range need to filter
     * @param to           end of date range need to filter
     * @param excludeDates exclude dates
     * @return a list of filtered dates
     */
    public static List<LocalDate> filterDates(LocalDate from, LocalDate to, List<LocalDate> excludeDates) {
        if (from == null || to == null) {
            return new ArrayList<>();
        }

        List<LocalDate> dates = new ArrayList<>();
        LocalDate iterDate = from;
        while (!iterDate.isAfter(to)) {
            if (ListUtil.hasNoItem(excludeDates) || !excludeDates.contains(iterDate)) {
                dates.add(iterDate);
            }

            iterDate = iterDate.plusDays(1);
        }

        return dates;
    }

    public static LocalDateTime min(LocalDateTime... elements) {
        return Stream.of(elements)
                .filter(Objects::nonNull)
                .min(LocalDateTime::compareTo).orElse(null);
    }

    public static LocalDateTime max(LocalDateTime... elements) {
        return Stream.of(elements)
                .filter(Objects::nonNull)
                .max(LocalDateTime::compareTo).orElse(null);
    }

    public static Optional<Duration> duration(LocalDateTime start, LocalDateTime end) {
        if(start == null || end == null) {
            return Optional.empty();
        } else {
            return Optional.of(Duration.between(start, end));
        }
    }

    public static Duration getDuration(LocalDateTime beginTime, LocalDateTime endTime) {
        return duration(beginTime, endTime).orElse(Duration.ZERO);
    }

}
