package com.siemens.spm.analysis.vo;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import com.siemens.spm.analysis.domain.PhaseColor;
import com.siemens.spm.perflog.vo.PerfLogEventVO;

import lombok.Getter;

/**
 * Per (phase, advance_detector).
 */
public class AppDelayCycleVO {

    public static class AppDelayCycleInfo {
        
        @Getter
        private final double delaySeconds;

        @Getter
        private final int delayDetCount;

        public AppDelayCycleInfo(double delay, int delayDetCount) {
            this.delaySeconds = delay;
            this.delayDetCount = delayDetCount;
        }

    }

    @Getter
    private final int phaseNum;

    /**
     * Use milliseconds as the unit for travel time to stop bar to minimize the loss of precision
     */
    private final long travelTimeToStopBarMillis;

    /**
     * Green begin time of the cycle. Can be null if the {@link PerfLogEventVO.Event#PHASE_BEGIN_GREEN} is missing in the selected time range.
     */
    @Getter
    private final LocalDateTime greenBeginTime;

    private LocalDateTime yellowBeginTime;

    private LocalDateTime redBeginTime;

    @Getter
    private LocalDateTime nextGreenBeginTime;

    // for debugging purposes
    private PhaseColor color;

    private final List<LocalDateTime> advanceDetOns = new ArrayList<>();

    private AppDelayCycleVO(int phaseNum, LocalDateTime greenBeginTime, long travelTimeToStopBarMillis) {
        this.phaseNum = phaseNum;
        this.greenBeginTime = greenBeginTime;
        this.color = PhaseColor.GREEN;
        this.travelTimeToStopBarMillis = travelTimeToStopBarMillis;
    }

    public static AppDelayCycleVO start(int phaseNum, LocalDateTime greenBeginTime, long travelTimeToStopBarInSeconds) {
        return new AppDelayCycleVO(phaseNum, greenBeginTime, travelTimeToStopBarInSeconds);
    }

    public void updateState(PerfLogEventVO eventVO) {
        switch (eventVO.getEvent()) {
            case PHASE_BEGIN_YELLOW_CLEARANCE:
                color = PhaseColor.YELLOW;
                yellowBeginTime = eventVO.getDateTime();
                break;

            case PHASE_BEGIN_RED_CLEARANCE:
                color = PhaseColor.RED;
                redBeginTime = eventVO.getDateTime();
                break;

            default:
                // Ignore other events
                break;
        }
    }

    public void advanceDetOn(LocalDateTime time) {
        advanceDetOns.add(time);
    }

    public void end(LocalDateTime time) {
        nextGreenBeginTime = time;
    }

    public AppDelayVehicleVO toAppDelayVehicleVO() {
        AppDelayCycleInfo appDelayCycleInfo = getAppDelayCycleInfo();
        return new AppDelayVehicleVO(greenBeginTime, nextGreenBeginTime,
                appDelayCycleInfo.getDelaySeconds(), appDelayCycleInfo.getDelayDetCount());
    }

    /**
     * Filters all advance det_on events fulfilling the condition:
     * <p> yellow_begin < advance_det_on + travel_time_to_stop_bar < next_green_begin_time.
     * <p> Delay is then calculated based on the filtered advance det_on events.
     * @return AppDelayCycleInfo containing the total delay in seconds and the count of advance det_on events
     */
    public AppDelayCycleInfo getAppDelayCycleInfo() {
        List<LocalDateTime> detOnsForDelay = getDetOnsForDelay();
        double delay = getDelayInSeconds(detOnsForDelay);
        return new AppDelayCycleInfo(delay, detOnsForDelay.size());
    }

    /**
     * Filters all advance det_on events fulfilling the 2 conditions:
     * <ul>
     * <li> advance_det_on is in the time range [fromTime, toTime)
     * <li>yellow_begin < advance_det_on + travel_time_to_stop_bar < next_green_begin_time
     * </ul>
     * <p>
     * 
     * Delay is then calculated based on the filtered advance det_on events.
     * 
     * @param fromTime
     * @param toTime
     * @return AppDelayCycleInfo containing the total delay in seconds and the count of advance det_on events
     */
    public AppDelayCycleInfo getAppDelayCycleInfo(LocalDateTime fromTime, LocalDateTime toTime) {
        List<LocalDateTime> detOnsForDelay = getDetOnsForDelay(fromTime, toTime);
        double delay = getDelayInSeconds(detOnsForDelay);
        return new AppDelayCycleInfo(delay, detOnsForDelay.size());
    }

    private double getDelayInSeconds(List<LocalDateTime> detOnsForDelay) {
        long delayMills = 0;

        for (LocalDateTime activationTime : detOnsForDelay) {
            long delay = Duration.between(activationTime, nextGreenBeginTime)
                            .minusMillis(travelTimeToStopBarMillis)
                            .toMillis();
            delayMills += delay;
        }

        return delayMills / 1000.0;
    }

    public List<LocalDateTime> getDetOnsForDelay() {
        if (nextGreenBeginTime == null) {
            return List.of();
        }

        LocalDateTime yellowOrRedBeginTime = (yellowBeginTime != null) ? yellowBeginTime : redBeginTime;
        
        if (yellowOrRedBeginTime == null) {
            // If there is no yellow or red begin time, we cannot calculate the delay
            return List.of();
        }

        List<LocalDateTime> detOnsForDelay = 
            advanceDetOns.stream().filter(activationTime -> {
                // a vehicle is considered to be delayed if yellow_begin < advance_det_on + travel_time_to_stop_bar < next_green_begin_time
                LocalDateTime projectedDetOnTime = activationTime.plus(travelTimeToStopBarMillis, ChronoUnit.MILLIS);
                return projectedDetOnTime.isAfter(yellowOrRedBeginTime) && projectedDetOnTime.isBefore(nextGreenBeginTime);
            }).toList();

        return Collections.unmodifiableList(detOnsForDelay);
    }

    public List<LocalDateTime> getDetOnsForDelay(LocalDateTime fromTime, LocalDateTime toTime) {
        return getDetOnsForDelay()
                .stream()
                .filter(detOnTime -> !detOnTime.isBefore(fromTime) && detOnTime.isBefore(toTime))
                .toList();
    }

}
