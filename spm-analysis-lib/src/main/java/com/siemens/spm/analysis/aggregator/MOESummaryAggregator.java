package com.siemens.spm.analysis.aggregator;

import com.siemens.spm.analysis.vo.moe.ActualPedPhaseLengthVO;
import com.siemens.spm.analysis.vo.moe.ActualVehiclePhaseLengthVO;
import com.siemens.spm.analysis.vo.moe.PatternSummaryMOEVO;
import com.siemens.spm.analysis.vo.moe.PhaseSplitTimeAggregationVO;
import com.siemens.spm.analysis.vo.moe.PhaseSummaryMOEVO;
import com.siemens.spm.analysis.vo.moe.ProgrammedVehiclePhaseLengthVO;
import com.siemens.spm.common.shared.domaintype.alarm.trafficmetric.MetricSpecifier;
import com.siemens.spm.common.shared.domaintype.alarm.trafficmetric.TrafficMetric;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static com.siemens.spm.analysis.util.Constants.MAX_PHASE_NUM;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_BEGIN_GREEN;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_BEGIN_RED_CLEARANCE;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_BEGIN_YELLOW_CLEARANCE;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_END_RED_CLEARANCE;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_END_YELLOW_CLEARANCE;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_GREEN_TERMINATION;
import static com.siemens.spm.perflog.vo.PerfLogEventVO.Event.PHASE_ON;

@Slf4j
public class MOESummaryAggregator implements PerfLogEventAggregator {

    private static final int ALL_PATTERN_KEY = -1;

    private static final int FREE_PATTERN = 254;

    private static final List<PerfLogEventVO.Event> EVENTS_NOT_FOR_FREE_PATTERN = List.of(
            PHASE_ON,
            PHASE_BEGIN_GREEN,
            PHASE_GREEN_TERMINATION,
            PHASE_BEGIN_YELLOW_CLEARANCE,
            PHASE_END_YELLOW_CLEARANCE,
            PHASE_BEGIN_RED_CLEARANCE,
            PHASE_END_RED_CLEARANCE
    );

    private IntersectionConfigVO configVO;

    private final Map<Integer, MOESummaryPatternAggregator> moeSummaryPatternAggregatorMap;

    private Integer pattern;

    @Getter
    private Map<Integer, PatternSummaryMOEVO> patternSummaryMOEVOMap;

    public MOESummaryAggregator() {
        this.moeSummaryPatternAggregatorMap = new LinkedHashMap<>();
        this.patternSummaryMOEVOMap = new LinkedHashMap<>();
    }

    @Override
    public void setConfig(IntersectionConfigVO configVO) {
        this.configVO = configVO;
        this.moeSummaryPatternAggregatorMap.forEach((k, v) -> v.setConfig(configVO));
    }

    @Override
    public void putEvent(PerfLogEventVO eventVO) {

        PerfLogEventVO.Event event = eventVO.getEvent();
        switch (event) {
            case COORD_PATTERN_CHANGE -> {
                pattern = getPattern(eventVO);
                if (moeSummaryPatternAggregatorMap.get(pattern) != null) {
                    moeSummaryPatternAggregatorMap.get(pattern).getMoeSummaryPhaseAggregatorMap()
                            .forEach((k, v) -> v.setChangedPattern(true));
                }
            }
            case PHASE_ON,
                 PHASE_BEGIN_GREEN,
                 PHASE_GREEN_TERMINATION,
                 PHASE_BEGIN_YELLOW_CLEARANCE,
                 PHASE_END_YELLOW_CLEARANCE,
                 PHASE_BEGIN_RED_CLEARANCE,
                 PHASE_END_RED_CLEARANCE,
                 PHASE_FORCE_OFF,
                 PHASE_GAP_OUT,
                 PHASE_MAX_OUT,
                 PHASE_OMIT_ON,
                 DETECTOR_ON,
                 PEDESTRIAN_CALL_REGISTERED,
                 PEDESTRIAN_BEGIN_CLEARANCE,
                 PEDESTRIAN_BEGIN_WALK,
                 PEDESTRIAN_BEGIN_NOT_WALK -> {
                if (pattern == null) {
                    return;
                }
                if (pattern.equals(FREE_PATTERN) && EVENTS_NOT_FOR_FREE_PATTERN.contains(eventVO.getEvent())) {
                    return;
                }
                moeSummaryPatternAggregatorMap.putIfAbsent(pattern, new MOESummaryPatternAggregator(pattern, configVO));
                moeSummaryPatternAggregatorMap.get(pattern).putEvent(eventVO);
            }
            default -> {
                // not use
            }
        }
    }

    private int getPattern(PerfLogEventVO eventVO) {
        return (int) eventVO.getParameter();
    }

    public void aggregatePatternSummaryMOEVO() {
        moeSummaryPatternAggregatorMap.forEach((k, v) -> patternSummaryMOEVOMap.put(k, v.getPatternSummaryMOEVO()));
        patternSummaryMOEVOMap.put(ALL_PATTERN_KEY, createAllPatternData());
    }

    private PatternSummaryMOEVO createAllPatternData() {
        PatternSummaryMOEVO allPatternSummaryMOEVO = new PatternSummaryMOEVO();
        List<PatternSummaryMOEVO> patternSummaryMOEVOList = patternSummaryMOEVOMap.values().stream().toList();
        for (int phase = 1; phase <= MAX_PHASE_NUM; phase++) {
            PhaseSummaryMOEVO allPhaseSummaryMOEVO = new PhaseSummaryMOEVO();
            PhaseSplitTimeAggregationVO allPhaseSplitTimeAggregationVO = new PhaseSplitTimeAggregationVO();
            ActualVehiclePhaseLengthVO allActualVehiclePhaseLengthVO = new ActualVehiclePhaseLengthVO();
            ActualPedPhaseLengthVO allActualPedPhaseLengthVO = new ActualPedPhaseLengthVO();
            ProgrammedVehiclePhaseLengthVO allProgrammedVehiclePhaseLengthVO = new ProgrammedVehiclePhaseLengthVO();
            int countedPhase = 0;
            for (PatternSummaryMOEVO patternSummaryMOEVO : patternSummaryMOEVOList) {
                PhaseSummaryMOEVO phaseSummaryMOEVO = patternSummaryMOEVO.getPhaseSummaryMOEVOMap().get(phase);
                PhaseSplitTimeAggregationVO phaseSplitTimeAggregationVO = patternSummaryMOEVO.getPhaseSplitTimeAggregationVOMap()
                        .get(phase);
                if (phaseSummaryMOEVO == null || phaseSplitTimeAggregationVO == null) {
                    continue;
                }
                addAllPhaseSummaryValue(allPhaseSummaryMOEVO, phaseSummaryMOEVO);
                addAllActualVehiclePhaseLength(allActualVehiclePhaseLengthVO, phaseSplitTimeAggregationVO.getAvp());
                addAllActualPedPhaseLength(allActualPedPhaseLengthVO, phaseSplitTimeAggregationVO.getApp());
                addProgVehiclePhaseLength(allProgrammedVehiclePhaseLengthVO, phaseSplitTimeAggregationVO.getPvp());
                countedPhase++;
            }
            if (countedPhase > 0) {
                allPhaseSummaryMOEVO.calAndFillVO();
                allActualPedPhaseLengthVO.calAndFillVO();
                allProgrammedVehiclePhaseLengthVO.calAndFillVO();
                allActualVehiclePhaseLengthVO.calAndFillVO();
                allPhaseSplitTimeAggregationVO.setApp(allActualPedPhaseLengthVO);
                allPhaseSplitTimeAggregationVO.setAvp(allActualVehiclePhaseLengthVO);
                allPhaseSplitTimeAggregationVO.setPvp(allProgrammedVehiclePhaseLengthVO);
                allPatternSummaryMOEVO.addPhaseSummaryMOEVOElement(phase, allPhaseSummaryMOEVO);
                allPatternSummaryMOEVO.addPhaseSplitTimeAggregationVOElement(phase,
                        allPhaseSplitTimeAggregationVO);
            }
        }
        return allPatternSummaryMOEVO;
    }

    private void addAllPhaseSummaryValue(PhaseSummaryMOEVO allPhaseSummaryMOEVO,
                                         PhaseSummaryMOEVO subPhaseSummaryMOEVO) {
        allPhaseSummaryMOEVO.addForceOff(
                subPhaseSummaryMOEVO.getForceOff() == null ? 0 : subPhaseSummaryMOEVO.getForceOff());
        allPhaseSummaryMOEVO.addGapOut(
                subPhaseSummaryMOEVO.getGapOut() == null ? 0 : subPhaseSummaryMOEVO.getGapOut());
        allPhaseSummaryMOEVO.addMaxOut(
                subPhaseSummaryMOEVO.getMaxOut() == null ? 0 : subPhaseSummaryMOEVO.getMaxOut());
        allPhaseSummaryMOEVO.addNumOfPedestrianCalls(subPhaseSummaryMOEVO.getNumOfPedestrianCalls() == null ?
                0 :
                subPhaseSummaryMOEVO.getNumOfPedestrianCalls());
        allPhaseSummaryMOEVO.addGreenLengthList(subPhaseSummaryMOEVO.getGreenLengthList() == null ?
                new ArrayList<>() :
                subPhaseSummaryMOEVO.getGreenLengthList());
        allPhaseSummaryMOEVO.addOmits(subPhaseSummaryMOEVO.getOmits() == null ? 0 : subPhaseSummaryMOEVO.getOmits());
        allPhaseSummaryMOEVO.addSumOfCycle(
                subPhaseSummaryMOEVO.getSumOfCycle() == null ? 0 : subPhaseSummaryMOEVO.getSumOfCycle());
        allPhaseSummaryMOEVO.addActuation(subPhaseSummaryMOEVO.getActuation() == null ? 0 :
                subPhaseSummaryMOEVO.getActuation());
        allPhaseSummaryMOEVO.addGreenLengthRatio(subPhaseSummaryMOEVO.getGreenLengthRatio());
    }

    private void addAllActualVehiclePhaseLength(ActualVehiclePhaseLengthVO allActualVehiclePhaseLengthVO,
                                                ActualVehiclePhaseLengthVO subActualVehiclePhaseLengthVO) {
        if (subActualVehiclePhaseLengthVO == null) {
            return;
        }
        allActualVehiclePhaseLengthVO.addGreenLengthList(subActualVehiclePhaseLengthVO
                .getGreenLengthList() == null ? new ArrayList<>() : subActualVehiclePhaseLengthVO
                .getGreenLengthList());
        allActualVehiclePhaseLengthVO.addYellowLengthList(
                subActualVehiclePhaseLengthVO.getYellowLengthList() == null ?
                        new ArrayList<>() : subActualVehiclePhaseLengthVO.getYellowLengthList());
        allActualVehiclePhaseLengthVO.addRedLengthList(
                subActualVehiclePhaseLengthVO.getRedLengthList() == null ?
                        new ArrayList<>() : subActualVehiclePhaseLengthVO.getRedLengthList());
    }

    private void addAllActualPedPhaseLength(ActualPedPhaseLengthVO allActualPedPhaseLengthVO,
                                            ActualPedPhaseLengthVO subActualPedPhaseLengthVO) {
        if (subActualPedPhaseLengthVO == null) {
            return;
        }
        allActualPedPhaseLengthVO.addActualPedWalkList(subActualPedPhaseLengthVO
                .getActualPedWalkList() == null ? new ArrayList<>() : subActualPedPhaseLengthVO
                .getActualPedWalkList());
        allActualPedPhaseLengthVO.addActualPedClearList(subActualPedPhaseLengthVO
                .getActualPedClearList() == null ? new ArrayList<>() : subActualPedPhaseLengthVO
                .getActualPedClearList());
    }

    private void addProgVehiclePhaseLength(ProgrammedVehiclePhaseLengthVO allProgrammedVehiclePhaseLengthVO,
                                           ProgrammedVehiclePhaseLengthVO subProgrammedVehiclePhaseLengthVO) {
        if (subProgrammedVehiclePhaseLengthVO == null) {
            return;
        }
        allProgrammedVehiclePhaseLengthVO.addGreenLengthList(
                subProgrammedVehiclePhaseLengthVO.getGreenLengthList() == null ?
                        new ArrayList<>() : subProgrammedVehiclePhaseLengthVO.getGreenLengthList());
    }

    @Override
    public List<TrafficMetric> getSupportedMetric() {
        return Collections.emptyList();
    }

    @Override
    public Double getAggregatedValue(MetricSpecifier metricSpecifier) {
        return null;
    }

    @Override
    public void setTargetIdentifier(String targetIdentifier) {
        // not use
    }

    @Override
    public void setBinSize(int binSize) {
        // not use
    }

    @Override
    public void setFromTime(LocalDateTime fromTime) {
        // not use
    }

    @Override
    public void setToTime(LocalDateTime toTime) {
        // not use
    }

}
