package com.siemens.spm.analysis.vo.queuelength;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.analysis.vo.Plan;

import java.time.LocalDateTime;

public class QueueLengthPlanStatVO extends QueueLengthBinVO {

    private static final long serialVersionUID = 3224277141694374797L;

    public QueueLengthPlanStatVO(int planNum, LocalDateTime fromTime){
        super(fromTime);

        this.plan = new Plan(planNum);
    }

    @JsonIgnore
    private final Plan plan;

    @JsonProperty("plan")
    public String planName() {
        return plan.getPlanName();
    }

}
