package com.siemens.spm.analysis.util;

import java.util.Map;

public final class Constants {
    private Constants() {
    }
    
    /**
     * Max number of phases
     */
    public static final int MAX_PHASE_NUM = 16;

    /**
     * Detector offset which is added with physical detector number to calculate
     * logical detector number.
     */
    public static final int MAX_VEHICLE_PHYSICAL_DET_ID = 72;

    /**
     * If det_mode is equal 1 then event 82 (DETECTOR_ON) is similar with event 90
     * (PED_DETECTOR_ON)
     */
    public static final int PED_DETECTOR_MODE = 1;

    public static final int PROGRAMMED_YELLOW_LENGTH = 4;

    public static final int PROGRAMMED_RED_LENGTH = 1;

    public static final String RING_KEY = "Ring ";

    public static final String PATTERN_KEY = "Pattern ";

    public static final String PHASE_KEY = "Phase ";

    public static final String NORTH_BOUND = "Northbound";

    public static final String EAST_BOUND = "Eastbound";

    public static final String SOUTH_BOUND = "Southbound";

    public static final String WEST_BOUND = "Westbound";

    public static final String LEFT_MOVE = "Left";

    public static final String RIGHT_MOVE = "Right";

    public static final String THROUGH_MOVE = "Through";

    public static final String THROUGH_LEFT_MOVE = "Through/Left";

    public static final String THROUGH_RIGHT_MOVE = "Through/Right";
    public static final Map<String, String> DIRECTION_MAP = Map.of(
            NORTH_BOUND, "NB",
            EAST_BOUND, "EB",
            SOUTH_BOUND, "SB",
            WEST_BOUND, "WB"
    );

    public static final Map<String, String> MOVEMENT_MAP = Map.of(
            LEFT_MOVE, "L",
            RIGHT_MOVE, "R",
            THROUGH_MOVE, "T",
            THROUGH_LEFT_MOVE, "TL",
            THROUGH_RIGHT_MOVE, "TR"
    );
}
