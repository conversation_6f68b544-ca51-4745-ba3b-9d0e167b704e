/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AorBinVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.analysis.vo;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;

public class AorBinVO extends VolumeBinVO {

    private static final long serialVersionUID = -5635624983594721325L;

    public AorBinVO(LocalDateTime fromTime, int binSize) {
        this(fromTime, fromTime.plusSeconds(binSize));
    }

    public AorBinVO(LocalDateTime fromTime, LocalDateTime toTime) {
        super(fromTime, toTime);
    }

    public AorBinVO(VolumeBinVO volumeBinVO) {
        super(volumeBinVO);
    }

    @JsonProperty("aor_volume")
    public double getAorVolume() {
        return aorVB.getVolumePerHour();
    }

    @JsonProperty("aor_percent")
    public Double getAorPercent() {
        int totalDetHits = getTotalDetHits();
        return totalDetHits != 0 ? 100.0 * aorVB.getCount() / totalDetHits : null;
    }

}
