package com.siemens.spm.analysis.factory.queuelength;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import com.siemens.spm.analysis.exception.AnalysisInitializationException;
import com.siemens.spm.analysis.exception.InvalidPerfLogException;
import com.siemens.spm.analysis.factory.AbstractPhaseAnalysisFactory;
import com.siemens.spm.analysis.factory.topology.DetTypeUsed;
import com.siemens.spm.analysis.factory.topology.Require;
import com.siemens.spm.analysis.util.ChartType;
import com.siemens.spm.analysis.util.IntersectionConfigUtils;
import com.siemens.spm.analysis.vo.queuelength.QueueLengthAnalysisVO;
import com.siemens.spm.analysis.vo.queuelength.QueueLengthChartVO;
import com.siemens.spm.analysis.vo.queuelength.QueueLengthLaneChartVO;
import com.siemens.spm.analysis.vo.queuelength.QueueLengthPhaseChartVO;
import com.siemens.spm.common.util.ListUtil;
import com.siemens.spm.perflog.vo.DetectorVO.DetectorType;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.LaneVO;
import com.siemens.spm.perflog.vo.PerfLogBundleVO;
import com.siemens.spm.perflog.vo.PerfLogChunkVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import com.siemens.spm.perflog.vo.PerfLogGapVO;
import com.siemens.spm.perflog.vo.PhaseVO;

@DetTypeUsed( value = { DetectorType.ADVANCE, DetectorType.STOP_BAR }, require = Require.ALL )
public class QueueLengthAnalysisFactory extends
        AbstractPhaseAnalysisFactory<QueueLengthAnalysisVO, QueueLengthChartVO> {

    public static final int BIN_SIZE_900 = 900;

    private static final List<PerfLogEventVO.Event> PHASE_EVENTS = List.of(PerfLogEventVO.Event.PHASE_ON,
            PerfLogEventVO.Event.PHASE_BEGIN_GREEN,
            PerfLogEventVO.Event.PHASE_GREEN_TERMINATION,
            PerfLogEventVO.Event.PHASE_BEGIN_YELLOW_CLEARANCE,
            PerfLogEventVO.Event.PHASE_END_YELLOW_CLEARANCE,
            PerfLogEventVO.Event.PHASE_BEGIN_RED_CLEARANCE,
            PerfLogEventVO.Event.PHASE_END_RED_CLEARANCE);

    private final int binSize;

    public QueueLengthAnalysisFactory(int binSize) {
        this.binSize = binSize;
    }

    public QueueLengthAnalysisVO createAnalysis(LocalDateTime fromTime,
                                                LocalDateTime toTime,
                                                PerfLogBundleVO perfLogBundleVO,
                                                List<PerfLogGapVO> perfLogGapVOList)
            throws InvalidPerfLogException, AnalysisInitializationException {
        return createAnalysis(fromTime, toTime, perfLogBundleVO, perfLogGapVOList, QueueLengthAnalysisVO.class,
                QueueLengthChartVO.class);
    }

    @Override
    protected void scanPerfLog(LocalDateTime fromTime, LocalDateTime toTime, List<PerfLogChunkVO> perfLogChunkVOList,
                               Map<String, IntersectionConfigVO> intConfigVOMap,
                               Map<Integer, QueueLengthChartVO> chartVOMap) {
        throw new UnsupportedOperationException();
    }

    @Override
    protected void createCharts(LocalDateTime fromTime,
                                LocalDateTime toTime,
                                List<PerfLogChunkVO> perfLogChunkVOList,
                                Map<String, IntersectionConfigVO> intConfigVOMap,
                                Set<Integer> phaseSet,
                                Class<QueueLengthChartVO> queueLengthChartVOClass) {

        if (intConfigVOMap.isEmpty()) {
            return;
        }

        // Create chart builder map: Phase -> List of chart builder
        Map<Integer, List<QueueLengthChartBuilder>> phaseChartBuilderMap = new HashMap<>();
        for (Integer phaseNum : phaseSet) {
            PhaseVO phaseVO = IntersectionConfigUtils.identifyPhase(new ArrayList<>(intConfigVOMap.values()), phaseNum);
            if (phaseVO == null || ListUtil.hasNoItem(phaseVO.getLanes())) {
                continue;
            }

            List<LaneVO> lanes = phaseVO.getLanes()
                    .stream()
                    .filter(Objects::nonNull)
                    .toList();
            for (int laneIdx = 0; laneIdx < lanes.size(); laneIdx++) {
                LaneVO laneVO = lanes.get(laneIdx);

                QueueLengthLaneChartVO laneChartVO = QueueLengthLaneChartVO.builder()
                        .chartType(ChartType.QUEUE_LENGTH_LANE.getChartName())
                        .laneIdx(laneIdx)
                        .movement(laneVO.getMovement())
                        .phase(phaseNum)
                        .fromTime(fromTime)
                        .toTime(toTime)
                        .build();
                QueueLengthChartBuilder chartBuilder = new QueueLengthLaneChartBuilder(laneChartVO, binSize, laneIdx);

                phaseChartBuilderMap.putIfAbsent(phaseNum, new ArrayList<>());
                phaseChartBuilderMap.get(phaseNum).add(chartBuilder);
            }

            QueueLengthPhaseChartVO chartVO = QueueLengthPhaseChartVO.builder()
                    .chartType(ChartType.QUEUE_LENGTH_PHASE.getChartName())
                    .phase(phaseNum)
                    .fromTime(fromTime)
                    .toTime(toTime)
                    .build();
            QueueLengthChartBuilder chartBuilder = new QueueLengthPhaseChartBuilder(chartVO, binSize);

            phaseChartBuilderMap.putIfAbsent(phaseNum, new ArrayList<>());
            phaseChartBuilderMap.get(phaseNum).add(chartBuilder);
        }

        // Scan PerfLog to build other chart data
        scanPerfLogWithMultipleChartsPerPhase(perfLogChunkVOList, intConfigVOMap, phaseChartBuilderMap);

        for (List<QueueLengthChartBuilder> chartBuilderList : phaseChartBuilderMap.values()) {
            chartBuilderList.forEach(chartBuilder -> analysisVO.addChart(chartBuilder.getChartVO()));
        }

        for (QueueLengthChartVO chartVO : analysisVO.getChartList()) {
            chartVO.setSkippedPhaseIntervals(this.skippedIntervalsMap.get(chartVO.getPhase()));
        }
    }

    protected void scanPerfLogWithMultipleChartsPerPhase(List<PerfLogChunkVO> perfLogChunkVOList,
                                                         Map<String, IntersectionConfigVO> intConfigVOMap,
                                                         Map<Integer, List<QueueLengthChartBuilder>> phaseChartBuilderMap) {
        if (perfLogChunkVOList == null || phaseChartBuilderMap == null || intConfigVOMap == null) {
            throw new IllegalArgumentException();
        }

        // Read chunk one by one
        for (PerfLogChunkVO chunkVO : perfLogChunkVOList) {
            IntersectionConfigVO intConfigVO = intConfigVOMap.get(chunkVO.getConfigID());
            for (List<QueueLengthChartBuilder> chartBuilders : phaseChartBuilderMap.values()) {
                for (QueueLengthChartBuilder chartBuilder : chartBuilders) {
                    chartBuilder.setIntersectionConfig(intConfigVO);
                }
            }

            List<PerfLogEventVO> eventVOList = chunkVO.getPerfLogEvents();
            for (PerfLogEventVO eventVO : eventVOList) {
                putEventToCharts(phaseChartBuilderMap, eventVO);
            }

        }
        // Finalize finish chart building and output to chartVOMap
        for (List<QueueLengthChartBuilder> chartBuilders : phaseChartBuilderMap.values()) {
            for (QueueLengthChartBuilder chartBuilder : chartBuilders) {
                chartBuilder.build();
            }
        }
    }

    private void putEventToCharts(Map<Integer, List<QueueLengthChartBuilder>> chartBuilderMap,
                                  PerfLogEventVO eventVO) {
        for (Map.Entry<Integer, List<QueueLengthChartBuilder>> entry : chartBuilderMap.entrySet()) {
            int phaseNum = entry.getKey();
            List<QueueLengthChartBuilder> chartBuilders = entry.getValue();
            for (QueueLengthChartBuilder chartBuilder : chartBuilders) {
                if (isTargetEvent(eventVO, phaseNum)) {
                    chartBuilder.putEvent(eventVO);
                }
            }
        }
    }

    private boolean isTargetEvent(PerfLogEventVO eventVO, int phaseNum) {
        PerfLogEventVO.Event event = eventVO.getEvent();
        if (PHASE_EVENTS.contains(event) && eventVO.getParameter() == phaseNum) {
            return true;
        } else {
            if (event == PerfLogEventVO.Event.COORD_PATTERN_CHANGE) {
                return true;
            }

            if (event == PerfLogEventVO.Event.DETECTOR_ON) {
                return isValidDetEventForProcessing(eventVO);
            }
            
            return false;
        }
    }

}
