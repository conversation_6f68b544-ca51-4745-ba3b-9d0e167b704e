package com.siemens.spm.analysis.vo.splitmonitor;

import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.LinkedList;

import com.siemens.spm.analysis.aggregator.Interval;

import lombok.Getter;
import lombok.Setter;

/**
 * Represents an interval of time that a phase was skipped while a plan was running.
 */
@Getter
@Setter
public class SkippedInterval extends Interval {

    private static final long serialVersionUID = -9626870885646198L;

    public enum SkippedIntervalReason {
        NOT_STARTED_AS_PLANNED,
        EXCESSIVE_GREEN_TIME,
        OMITTED_PHASE
    }

    private int plan;
    private SkippedIntervalReason reason;

    public SkippedInterval(int plan, LocalDateTime fromTime, LocalDateTime toTime) {
        super(fromTime, toTime);
        this.plan = plan;
    }

    public SkippedInterval(int plan, LocalDateTime fromTime, LocalDateTime toTime, SkippedIntervalReason reason) {
        this(plan, fromTime, toTime);
        this.reason = reason;
    }

    public SkippedInterval copy() {
        return new SkippedInterval(plan, fromTime, toTime, reason);
    }

    /**
     * Plans runs one after another, so we can concatenate intervals from the same plan if they are consecutive.
     * 
     * @param intervals skipped intervals of a specific phase while the plan was running
     * @return a list of concatenated skipped intervals
     */
    @SuppressWarnings("java:S1319")
    public static LinkedList<SkippedInterval> concatenateSkippedIntervals(LinkedList<SkippedInterval> intervals) {
        if (intervals == null || intervals.size() < 2) {
            // If there are no intervals or only one, return as is
            return intervals;
        }

        intervals.sort(Comparator.comparing(SkippedInterval::getFromTime));

        LinkedList<SkippedInterval> concatenated = new LinkedList<>();
        SkippedInterval last = intervals.getFirst().copy();
        concatenated.add(last);

        for (int i = 1; i < intervals.size(); i++) {
            last = concatenated.getLast();
            SkippedInterval current = intervals.get(i);

            // Check if the last interval and current interval are from the same plan and are consecutive
            if (last.getPlan() == current.getPlan() 
                && last.toTime.equals(current.fromTime)) {
                    last.toTime = current.toTime;
            } else {
                // If they are different plans or phases, just add the last interval and start a new one
                concatenated.add(current.copy());
            }
        }

        return concatenated;
    }

}
