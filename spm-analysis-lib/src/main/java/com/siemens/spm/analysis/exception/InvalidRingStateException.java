/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : InvalidRingStateException.java
 * Project     : SPM Platform
 */
package com.siemens.spm.analysis.exception;

public class InvalidRingStateException extends Exception {

    private static final long serialVersionUID = 4285750595825547672L;

    public InvalidRingStateException() {
        super();
    }

    public InvalidRingStateException(String message, Throwable cause) {
        super(message, cause);
    }

    public InvalidRingStateException(String message) {
        super(message);
    }

    public InvalidRingStateException(Throwable cause) {
        super(cause);
    }

}
