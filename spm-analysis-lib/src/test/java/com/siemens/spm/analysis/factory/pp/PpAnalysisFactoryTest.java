package com.siemens.spm.analysis.factory.pp;

import com.siemens.spm.analysis.exception.AnalysisInitializationException;
import com.siemens.spm.analysis.exception.InvalidPerfLogException;
import com.siemens.spm.analysis.util.ChartType;
import com.siemens.spm.analysis.vo.pp.PpAnalysisVO;
import com.siemens.spm.analysis.vo.pp.PpChartVO;
import com.siemens.spm.analysis.vo.pp.PpEventVO;
import com.siemens.spm.analysis.vo.pp.PpRequestChartVO;
import com.siemens.spm.analysis.vo.pp.PreemptionCycleVO;
import com.siemens.spm.analysis.vo.pp.PreemptionDetailChartVO;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.IntersectionInfoVO;
import com.siemens.spm.perflog.vo.PerfLogBundleVO;
import com.siemens.spm.perflog.vo.PerfLogChunkVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

class PpAnalysisFactoryTest {

    private static final LocalDateTime FROM_TIME = LocalDateTime.of(2021, 5, 5, 20, 0);
    private static final LocalDateTime TO_TIME = LocalDateTime.of(2021, 5, 5, 22, 0);

    private static final String SAMPLE_EVENTS_1 =
            "2021-05-05T21:47:26.5,102,3\n" +
                    "2021-05-05T21:47:26.6,105,3\n" +
                    "2021-05-05T21:47:32.6,107,3\n" +
                    "2021-05-05T21:48:09.6,104,3\n" +
                    "2021-05-05T21:48:09.6,111,3\n" +

                    "2021-05-05T21:49:58.1,102,4\n" +
                    "2021-05-05T21:49:58.2,105,4\n" +
                    "2021-05-05T21:50:04.2,107,4\n" +
                    "2021-05-05T21:50:14.4,104,4\n" +
                    "2021-05-05T21:50:14.4,111,4\n" +

                    "2021-05-05T21:50:25.4,102,4\n" +
                    "2021-05-05T21:50:25.5,105,4\n" +
                    "2021-05-05T21:50:31.5,107,4\n" +
                    "2021-05-05T21:50:34.6,104,4\n" +
                    "2021-05-05T21:50:40.5,102,4\n" +
                    "2021-05-05T21:50:54.9,104,4\n" +
                    "2021-05-05T21:50:54.9,111,4\n" +

                    "2021-05-05T21:51:01.9,102,4\n" +
                    "2021-05-05T21:51:02,105,4\n" +
                    "2021-05-05T21:51:08,107,4\n" +
                    "2021-05-05T21:51:25.3,104,4\n" +
                    "2021-05-05T21:51:25.3,111,4\n";

    @Test
    void test() throws InvalidPerfLogException, AnalysisInitializationException {
        PpAnalysisFactory factory = new PpAnalysisFactory();
        PpAnalysisVO analysisVO = factory
                .createAnalysis(FROM_TIME, TO_TIME, mockPerfLogBundleVO(), Collections.emptyList());

        Assertions.assertNotNull(analysisVO);
        List<PpChartVO> chartVOList = analysisVO.getChartList();
        Assertions.assertNotNull(chartVOList);

        // 2 preemption detail charts and 1 pp request chart
        Assertions.assertEquals(3, chartVOList.size());

        PpRequestChartVO ppRequestChartVO = (PpRequestChartVO) chartVOList.stream()
                .filter(ppChartVO -> ChartType.PREEMPTION_PRIORITY_REQUEST.getChartName().equals(ppChartVO.chartType()))
                .findFirst()
                .orElse(null);
        Assertions.assertNotNull(ppRequestChartVO);
        List<PpEventVO> preemptRequests = ppRequestChartVO.getPreemptRequests();
        Assertions.assertNotNull(preemptRequests);
        Assertions.assertEquals(5, preemptRequests.size());

        PreemptionDetailChartVO preemptionDetailChartVO = chartVOList.stream()
                .filter(ppChartVO -> ChartType.PREEMPTION_DETAIL.getChartName().equals(ppChartVO.chartType()))
                .map(ppChartVO -> (PreemptionDetailChartVO) ppChartVO)
                .filter(ppChartVO -> ppChartVO.getNumber() == 4)
                .findFirst()
                .orElse(null);
        Assertions.assertNotNull(preemptionDetailChartVO);

        List<PreemptionCycleVO> cycleVOList = preemptionDetailChartVO.getCycles();
        Assertions.assertNotNull(cycleVOList);
        Assertions.assertEquals(3, cycleVOList.size());

        PreemptionCycleVO cycleVOFirst = cycleVOList.get(0);
        Assertions.assertEquals(0.1, cycleVOFirst.getEntryDelayDuration());
        Assertions.assertEquals(0, cycleVOFirst.getTrackClearInterval());
        Assertions.assertEquals(10.2, cycleVOFirst.getDWellDuration());

        PreemptionCycleVO cycleVOSecond = cycleVOList.get(1);
        Assertions.assertEquals(2, cycleVOSecond.getInputOnTimes().size());
        Assertions.assertEquals(0.1, cycleVOSecond.getEntryDelayDuration());
        Assertions.assertEquals(0, cycleVOSecond.getTrackClearInterval());
        Assertions.assertEquals(23.4, cycleVOSecond.getDWellDuration());
    }

    private PerfLogBundleVO mockPerfLogBundleVO() {
        return PerfLogBundleVO.builder()
                .intUUID("Hulk")
                .fromTime(FROM_TIME)
                .toTime(TO_TIME)
                .intConfigs(Map.of("PandaId", IntersectionConfigVO.builder()
                        .intInfo(IntersectionInfoVO.builder()
                                .name("Lawrence")
                                .build())
                        .build()))
                .perfLogChunks(List.of(PerfLogChunkVO.builder()
                        .configID("PandaId")
                        .fromTime(FROM_TIME)
                        .toTime(TO_TIME)
                        .perfLogEvents(mockEventVOList())
                        .build()))
                .build();
    }

    private List<PerfLogEventVO> mockEventVOList() {
        String[] eventsStr = SAMPLE_EVENTS_1.split("\n");

        List<PerfLogEventVO> eventVOList = new ArrayList<>();
        for (String eventStr : eventsStr) {
            String[] values = eventStr.split(",");

            LocalDateTime time;
            try {
                time = LocalDateTime.parse(values[0], DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.S"));
            } catch (Exception e) {
                time = LocalDateTime.parse(values[0], DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"));
            }

            PerfLogEventVO.Event event = PerfLogEventVO.Event.of(Integer.parseInt(values[1]));
            long param = Long.parseLong(values[2]);

            eventVOList.add(new PerfLogEventVO(time, event, param));
        }

        return eventVOList;
    }

}
