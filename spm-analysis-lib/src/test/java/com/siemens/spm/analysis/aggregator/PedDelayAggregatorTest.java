package com.siemens.spm.analysis.aggregator;

import com.siemens.spm.common.shared.domaintype.alarm.trafficmetric.MetricSpecifier;
import com.siemens.spm.common.shared.domaintype.alarm.trafficmetric.TrafficMetric;
import com.siemens.spm.perflog.vo.DetectorInfoVO;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import com.siemens.spm.perflog.vo.PerfLogEventVO.Event;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

class PedDelayAggregatorTest {

    private static final int DET_NUM_1 = 1;
    private static final int DET_NUM_2 = 2;

    private static final int YEAR = 2021;
    private static final int MONTH = 1;
    private static final int DAY = 4;
    private static final int MINUTES_PER_HOUR = 60;
    private static final LocalDateTime T0 = LocalDateTime.of(YEAR, MONTH, DAY, 12, 0);

    private PedDelayAggregator aggregator;

    @BeforeEach
    void setUp() throws Exception {

    }

    @Test
    void testGetAggregatedValue_anyPhases() {
        // Initial aggregator unspecified phases
        aggregator = new PedDelayAggregator();
        aggregator.setConfig(createIntConfig());
        // Add ped events
        List<PerfLogEventVO> events = createPerfLogEventVOs();
        for (var event : events) {
            aggregator.putEvent(event);
        }

        Double maxPedDelay = aggregator
                .getAggregatedValue(new MetricSpecifier(TrafficMetric.MAX_PED_DELAY.getId(), null));
        assertNotNull(maxPedDelay);
        assertEquals(12 * MINUTES_PER_HOUR, maxPedDelay);

        Double avgPedDelay = aggregator
                .getAggregatedValue(new MetricSpecifier(TrafficMetric.AVG_PED_DELAY.getId(), null));
        assertNotNull(avgPedDelay);
        assertEquals(8 * MINUTES_PER_HOUR, avgPedDelay);
    }
    
    @Test
    void testGetAggregatedValue_specifiedPhase() {
        // Initial aggregator unspecified phases
        aggregator = new PedDelayAggregator(DET_NUM_1);
        aggregator.setConfig(createIntConfig());
        // Add ped events
        List<PerfLogEventVO> events = createPerfLogEventVOs();
        for (var event : events) {
            aggregator.putEvent(event);
        }

        Double maxPedDelay = aggregator
                .getAggregatedValue(new MetricSpecifier(TrafficMetric.MAX_PED_DELAY.getId(), null));
        assertNotNull(maxPedDelay);
        assertEquals(10 * MINUTES_PER_HOUR, maxPedDelay);

        Double avgPedDelay = aggregator
                .getAggregatedValue(new MetricSpecifier(TrafficMetric.AVG_PED_DELAY.getId(), null));
        assertNotNull(avgPedDelay);
        assertEquals(7.5 * MINUTES_PER_HOUR, avgPedDelay);
    }

    private List<PerfLogEventVO> createPerfLogEventVOs() {
        List<PerfLogEventVO> eventList = new ArrayList<>();

        // 1. Add events DETECTOR 1
        // PED_DETECTOR_ON
        eventList.add(new PerfLogEventVO(T0.plusMinutes(5), Event.PED_DETECTOR_ON, DET_NUM_1));
        eventList.add(new PerfLogEventVO(T0.plusMinutes(8), Event.PED_DETECTOR_ON, DET_NUM_1));

        // Pedestrian events:
        // + PEDESTRIAN_BEGIN_WALK
        // + PEDESTRIAN_BEGIN_CLEARANCE
        // + PEDESTRIAN_BEGIN_NOT_WALK
        eventList.add(new PerfLogEventVO(T0.plusMinutes(10), Event.PEDESTRIAN_BEGIN_WALK, DET_NUM_1));
        eventList.add(new PerfLogEventVO(T0.plusMinutes(15), Event.PEDESTRIAN_BEGIN_CLEARANCE, DET_NUM_1));
        eventList.add(new PerfLogEventVO(T0.plusMinutes(20), Event.PEDESTRIAN_BEGIN_NOT_WALK, DET_NUM_1));

        // PED_DETECTOR_ON
        eventList.add(new PerfLogEventVO(T0.plusMinutes(25), Event.PED_DETECTOR_ON, DET_NUM_1));

        // Pedestrian events:
        // + PEDESTRIAN_BEGIN_WALKÏ
        // + PEDESTRIAN_BEGIN_CLEARANCE
        // + PEDESTRIAN_BEGIN_NOT_WALK
        eventList.add(new PerfLogEventVO(T0.plusMinutes(35), Event.PEDESTRIAN_BEGIN_WALK, DET_NUM_1));
        eventList.add(new PerfLogEventVO(T0.plusMinutes(40), Event.PEDESTRIAN_BEGIN_CLEARANCE, DET_NUM_1));
        eventList.add(new PerfLogEventVO(T0.plusMinutes(45), Event.PEDESTRIAN_BEGIN_NOT_WALK, DET_NUM_1));

        // 2. Add events DETECTOR 2
        // PED_DETECTOR_ON
        eventList.add(new PerfLogEventVO(T0.plusMinutes(6), Event.PED_DETECTOR_ON, DET_NUM_2));
        eventList.add(new PerfLogEventVO(T0.plusMinutes(12), Event.PED_DETECTOR_ON, DET_NUM_2));

        // Pedestrian events:
        // + PEDESTRIAN_BEGIN_WALK
        // + PEDESTRIAN_BEGIN_CLEARANCE
        // + PEDESTRIAN_BEGIN_NOT_WALK
        eventList.add(new PerfLogEventVO(T0.plusMinutes(18), Event.PEDESTRIAN_BEGIN_WALK, DET_NUM_2));
        eventList.add(new PerfLogEventVO(T0.plusMinutes(28), Event.PEDESTRIAN_BEGIN_CLEARANCE, DET_NUM_2));
        eventList.add(new PerfLogEventVO(T0.plusMinutes(30), Event.PEDESTRIAN_BEGIN_NOT_WALK, DET_NUM_2));

        // PED_DETECTOR_ON
        eventList.add(new PerfLogEventVO(T0.plusMinutes(35), Event.PED_DETECTOR_ON, DET_NUM_2));

        // Pedestrian events:
        // + PEDESTRIAN_BEGIN_WALKÏ
        // + PEDESTRIAN_BEGIN_CLEARANCE
        // + PEDESTRIAN_BEGIN_NOT_WALK
        eventList.add(new PerfLogEventVO(T0.plusMinutes(40), Event.PEDESTRIAN_BEGIN_WALK, DET_NUM_2));
        eventList.add(new PerfLogEventVO(T0.plusMinutes(45), Event.PEDESTRIAN_BEGIN_CLEARANCE, DET_NUM_2));
        eventList.add(new PerfLogEventVO(T0.plusMinutes(50), Event.PEDESTRIAN_BEGIN_NOT_WALK, DET_NUM_2));

        return eventList;
    }

    private IntersectionConfigVO createIntConfig() {
        final int[][] detCallPhases = {
                { 1 }, {}, {}, {}, {}, {}, {}, {}, {}, {},
                {}, {}, {}, {}, {}, {}, {}, {}, {}, {},
                {}, {}, {}, {}, {}, {}, {}, {}, {}, {},
                {}, {}, {}, {}, {}, {}, {}, {}, {}, {},
                {}, {}, {}, {}, {}, {}, {}, {}, {}, {},
                {}, {}, {}, {}, {}, {}, {}, {}, {}, {},
                {}, {}, {}, {}, {}, {}, {}, {}, {}, {},
                {}, {}, { 1 }, { 2 }, {}, {}, {}, {}, {}, {}
        };
        final int[] detMode = {
                1, 1, 1, 1, 1, 1, 1, 1, 0, 0,
                0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
                0, 0, 1, 1, 1, 1, 1, 1, 1, 1
        };
        DetectorInfoVO detInfo = DetectorInfoVO.builder()
                .detCallPhases(detCallPhases)
                .detMode(detMode)
                .build();
        return IntersectionConfigVO.builder()
                .detInfo(detInfo)
                .build();
    }

}
