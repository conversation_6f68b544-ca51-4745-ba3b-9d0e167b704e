package com.siemens.spm.analysis.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.CollectionType;
import com.siemens.spm.common.constant.IntersectionConstants;
import com.siemens.spm.perflog.vo.ApproachVO;
import com.siemens.spm.perflog.vo.IntersectionConfigVO;
import com.siemens.spm.perflog.vo.RingStructureInfoVO;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.data.util.Pair;

import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

class IntersectionConfigUtilsTest {

    @Test
    void testIsValidRingStructure_valid() {
        RingStructureInfoVO ringStructureInfoVO = new RingStructureInfoVO();
        ringStructureInfoVO.setSequenceData(new int[][][] { { { 1, 2, 3, 4 }, { 6, 5, 7, 8 } } });

        Assertions.assertTrue(IntersectionConfigUtils.isValidRingStructure(ringStructureInfoVO));
    }

    @Test
    void testIsValidRingStructure_valid2() {
        RingStructureInfoVO ringStructureInfoVO = new RingStructureInfoVO();
        ringStructureInfoVO.setSequenceData(new int[][][] { { { 1, 2, 4, 3 }, { 6, 5, 8, 7 } } });

        Assertions.assertTrue(IntersectionConfigUtils.isValidRingStructure(ringStructureInfoVO));
    }

    @Test
    void testIsValidRingStructure_valid3() {
        RingStructureInfoVO ringStructureInfoVO = new RingStructureInfoVO();
        ringStructureInfoVO.setSequenceData(new int[][][] { { { 2, 1, 3, 4 }, { 6, 5, 7, 8 } } });

        Assertions.assertTrue(IntersectionConfigUtils.isValidRingStructure(ringStructureInfoVO));
    }

    @Test
    void testIsValidRingStructure_invalidRing1() {
        RingStructureInfoVO ringStructureInfoVO = new RingStructureInfoVO();
        ringStructureInfoVO.setSequenceData(new int[][][] { { { 1, 3, 2, 4 }, { 6, 5, 7, 8 } } });

        Assertions.assertFalse(IntersectionConfigUtils.isValidRingStructure(ringStructureInfoVO));
    }

    @Test
    void testIsValidRingStructure_invalidRing2() {
        RingStructureInfoVO ringStructureInfoVO = new RingStructureInfoVO();
        ringStructureInfoVO.setSequenceData(new int[][][] { { { 1, 2, 2, 5 }, { 6, 5, 8, 7 } } });

        Assertions.assertFalse(IntersectionConfigUtils.isValidRingStructure(ringStructureInfoVO));
    }

    @Test
    void testResolvePairsOfPhasesYellowTrap_valid1() {
        IntersectionConfigVO configVO = new IntersectionConfigVO();
        ObjectMapper om = new ObjectMapper();
        CollectionType ct = om.getTypeFactory().constructCollectionType(List.class, ApproachVO.class);
        configVO.setApproaches(null);
        List<Pair<Long, Long>> actual2 = IntersectionConfigUtils.resolvePairsOfPhasesYellowTrap(configVO);
        List<Pair<Long, Long>> expect2 = IntersectionConstants.DEFAULT_PAIRS_OF_PHASE;
        assert2Pairs(actual2, expect2);
    }

    @Test
    void testResolvePairsOfPhasesYellowTrap_valid2() {
        IntersectionConfigVO configVO = new IntersectionConfigVO();
        ObjectMapper om = new ObjectMapper();
        CollectionType ct = om.getTypeFactory().constructCollectionType(List.class, ApproachVO.class);

        configVO.setApproaches(List.of());
        List<Pair<Long, Long>> actual3 = IntersectionConfigUtils.resolvePairsOfPhasesYellowTrap(configVO);
        List<Pair<Long, Long>> expect3 = IntersectionConstants.DEFAULT_PAIRS_OF_PHASE;
        assert2Pairs(actual3, expect3);
    }

    @Test
    void testResolvePairsOfPhasesYellowTrap_valid3() throws JsonProcessingException {
        IntersectionConfigVO configVO = new IntersectionConfigVO();
        ObjectMapper om = new ObjectMapper();
        CollectionType ct = om.getTypeFactory().constructCollectionType(List.class, ApproachVO.class);

        // intersection normal
        String rawApproachesJsonTestData = "[{\"Direction\":\"Eastbound\",\"UpstreamEntityID\":\"\",\"UpstreamDistance\":0,\"ApproachSpeed\":30,\"DownstreamDistance\":0,\"DownstreamEntityID\":\"\",\"Phases\":[{\"PhaseNumber\":3,\"Lanes\":[{\"Movement\":\"Left\",\"Detectors\":[{\"DetectorNumber\":3,\"Type\":\"ADVANCE\",\"Distance\":50}]}]},{\"PhaseNumber\":8,\"Lanes\":[{\"Movement\":\"Through/Right\",\"Detectors\":[{\"DetectorNumber\":8,\"Type\":\"ADVANCE\",\"Distance\":60.5}]}]}]},{\"Direction\":\"Westbound\",\"UpstreamEntityID\":\"\",\"UpstreamDistance\":0,\"ApproachSpeed\":30,\"DownstreamDistance\":0,\"DownstreamEntityID\":\"\",\"Phases\":[{\"PhaseNumber\":7,\"Lanes\":[{\"Movement\":\"Left\",\"Detectors\":[{\"DetectorNumber\":7,\"Type\":\"STOP_BAR\",\"Distance\":0}]}]},{\"PhaseNumber\":4,\"Lanes\":[{\"Movement\":\"Through/Right\",\"Detectors\":[{\"DetectorNumber\":4,\"Type\":\"STOP_BAR\",\"Distance\":0}]},{\"Movement\":\"Through\",\"Detectors\":[{\"DetectorNumber\":4,\"Type\":\"STOP_BAR\",\"Distance\":0}]}]}]},{\"Direction\":\"Southbound\",\"UpstreamEntityID\":\"\",\"UpstreamDistance\":0,\"ApproachSpeed\":30,\"DownstreamDistance\":0,\"DownstreamEntityID\":\"\",\"Phases\":[{\"PhaseNumber\":1,\"Lanes\":[{\"Movement\":\"Left\",\"Detectors\":[{\"DetectorNumber\":1,\"Type\":\"STOP_BAR\",\"Distance\":0}]}]},{\"PhaseNumber\":6,\"Lanes\":[{\"Movement\":\"Through/Right\",\"Detectors\":[{\"DetectorNumber\":6,\"Type\":\"STOP_BAR\",\"Distance\":0}]}]}]},{\"Direction\":\"Northbound\",\"UpstreamEntityID\":\"\",\"UpstreamDistance\":0,\"ApproachSpeed\":30,\"DownstreamDistance\":0,\"DownstreamEntityID\":\"\",\"Phases\":[{\"PhaseNumber\":5,\"Lanes\":[{\"Movement\":\"Left\",\"Detectors\":[{\"DetectorNumber\":5,\"Type\":\"STOP_BAR\",\"Distance\":0}]}]},{\"PhaseNumber\":2,\"Lanes\":[{\"Movement\":\"Through\",\"Detectors\":[{\"DetectorNumber\":2,\"Type\":\"STOP_BAR\",\"Distance\":0}]},{\"Movement\":\"Through/Right\",\"Detectors\":[{\"DetectorNumber\":5,\"Type\":\"STOP_BAR\",\"Distance\":0}]}]}]}]";
        List<ApproachVO> normalApproaches = om.readValue(
                rawApproachesJsonTestData,
                ct);
        configVO.setApproaches(normalApproaches);
        List<Pair<Long, Long>> actual1 = IntersectionConfigUtils.resolvePairsOfPhasesYellowTrap(configVO);
        List<Pair<Long, Long>> expect1 = IntersectionConstants.DEFAULT_PAIRS_OF_PHASE;
        assert2Pairs(actual1, expect1);
    }

    @Test
    void testResolvePairsOfPhasesYellowTrap_valid4() throws JsonProcessingException {
        IntersectionConfigVO configVO = new IntersectionConfigVO();
        ObjectMapper om = new ObjectMapper();
        CollectionType ct = om.getTypeFactory().constructCollectionType(List.class, ApproachVO.class);

        // intersection miami
        String rawApproachesJsonTestDataMiami = "[{\"Direction\":\"Eastbound\",\"UpstreamEntityID\":\"\",\"UpstreamDistance\":0,\"ApproachSpeed\":30,\"DownstreamDistance\":0,\"DownstreamEntityID\":\"\",\"Phases\":[{\"PhaseNumber\":3,\"Lanes\":[{\"Movement\":\"Left\",\"Detectors\":[{\"DetectorNumber\":3,\"Type\":\"STOP_BAR\",\"Distance\":0}]}]},{\"PhaseNumber\":8,\"Lanes\":[{\"Movement\":\"Through/Right\",\"Detectors\":[{\"DetectorNumber\":8,\"Type\":\"STOP_BAR\",\"Distance\":0}]},{\"Movement\":\"Left\",\"Detectors\":[{\"DetectorNumber\":8,\"Type\":\"STOP_BAR\",\"Distance\":0}]}]}]},{\"Direction\":\"Westbound\",\"UpstreamEntityID\":\"\",\"UpstreamDistance\":0,\"ApproachSpeed\":30,\"DownstreamDistance\":0,\"DownstreamEntityID\":\"\",\"Phases\":[{\"PhaseNumber\":7,\"Lanes\":[{\"Movement\":\"Left\",\"Detectors\":[{\"DetectorNumber\":7,\"Type\":\"STOP_BAR\",\"Distance\":0}]}]},{\"PhaseNumber\":4,\"Lanes\":[{\"Movement\":\"Through/Right\",\"Detectors\":[{\"DetectorNumber\":4,\"Type\":\"STOP_BAR\",\"Distance\":0}]},{\"Movement\":\"Left\",\"Detectors\":[{\"DetectorNumber\":4,\"Type\":\"STOP_BAR\",\"Distance\":0}]}]}]},{\"Direction\":\"Southbound\",\"UpstreamEntityID\":\"\",\"UpstreamDistance\":0,\"ApproachSpeed\":30,\"DownstreamDistance\":0,\"DownstreamEntityID\":\"\",\"Phases\":[{\"PhaseNumber\":1,\"Lanes\":[{\"Movement\":\"Left\",\"Detectors\":[{\"DetectorNumber\":1,\"Type\":\"STOP_BAR\",\"Distance\":0}]}]},{\"PhaseNumber\":6,\"Lanes\":[{\"Movement\":\"Through/Right\",\"Detectors\":[{\"DetectorNumber\":6,\"Type\":\"STOP_BAR\",\"Distance\":0}]},{\"Movement\":\"Left\",\"Detectors\":[{\"DetectorNumber\":6,\"Type\":\"STOP_BAR\",\"Distance\":0}]}]}]},{\"Direction\":\"Northbound\",\"UpstreamEntityID\":\"\",\"UpstreamDistance\":0,\"ApproachSpeed\":30,\"DownstreamDistance\":0,\"DownstreamEntityID\":\"\",\"Phases\":[{\"PhaseNumber\":5,\"Lanes\":[{\"Movement\":\"Left\",\"Detectors\":[{\"DetectorNumber\":5,\"Type\":\"STOP_BAR\",\"Distance\":0}]}]},{\"PhaseNumber\":2,\"Lanes\":[{\"Movement\":\"Through/Right\",\"Detectors\":[{\"DetectorNumber\":2,\"Type\":\"STOP_BAR\",\"Distance\":0}]},{\"Movement\":\"Left\",\"Detectors\":[{\"DetectorNumber\":2,\"Type\":\"STOP_BAR\",\"Distance\":0}]}]}]}]";
        List<ApproachVO> miamiApproaches = om.readValue(
                rawApproachesJsonTestDataMiami,
                ct);
        configVO.setApproaches(miamiApproaches);
        List<Pair<Long, Long>> actual4 = IntersectionConfigUtils.resolvePairsOfPhasesYellowTrap(configVO);
        List<Pair<Long, Long>> expect4 = List.of(
                Pair.of(2L, 6L),
                Pair.of(6L, 2L),
                Pair.of(4L, 8L),
                Pair.of(8L, 4L),
                Pair.of(1L, 2L),
                Pair.of(3L, 4L),
                Pair.of(5L, 6L),
                Pair.of(7L, 8L)
        );
        assert2Pairs(actual4, expect4);
    }

    private void assert2Pairs(List<Pair<Long, Long>> actual, List<Pair<Long, Long>> expect) {
        AtomicInteger count = new AtomicInteger(actual.size());
        actual.forEach(pairA -> {
            AtomicBoolean isOk = new AtomicBoolean(false);
            expect.forEach(pairE -> {
                if (pairA.getFirst().equals(pairE.getFirst()) && pairA.getSecond().equals(pairE.getSecond())) {
                    isOk.set(true);
                }
            });
            if (isOk.get()) {
                count.decrementAndGet();
            }
        });
        if (count.get() > 0) {
            Assertions.fail();
        } else {
            Assertions.assertTrue(true);
        }
    }
}
