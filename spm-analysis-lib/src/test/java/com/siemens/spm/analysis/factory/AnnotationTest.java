package com.siemens.spm.analysis.factory;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.List;

import org.junit.jupiter.api.Test;

import com.siemens.spm.analysis.factory.moe.MOEAnalysisFactory;
import com.siemens.spm.analysis.factory.queuelength.QueueLengthAnalysisFactory;
import com.siemens.spm.analysis.factory.redlightviolation.RedLightViolationAnalysisFactory;
import com.siemens.spm.analysis.factory.turningmovement.TurningMovementAnalysisFactory;
import com.siemens.spm.perflog.vo.DetectorVO.DetectorType;

class AnnotationTest {

    @Test
    void arrivalOn_advanceDetRequired() {
        AppDelayAnalysisFactory appDelayAnalysisFactory = new AppDelayAnalysisFactory(900);
        assertTrue(appDelayAnalysisFactory.getUsedDetTypes().contains(DetectorType.ADVANCE));

        AogAnalysisFactory aogAnalysisFactory = new AogAnalysisFactory(900);
        assertTrue(aogAnalysisFactory.getUsedDetTypes().contains(DetectorType.ADVANCE));

        AorAnalysisFactory aorAnalysisFactory = new AorAnalysisFactory(900);
        assertTrue(aorAnalysisFactory.getUsedDetTypes().contains(DetectorType.ADVANCE));

        CoordAnalysisFactory coordAnalysisFactory = new CoordAnalysisFactory(900);
        assertTrue(coordAnalysisFactory.getUsedDetTypes().contains(DetectorType.ADVANCE));

        MOEAnalysisFactory moeAnalysisFactory = new MOEAnalysisFactory();
        assertTrue(moeAnalysisFactory.getUsedDetTypes().contains(DetectorType.STOP_BAR));

        QueueLengthAnalysisFactory queueLengthAnalysisFactory = new QueueLengthAnalysisFactory(900);
        assertTrue(queueLengthAnalysisFactory.getUsedDetTypes().contains(DetectorType.ADVANCE));

        VolumeAnalysisFactory volumeAnalysisFactory = new VolumeAnalysisFactory(900);
        assertTrue(volumeAnalysisFactory.getUsedDetTypes().contains(DetectorType.ADVANCE));

        RedLightViolationAnalysisFactory redLightViolationAnalysisFactory = new RedLightViolationAnalysisFactory(900);
        assertEquals(redLightViolationAnalysisFactory.getUsedDetTypes(), List.of(DetectorType.RED_LIGHT_VIOLATION, DetectorType.STOP_BAR));
    }

    @Test
    void stopBarDetRequired() {
        SplitFailureAnalysisFactory splitFailureAnalysisFactory = new SplitFailureAnalysisFactory();
        assertTrue(splitFailureAnalysisFactory.getUsedDetTypes().contains(DetectorType.STOP_BAR));

        TurningMovementAnalysisFactory turningMovementAnalysisFactory = new TurningMovementAnalysisFactory(900);
        assertTrue(turningMovementAnalysisFactory.getUsedDetTypes().contains(DetectorType.STOP_BAR));

        QueueLengthAnalysisFactory queueLengthAnalysisFactory = new QueueLengthAnalysisFactory(900);
        assertTrue(queueLengthAnalysisFactory.getUsedDetTypes().contains(DetectorType.STOP_BAR));
    }

    @Test
    void RedLightDetRequired() {
        RedLightViolationAnalysisFactory redLightViolationAnalysisFactory = new RedLightViolationAnalysisFactory(900);
        assertTrue(redLightViolationAnalysisFactory.getUsedDetTypes().contains(DetectorType.RED_LIGHT_VIOLATION));
    }
}
