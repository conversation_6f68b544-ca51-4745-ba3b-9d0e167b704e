/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : SimpleResultObject.java
 * Project     : SPM Platform
 */
package com.siemens.spm.common.shared.vo;

import org.springframework.http.HttpStatus;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.siemens.spm.common.shared.vo.SimpleResultObject.SimpleStatusCode;

import lombok.AllArgsConstructor;
import lombok.Getter;

@JsonInclude(Include.NON_NULL)
public class SimpleResultObject extends AbstractSimpleResultObject<SimpleStatusCode> {

    private static final long serialVersionUID = 1815205662374879077L;

    private SimpleStatusCode statusCode;

    /**
     * Constructor for ERROR case
     */
    public SimpleResultObject() {
    }

    /**
     * Constructor to set status to other than success
     *
     * @param statusCode
     */
    public SimpleResultObject(SimpleStatusCode statusCode) {
        super(statusCode);
    }

    @Override
    public SimpleStatusCode getStatusCode() {
        return statusCode;
    }

    @Override
    public HttpStatus getHttpStatus() {
        return statusCode.getHttpStatus();
    }

    @Override
    public String getErrorFieldName() {
        return statusCode.getErrorField();
    }

    @Override
    protected void setStatusCode(SimpleStatusCode value) {
        this.statusCode = value;
    }

    @Override
    protected SimpleStatusCode getErrorStatusValue() {

        return SimpleStatusCode.ERROR;
    }

    @Override
    public SimpleStatusCode getSuccessfulStatusValue() {
        return SimpleStatusCode.SUCCESS;
    }

    @Override
    public String getMessage() {
        return statusCode.getMessage();
    }

    @AllArgsConstructor
    @Getter
    public enum SimpleStatusCode {
        ERROR("unknown_exception", HttpStatus.INTERNAL_SERVER_ERROR),
        SUCCESS("success", HttpStatus.OK),
        NO_DATA("no_data", HttpStatus.NO_CONTENT);

        private final String errorField;
        private final String message;
        private final HttpStatus httpStatus;

        SimpleStatusCode(String message, HttpStatus httpStatus) {
            this(null, message, httpStatus);
        }
    }
}
