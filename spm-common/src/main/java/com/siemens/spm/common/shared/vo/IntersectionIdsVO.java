package com.siemens.spm.common.shared.vo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import jakarta.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.constant.IntersectionConstants;
import com.siemens.spm.common.shared.domaintype.IntersectionScope;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class IntersectionIdsVO implements Serializable {

    private static final long serialVersionUID = 5062631628165301922L;

    @NotNull(message = "templates.intersections.scope_not_null")
    @JsonProperty("scope")
    private IntersectionScope scope;

    @JsonProperty("uuids")
    private List<String> uuids;

    @JsonIgnore
    public static IntersectionIdsVO resolveAllIntersection() {
        return IntersectionIdsVO.builder()
                .scope(IntersectionScope.ALL_INTERSECTIONS)
                .uuids(Collections.emptyList())
                .build();
    }

    @JsonIgnore
    public static IntersectionIdsVO resolveSpecificIntersectionFromString(@NotNull String intersectionIdString) {
        List<String> uuids = Arrays
                .asList(intersectionIdString.split(IntersectionConstants.INTERSECTION_SEPARATE_INDICATOR));

        return IntersectionIdsVO.builder()
                .scope(IntersectionScope.SPECIFIC_INTERSECTIONS)
                .uuids(uuids)
                .build();
    }

    @JsonIgnore
    public static IntersectionIdsVO resolve(String intersectionIdsString) {
        if (IntersectionConstants.ALL_INTERSECTION_INDICATOR.equals(intersectionIdsString)) {
            return resolveAllIntersection();
        } else {
            return resolveSpecificIntersectionFromString(intersectionIdsString);
        }
    }

    @JsonIgnore
    public static String resolveIntersectionIdsString(@NotNull IntersectionIdsVO intersectionIdsVO) {
        if (IntersectionScope.ALL_INTERSECTIONS.equals(intersectionIdsVO.getScope())) {
            return IntersectionConstants.ALL_INTERSECTION_INDICATOR;
        } else {
            List<String> uuids = intersectionIdsVO.getUuids();
            if (uuids == null)
                throw new IllegalArgumentException("Can not resolve intersection ids string when uuids list is null!");
            String intersectionListString = "";
            for (int i = 0; i < uuids.size(); i++) {
                intersectionListString = intersectionListString.concat(uuids.get(i));
                if (i != uuids.size() - 1) {
                    intersectionListString = intersectionListString
                            .concat(IntersectionConstants.INTERSECTION_SEPARATE_INDICATOR);
                }
            }
            return intersectionListString;
        }
    }

    @JsonIgnore
    public static List<String> resolveIntersectionIdsList(@NotNull IntersectionIdsVO intersectionIdsVO) {
        List<String> uuids = intersectionIdsVO.getUuids();
        if (uuids == null || IntersectionScope.ALL_INTERSECTIONS.equals(intersectionIdsVO.getScope())) {
            uuids = Collections.emptyList();
        }

        // Remove duplicated ids
        Set<String> uuidsSet = new HashSet<>(uuids);

        return new ArrayList<>(uuidsSet);
    }

}
