/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : ConditionChecker.java
 * Project     : SPM Platform
 */

package com.siemens.spm.common.shared.domaintype.alarm.condition;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.siemens.spm.common.shared.domaintype.alarm.trafficmetric.MetricSpecifier;

/**
 * This is a helper class to check condition group
 *
 * <AUTHOR>
 *
 */
public final class ConditionChecker {

    private ConditionChecker() {}

    /**
     * Check a simple condition (which contains metric id, operator and metric
     * threshold)
     * 
     * @param trafficMetricToValueMap
     * @param condition
     * @return MetricSpecifier of metric on a target which excesses threshold in
     *         condition
     */
    private static MetricSpecifier checkCondition(Map<MetricSpecifier, Double> trafficMetricToValueMap,
                                                  ConditionGroup condition) {
        if (condition == null || !condition.isCondition()) {
            throw new IllegalArgumentException("input condition must be a simple condition!");
        }

        MetricSpecifier metricSpecifier = condition.getMetricSpecifier();
        Double metricThreshold = condition.getValue();
        Operator operator = condition.getOperator();

        Double metricValue = trafficMetricToValueMap.get(metricSpecifier);

        // if metric's value does not exist in the map
        if (metricValue == null) {
            return null;
        }

        boolean result;

        switch (operator) {
            case EQUALS:
                result = Double.compare(metricValue, metricThreshold) == 0;
                break;

            case GREATER_OR_EQUAL:
                result = Double.compare(metricValue, metricThreshold) >= 0;
                break;

            case GREATER_THAN:
                result = Double.compare(metricValue, metricThreshold) > 0;
                break;

            case LESS_OR_EQUAL:
                result = Double.compare(metricValue, metricThreshold) <= 0;
                break;

            case LESS_THAN:
                result = Double.compare(metricValue, metricThreshold) < 0;
                break;

            case NOT_EQUAL:
                result = Double.compare(metricValue, metricThreshold) != 0;
                break;

            default:
                result = false;
                break;
        }

        // if condition is met -> return metric specifier which crosses its threshold
        // if condition is NOT met -> return null
        return result ? metricSpecifier : null;
    }

    // check a condition group (which can be a simple condition or can contain
    // conjunction and a list of simple conditions)
    public static Set<MetricSpecifier> checkConditionGroup(Map<MetricSpecifier, Double> trafficMetricToValueMap,
                                                           ConditionGroup condition) {
        Set<MetricSpecifier> crossThresholdMetricSpecSet = new HashSet<>();

        if (condition.isCondition()) {
            MetricSpecifier crossThresholdMetricSpec = checkCondition(trafficMetricToValueMap, condition);

            if (crossThresholdMetricSpec != null) {
                crossThresholdMetricSpecSet.add(crossThresholdMetricSpec);
            }
        } else if (condition.isConditionGroup()) {
            Conjunction conjunction = condition.getConjunction();
            ConditionGroup[] subConditionGroups = condition.getConditions();

            Set<MetricSpecifier> crossThresholdMetricSpecOfSubConditionGroups = new HashSet<>();
            List<Boolean> checkSubConditionGroupsResults = new ArrayList<>();

            for (ConditionGroup subConditionGroup : subConditionGroups) {
                Set<MetricSpecifier> crossThresholdMetricSpecOfSubConditionGroup = checkConditionGroup(
                        trafficMetricToValueMap,
                        subConditionGroup);

                checkSubConditionGroupsResults.add(!crossThresholdMetricSpecOfSubConditionGroup.isEmpty());
                crossThresholdMetricSpecOfSubConditionGroups.addAll(crossThresholdMetricSpecOfSubConditionGroup);
            }

            boolean checkConditionGroupResult;

            if (conjunction == Conjunction.AND) {
                // only 1 result is false -> the final result will be false
                checkConditionGroupResult = !checkSubConditionGroupsResults.contains(false);
            } else if (conjunction == Conjunction.OR) {
                // only 1 result is true -> the final result will be true
                checkConditionGroupResult = checkSubConditionGroupsResults.contains(true);
            } else {
                checkConditionGroupResult = checkSubConditionGroupsResults.get(0);
            }

            if (checkConditionGroupResult) {
                crossThresholdMetricSpecSet.addAll(crossThresholdMetricSpecOfSubConditionGroups);
            }
        } else {
            throw new IllegalArgumentException("input condition is invalid!");
        }

        return crossThresholdMetricSpecSet;
    }

}
