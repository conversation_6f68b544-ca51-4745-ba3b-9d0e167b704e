/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : RuleDetailActionDataVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.common.shared.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class RuleDetailActionDataVO extends SimpleActionDataVO {

    private static final long serialVersionUID = -4263851185183764797L;

}
