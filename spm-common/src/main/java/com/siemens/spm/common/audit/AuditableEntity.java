/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AuditableEntity.java
 * Project     : SPM Platform
 */
package com.siemens.spm.common.audit;

import java.sql.Timestamp;

import jakarta.persistence.Column;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.MappedSuperclass;

import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import lombok.Getter;
import lombok.Setter;

/**
 * Base class of all auditable entities. It contains name of user that changed
 * the entity and the time stamp at which the entity was changed.
 * 
 * <AUTHOR>
 */
@MappedSuperclass
@EntityListeners({ AuditingEntityListener.class, ActivityListener.class })
@Setter
@Getter
public abstract class AuditableEntity {

    public static final class ColumnName {

        private ColumnName() {}

        public static final String CREATED_BY = "created_by";
        public static final String CREATED_AT = "created_at";
        public static final String LAST_MODIFIED_BY = "last_modified_by";
        public static final String LAST_MODIFIED_AT = "last_modified_at";

    }

    @CreatedBy
    @Column(name = ColumnName.CREATED_BY, updatable = false)
    protected String createdBy;

    @CreatedDate
    @Column(name = ColumnName.CREATED_AT, updatable = false)
    protected Timestamp createdAt;

    @LastModifiedBy
    @Column(name = ColumnName.LAST_MODIFIED_BY)
    protected String lastModifiedBy;

    @LastModifiedDate
    @Column(name = ColumnName.LAST_MODIFIED_AT)
    protected Timestamp lastModifiedAt;

}
