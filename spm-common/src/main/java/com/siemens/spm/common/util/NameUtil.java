package com.siemens.spm.common.util;

import org.springframework.util.StringUtils;

public class NameUtil {

    private NameUtil() {
    }

    public static String englishFullName(String firstName, String lastName) {
        if (StringUtils.hasText(firstName)) {
            if (StringUtils.hasText(lastName)) {
                return firstName + " " + lastName;
            }

            return firstName;
        }
        return lastName;
    }

    public static String usFullName(String firstName, String lastName) {
        if (StringUtils.hasText(firstName)) {
            if (StringUtils.hasText(lastName)) {
                return lastName + ", " + firstName;
            }

            return firstName;
        }
        return lastName;
    }

}
