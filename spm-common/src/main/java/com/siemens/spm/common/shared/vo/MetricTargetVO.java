/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : MetricTargetVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.common.shared.vo;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.siemens.spm.common.shared.domaintype.alarm.trafficmetric.MetricTarget;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MetricTargetVO implements Serializable {

    /**
     * 
     */
    private static final long serialVersionUID = 4308917539487111106L;

    private MetricTarget.TargetType type;

    private String identifier;

}
