package com.siemens.spm.common.shared.domaintype;

import java.util.Arrays;
import java.util.Optional;

public enum IntersectionScope {

    ALL_INTERSECTIONS, SPECIFIC_INTERSECTIONS;

    public static Optional<IntersectionScope> resolve(String value) {
        if (value == null) {
            return Optional.empty();

        }
        return Arrays.stream(IntersectionScope.values())
                .filter(scope -> scope.name().equalsIgnoreCase(value))
                .findFirst();
    }

}
