/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : UUIDConstants.java
 * Project     : SPM Platform
 */

package com.siemens.spm.common.shared.resource;

import java.util.UUID;

/**
 *
 * <AUTHOR>
 *
 */
public final class UUIDConstants {

    private UUIDConstants() {
    }

    public static final String REGEXP_FOR_1_UUID_V4 = "(([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}){1})";

    public static final String ALL_AGENCIES_AGENCY_ID = new UUID( 0L , 0L ).toString();

    public static final String REGEXP_FOR_1_AGENCY_ID = REGEXP_FOR_1_UUID_V4;
    
    public static final String REGEXP_FOR_1_INTERSECTION_ID = REGEXP_FOR_1_UUID_V4;

    public static final String REGEXP_FOR_1_CORRIDOR_ID = REGEXP_FOR_1_UUID_V4;

}
