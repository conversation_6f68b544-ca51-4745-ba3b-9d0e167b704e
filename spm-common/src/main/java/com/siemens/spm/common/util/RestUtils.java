package com.siemens.spm.common.util;

import com.siemens.spm.common.exception.RestApiException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.util.Assert;
import org.springframework.web.client.RestClientException;

import java.util.Optional;

import static com.siemens.spm.common.constant.AgencyConstants.AGENCY_ID_HEADER;

@Slf4j
public final class RestUtils {
    private static final long MAX_RETRY_DELAY_MS = 5000;
    private static final double BACKOFF_MULTIPLIER = 2.0;
    private static final int MAX_RETRY_ATTEMPTS = 3;

    public static final String URL_MUST_NOT_BE_EMPTY = "URL must not be empty";
    public static final String RESPONSE_TYPE_MUST_NOT_BE_NULL = "Response type must not be null";
    public static final String AGENCY_ID_MUST_NOT_BE_EMPTY = "Agency ID must not be empty";

    private RestUtils() {
        throw new AssertionError("Utility class - do not instantiate");
    }

    public static <T> ResponseEntity<T> get(String url, Class<T> responseType) throws RestApiException {
        validateParameters(url, responseType);
        return executeRestCall(url, HttpMethod.GET, null, null, responseType);
    }

    public static <T> ResponseEntity<T> get(String url, ParameterizedTypeReference<T> responseType)
            throws RestApiException {
        validateParameters(url, responseType);
        return executeRestCall(url, HttpMethod.GET, null, null, responseType);
    }

    public static <T> ResponseEntity<T> get(String url, HttpHeaders headers, Class<T> responseType)
            throws RestApiException {
        validateParameters(url, responseType);
        return executeRestCall(url, HttpMethod.GET, null, getHeadersOrDefault(headers), responseType);
    }

    public static <T> ResponseEntity<T> getWithAgencyHeader(String url, String agencyId, Class<T> responseType)
            throws RestApiException {
        validateParametersWithAgency(url, agencyId, responseType);
        return executeRestCall(url, HttpMethod.GET, null, createHeadersWithAgency(agencyId), responseType);
    }

    public static <T> ResponseEntity<T> getWithAgencyHeader(String url, String agencyId,
            ParameterizedTypeReference<T> responseType) throws RestApiException {
        validateParametersWithAgency(url, agencyId, responseType);
        return executeRestCall(url, HttpMethod.GET, null, createHeadersWithAgency(agencyId), responseType);
    }

    public static <T> ResponseEntity<T> post(String url, Object requestBody, Class<T> responseType)
            throws RestApiException {
        validateParameters(url, responseType);
        return executeRestCall(url, HttpMethod.POST, requestBody, null, responseType);
    }

    public static <T> ResponseEntity<T> post(String url, Object requestBody, HttpHeaders headers, Class<T> responseType)
            throws RestApiException {
        validateParameters(url, responseType);
        return executeRestCall(url, HttpMethod.POST, requestBody, getHeadersOrDefault(headers), responseType);
    }

    public static <T> ResponseEntity<T> postWithAgencyHeader(String url, String agencyId, Object requestBody,
            Class<T> responseType) throws RestApiException {
        validateParametersWithAgency(url, agencyId, responseType);
        return executeRestCall(url, HttpMethod.POST, requestBody, createHeadersWithAgency(agencyId), responseType);
    }

    public static <T> ResponseEntity<T> postWithAgencyHeader(String url, String agencyId, Object requestBody,
        ParameterizedTypeReference<T> responseType) throws RestApiException {
        validateParametersWithAgency(url, agencyId, responseType);
        return executeRestCall(url, HttpMethod.POST, requestBody, createHeadersWithAgency(agencyId), responseType);
    }

    public static <T> ResponseEntity<T> put(String url, Object requestBody, Class<T> responseType)
            throws RestApiException {
        validateParameters(url, responseType);
        return executeRestCall(url, HttpMethod.PUT, requestBody, null, responseType);
    }

    public static <T> ResponseEntity<T> put(String url, Object requestBody, HttpHeaders headers, Class<T> responseType)
            throws RestApiException {
        validateParameters(url, responseType);
        return executeRestCall(url, HttpMethod.PUT, requestBody, getHeadersOrDefault(headers), responseType);
    }

    public static <T> ResponseEntity<T> putWithAgencyHeader(String url, String agencyId, Object requestBody,
            Class<T> responseType) throws RestApiException {
        validateParametersWithAgency(url, agencyId, responseType);
        return executeRestCall(url, HttpMethod.PUT, requestBody, createHeadersWithAgency(agencyId), responseType);
    }

    public static <T> ResponseEntity<T> putWithAgencyHeader(String url, String agencyId, Object requestBody,
        ParameterizedTypeReference<T> responseType) throws RestApiException {
        validateParametersWithAgency(url, agencyId, responseType);
        return executeRestCall(url, HttpMethod.PUT, requestBody, createHeadersWithAgency(agencyId), responseType);
    }

    public static <T> ResponseEntity<T> delete(String url, Class<T> responseType) throws RestApiException {
        validateParameters(url, responseType);
        return executeRestCall(url, HttpMethod.DELETE, null, null, responseType);
    }

    public static <T> ResponseEntity<T> delete(String url, HttpHeaders headers, Class<T> responseType)
            throws RestApiException {
        validateParameters(url, responseType);
        return executeRestCall(url, HttpMethod.DELETE, null, getHeadersOrDefault(headers), responseType);
    }

    public static <T> ResponseEntity<T> deleteWithAgencyHeader(String url, String agencyId, Class<T> responseType)
            throws RestApiException {
        validateParametersWithAgency(url, agencyId, responseType);
        return executeRestCall(url, HttpMethod.DELETE, null, createHeadersWithAgency(agencyId), responseType);
    }

    public static <T> ResponseEntity<T> deleteWithAgencyHeader(String url, String agencyId,
        ParameterizedTypeReference<T> responseType) throws RestApiException {
        validateParametersWithAgency(url, agencyId, responseType);
        return executeRestCall(url, HttpMethod.DELETE, null, createHeadersWithAgency(agencyId), responseType);
    }

    private static void validateParameters(String url, Class<?> responseType) {
        Assert.hasText(url, URL_MUST_NOT_BE_EMPTY);
        Assert.notNull(responseType, RESPONSE_TYPE_MUST_NOT_BE_NULL);
    }

    private static <T> void validateParameters(String url, ParameterizedTypeReference<T> responseType) {
        Assert.hasText(url, URL_MUST_NOT_BE_EMPTY);
        Assert.notNull(responseType, RESPONSE_TYPE_MUST_NOT_BE_NULL);
    }

    private static void validateParametersWithAgency(String url, String agencyId, Class<?> responseType) {
        validateParameters(url, responseType);
        Assert.hasText(agencyId, AGENCY_ID_MUST_NOT_BE_EMPTY);
    }

    private static <T> void validateParametersWithAgency(String url, String agencyId,
            ParameterizedTypeReference<T> responseType) {
        validateParameters(url, responseType);
        Assert.hasText(agencyId, AGENCY_ID_MUST_NOT_BE_EMPTY);
    }

    private static HttpHeaders getHeadersOrDefault(HttpHeaders headers) {
        return Optional.ofNullable(headers).orElseGet(RestUtils::createDefaultHeaders);
    }

    private static HttpHeaders createHeadersWithAgency(String agencyId) {
        HttpHeaders headers = createDefaultHeaders();
        headers.add(AGENCY_ID_HEADER, agencyId);
        return headers;
    }

    private static HttpHeaders createDefaultHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        return headers;
    }

    @Retryable(
            retryFor = {RestClientException.class},
            maxAttempts = MAX_RETRY_ATTEMPTS,
            backoff = @Backoff(
                    maxDelay = MAX_RETRY_DELAY_MS,
                    multiplier = BACKOFF_MULTIPLIER
            )
    )
    private static <T> ResponseEntity<T> executeRestCall(String url, HttpMethod method, Object requestBody,
            HttpHeaders headers, Class<T> responseType) throws RestApiException {
        try {
            HttpEntity<?> requestEntity = new HttpEntity<>(requestBody, getHeadersOrDefault(headers));
            log.debug("Executing REST call to {} with method {}", url, method);

            ResponseEntity<T> response =
                    BeanFinder.getDefaultRestTemplate().exchange(url, method, requestEntity, responseType);

            log.debug("Received response with status: {}", response.getStatusCode());
            return response;

        } catch (RestClientException e) {
            log.error("REST call failed for URL: {} with method: {}", url, method, e);
            throw new RestApiException(String.format("Failed to execute REST call to %s: %s", url, e.getMessage()), e);
        }
    }

    @Retryable(
            retryFor = {RestClientException.class},
            maxAttempts = MAX_RETRY_ATTEMPTS,
            backoff = @Backoff(
                    maxDelay = MAX_RETRY_DELAY_MS,
                    multiplier = BACKOFF_MULTIPLIER
            )
    )
    private static <T> ResponseEntity<T> executeRestCall(String url, HttpMethod method, Object requestBody,
            HttpHeaders headers, ParameterizedTypeReference<T> responseType) throws RestApiException {
        try {
            HttpEntity<?> requestEntity = new HttpEntity<>(requestBody, getHeadersOrDefault(headers));
            log.debug("Executing REST call to {} with method {}", url, method);

            ResponseEntity<T> response =
                    BeanFinder.getDefaultRestTemplate().exchange(url, method, requestEntity, responseType);

            log.debug("Received response with status: {}", response.getStatusCode());
            return response;

        } catch (RestClientException e) {
            log.error("REST call failed for URL: {} with method: {}", url, method, e);
            throw new RestApiException(String.format("Failed to execute REST call to %s: %s", url, e.getMessage()), e);
        }
    }
}
