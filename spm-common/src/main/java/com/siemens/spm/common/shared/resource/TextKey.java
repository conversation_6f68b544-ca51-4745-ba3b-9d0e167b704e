/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : TextKey.java
 * Project     : SPM Platform
 */
package com.siemens.spm.common.shared.resource;

/**
 * Define all text keys for translation
 */
public final class TextKey {


    /**
     * Hidden constructor
     **/
    private TextKey() {
    }

    public static final String ALL = "all";
    public static final String ARRIVALS_ON_GREEN = "arrivals_on_green";
    public static final String ARRIVALS_ON_RED = "arrivals_on_red";
    public static final String VOLUME = "volume";
    public static final String SPLIT_MONITOR = "split_monitor";
    public static final String ABNORMAL_DATA = "abnormal_data";
    public static final String PREEMPTION_PRIORITY = "preemption_priority";
    public static final String TURNING_MOVEMENT = "turning_movement";
    public static final String RED_LIGHT_VIOLATION = "red_light_violation";
    public static final String YELLOW_TRAP_OCCURRENCES = "yellow_trap_occurrences";
    public static final String ARRIVALS_ON_YELLOW = "arrivals_on_yellow";
    public static final String COORDINATION = "coordination";
    public static final String DETECTOR_ACTUATION = "detector_actuation";
    public static final String EXPORT_COMPLETED_CLICK_TO_DOWNLOAD = "export_completed_click_here_to_download";
    public static final String EXPORT_COMPLETED = "export_completed";
    public static final String EXPORT_FAILED = "export_failed";
    public static final String EXPORT_FAILED_PLEASE_TRY_AGAIN = "export_failed_please_try_again";
    public static final String EXPORT_FAILED_MESSAGE = "export_failed_message";
    public static final String SUMMARY_REPORT_FAILED_MESSAGE = "summary_report_failed_message";
    public static final String PERFORMANCE_METRIC_FAILED_MESSAGE = "performance_metric_failed_message";
    public static final String DETECTOR_REPORT_FAILED_MESSAGE = "detector_report_failed_message";
    public static final String EXPORT_PROGRESS_TITLE = "export_progress_title";
    public static final String SUMMARY_REPORT_TITLE = "summary_report_title";
    public static final String DETECTOR_REPORT_TITLE = "detector_report_title";
    public static final String PERFORMANCE_METRIC_TITLE = "performance_metric_title";
    public static final String EXPORT_PROGRESS_DESCRIPTION = "export_progress_description";
    public static final String SUMMARY_REPORT_DESCRIPTION = "summary_report_description";

    public static final String MOE_ANALYSIS = "moe_analysis";
    public static final String DETECTOR_REPORT_ANALYSIS = "detector_report";
    public static final String PERFORMANCE_METRIC_DESCRIPTION = "performance_metric_description";
    public static final String DETECTOR_REPORT_DESCRIPTION = "detector_report_description";
    public static final String FORCE_OFF = "force_off";
    public static final String GAP_OUT = "gap_out";
    public static final String MAX_OUT = "max_out";
    public static final String PEDESTRIAN_PHASE_CALLS = "pedestrian_phase_calls";
    public static final String PHASE_CALLS = "phase_calls";
    public static final String PHASE_TERMINATIONS = "phase_terminations";
    public static final String UNIT_PERCENT = "unit.percent";
    public static final String UNIT_RAW_COUNT = "unit.raw_count";
    public static final String UNIT_HOUR = "unit.hour";
    public static final String UNIT_VPH = "unit.vph";
    public static final String UNIT_SECOND = "unit.second";
    public static final String UNIT_SECOND_DASHBOARD = "unit.second.dashboard";
    public static final String UNIT_VEHICLE = "unit.vehicle";
    public static final String DOWNLOAD_BTN_TITLE = "download_btn_title";
    public static final String VIEW_BTN_TITLE = "view_btn_title";
    public static final String RELEASE_NOTES_TITLE = "release_notes_title";
    public static final String RELEASE_NOTES_DESC = "release_notes_desc";
    public static final String PEDESTRIAN = "pedestrian";
    public static final String MAX_PED_DELAY = "max_ped_delay";
    public static final String AVG_PED_DELAY = "avg_ped_delay";
    public static final String NO_DATA = "no_data";
    public static final String COORDINATION_HEALTH = "coordination_health";
    public static final String SPLIT_FAILURE = "split_failure";
    public static final String APPROACH_DELAY = "approach_delay";
    public static final String QUEUE_LENGTH = "queue_length";
    public static final String TRANSITION_PERCENT = "transition_percent";
    public static final String MAX_GOR_PERCENT = "max_gor_percent";
    public static final String AVG_GOR_PERCENT = "avg_gor_percent";
    public static final String MAX_ROR5_PERCENT = "max_ror5_percent";
    public static final String AVG_ROR5_PERCENT = "avg_ror5_percent";
    public static final String POOR_COORDINATION = "poor_coordination";
    public static final String EXCESS_CAPACITY = "excess_capacity";
    public static final String AVG_APPROACH_DELAY = "avg_approach_delay";
    public static final String AVG_QUEUE_LENGTH = "avg_queue_length";

    public static final String DIRECTION_EASTBOUND = "eastbound";
    public static final String DIRECTION_WESTBOUND = "westbound";
    public static final String DIRECTION_NORTHBOUND = "northbound";
    public static final String DIRECTION_SOUTHBOUND = "southbound";
    public static final String DIRECTION_OTHER = "other";

    public static final String MOVEMENT_RIGHT = "move_right";
    public static final String MOVEMENT_LEFT = "move_left";
    public static final String MOVEMENT_THROUGH = "move_through";
    public static final String MOVEMENT_THROUGH_RIGHT = "move_through_right";
    public static final String MOVEMENT_THROUGH_LEFT = "move_through_left";
    public static final String DETECTOR_ADVANCE = "detector_type_advance";
    public static final String DETECTOR_STOPBAR = "detector_type_stopbar";
    public static final String DETECTOR_RED_LIGHT_VIOLATION = "detector_type_red_light_violation";
    public static final String DETECTOR_PEDESTRIAN = "detector_type_pedestrian";

    public static final String LEFT = "Left";

    public static final String RIGHT = "Right";
    public static final String THOUGHT = "Through";

    public static final String DETECTOR_REPORT = "detector-report-description";

    public static final String SUMMARY_REPORT = "summary-report";
    public static final String PERFORMANCE_METRIC = "performance-metric";
}
