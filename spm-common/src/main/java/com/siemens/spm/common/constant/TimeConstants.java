/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : TimeConstants.java
 * Project     : SPM Platform
 */

package com.siemens.spm.common.constant;

import java.time.ZoneId;
import java.util.concurrent.TimeUnit;

public final class TimeConstants {

    private TimeConstants() {
    }

    public static final long NUMBER_OF_MILISECONDS_IN_1_SECOND = TimeUnit.SECONDS.toMillis(1);

    public static final long NUMBER_OF_SECONDS_IN_1_MINUTE = TimeUnit.MINUTES.toSeconds(1);

    public static final long NUMBER_OF_SECONDS_IN_1_HOUR = TimeUnit.HOURS.toSeconds(1);

    public static final long NUMBER_OF_SECONDS_IN_1_DAY = TimeUnit.DAYS.toSeconds(1);

    public static final long NUMBER_OF_MINUTES_IN_1_HOUR = TimeUnit.HOURS.toMinutes(1);

    public static final long NUMBER_OF_MINUTES_IN_1_DAY = TimeUnit.DAYS.toMinutes(1);

    public static final long NUMBER_OF_HOURS_IN_1_DAY = TimeUnit.DAYS.toHours(1);

    // present supported formats are "-hh:mm", "+hh:mm" and "Z"
    public static final String REGEXP_FOR_ZONE_OFFSET = "^(?:Z|[+-](?:2[0-3]|[01][0-9]):[0-5][0-9])$";
    public static final String REGEXP_FOR_ZONE_ID = "^[A-Za-z_]+(?:/[A-Za-z_]+)+$";

    // support format "hh:00"
    public static final String REGEXP_FOR_ROUNDED_HOURS = "^((?:2[0-3]|[01][0-9]):00)$";

    public static final int MIN_DAY_PER_WEEK = 1;

    public static final int MAX_DAY_PER_WEEK = 7;

    public static final int MIN_DAY_PER_MONTH = 1;

    public static final int MAX_DAY_PER_MONTH = 31;

    public static final int MAX_WEEK_PER_MONTH = 4;

    public static final int MAX_MONTH_PER_YEAR = 12;

    public static final ZoneId UTC_ZONE_ID = ZoneId.of("UTC");

}
