package com.siemens.spm.common.constant;

import java.util.List;

import org.springframework.data.util.Pair;

public final class IntersectionConstants {

    private IntersectionConstants() {
    }

    public static final String ALL_INTERSECTION_INDICATOR = "*";

    public static final String INTERSECTION_SEPARATE_INDICATOR = ",";
    public static final List<Pair<Long, Long>> DEFAULT_PAIRS_OF_PHASE = List.of(
            Pair.of(1L, 2L),
            Pair.of(3L, 4L),
            Pair.of(5L, 6L),
            Pair.of(7L, 8L)
    );

}
