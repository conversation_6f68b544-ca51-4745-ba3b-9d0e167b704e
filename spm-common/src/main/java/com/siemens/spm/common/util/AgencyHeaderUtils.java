package com.siemens.spm.common.util;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;

import static com.siemens.spm.common.constant.AgencyConstants.AGENCY_ID_HEADER;

public final class AgencyHeaderUtils {

    private AgencyHeaderUtils() {
    }

    public static HttpEntity<Object> createRequestWithHeadersEntity(String agencyId, Object request) {
        HttpHeaders headers = new HttpHeaders();
        headers.add(AGENCY_ID_HEADER, agencyId);
        if (request == null) {
            return new HttpEntity<>(headers);
        }

        return new HttpEntity<>(request, headers);
    }

    public static HttpEntity<Object> createHeadersEntity(String agencyId) {
        return createRequestWithHeadersEntity(agencyId, null);
    }

}
