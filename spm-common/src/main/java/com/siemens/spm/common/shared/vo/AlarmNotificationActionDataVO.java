/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AnalysisActionDataVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.common.shared.vo;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.shared.domaintype.notification.NotificationType;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class AlarmNotificationActionDataVO implements Serializable {

    private static final long serialVersionUID = -2079406074474339416L;

    @JsonProperty("type_id")
    @Builder.Default
    private final Long typeID = NotificationType.ALARM_NOTIFICATION.getId();

    @JsonProperty("intersection")
    private String intUUID;

    @JsonProperty("read_status")
    private String readStatus;

}
