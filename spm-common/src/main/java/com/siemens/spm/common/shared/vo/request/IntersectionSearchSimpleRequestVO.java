package com.siemens.spm.common.shared.vo.request;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class IntersectionSearchSimpleRequestVO implements Serializable {

    private static final long serialVersionUID = -6391253150667871626L;

    @JsonProperty("agency_id")
    private Integer agencyId;

    @JsonProperty("exclude_int_ids")
    private String[] excludeIntIds;

    @JsonProperty("text")
    private String text;

    @JsonProperty("status")
    private String status;

    @JsonProperty("order_by_columns")
    private String[] orderByColumns;

    @JsonProperty("should_paginate")
    private Boolean shouldPaginate;

    @JsonProperty("page")
    private Integer page;

    @JsonProperty("size")
    private Integer size;

}
