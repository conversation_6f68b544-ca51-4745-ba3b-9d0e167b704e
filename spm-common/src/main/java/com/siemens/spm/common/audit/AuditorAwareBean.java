/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AuditorAwareBean.java
 * Project     : SPM Platform
 */
package com.siemens.spm.common.audit;

import java.util.Optional;

import org.springframework.data.domain.AuditorAware;

import com.siemens.spm.common.security.SecurityUtils;

public class AuditorAwareBean implements AuditorAware<String> {

    @Override
    public Optional<String> getCurrentAuditor() {
        return Optional.ofNullable(SecurityUtils.getCurrentUserLogin());
    }

}
