/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : BaseResponse.java
 * Project     : SPM Platform
 */

package com.siemens.spm.common.validator;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;

import lombok.NoArgsConstructor;
import org.springframework.beans.TypeMismatchException;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;

import com.google.common.base.CaseFormat;
import com.siemens.spm.common.message.MessageService;
import com.siemens.spm.common.util.BeanFinder;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@SuppressWarnings("java:S1118")
public class BaseBadResponse {

    private static final String BAD_DATA = "bad_data";

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    protected static class Error {
        private String field;
        private String message;
    }

    @Data
    @Builder
    protected static class BadRequestMessage {
        private String key;
        private List<Error> errors;
    }

    protected static List<Error> getErrorList(TypeMismatchException ex) {
        List<Error> data = new ArrayList<>();
        MessageService messageService = BeanFinder.getDefaultMessageService();

        if (ex != null) {
            String message = messageService.getMessage("type_mismatch");
            data.add(new Error(BAD_DATA, message));
        }

        return data;
    }

    protected static List<Error> getErrorList(HttpMessageNotReadableException ex) {
        List<Error> data = new ArrayList<>();
        MessageService messageService = BeanFinder.getDefaultMessageService();
        if (ex != null) {
            String message = messageService.getMessage("json_parse_error");
            data.add(new Error(BAD_DATA, message));
        }

        return data;
    }

    protected static List<Error> getErrorList(MethodArgumentNotValidException ex) {
        List<Error> data = new ArrayList<>();
        MessageService messageService = BeanFinder.getDefaultMessageService();

        if (ex != null) {
            for (ObjectError objectError : ex.getBindingResult().getAllErrors()) {
                if (objectError instanceof FieldError fieldError) {
                    String fieldName = CaseFormat.UPPER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, fieldError.getField());
                    String message = messageService.getMessage(fieldError.getDefaultMessage());
                    data.add(new Error(fieldName, message));
                }
            }
        }

        return data;
    }

    protected static List<Error> getErrorList(ConstraintViolationException ex) {
        List<Error> data = new ArrayList<>();
        MessageService messageService = BeanFinder.getDefaultMessageService();

        if (ex != null) {
            Set<ConstraintViolation<?>> violations = ex.getConstraintViolations();
            for (ConstraintViolation<?> constraint : violations) {
                String message = messageService.getMessage(constraint.getMessage());
                data.add(new Error(BAD_DATA, message));
            }
        }

        return data;
    }
}
