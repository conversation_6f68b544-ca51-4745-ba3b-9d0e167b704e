/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : JWTPair.java
 * Project     : SPM Platform
 */
package com.siemens.spm.common.shared.domaintype;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 *
 */
@Getter
@AllArgsConstructor
public class TokenPair {

    private String accessToken;
    
    private long accessTokenExp;
    
    private String refreshToken;
    
    private long refreshTokenExp;

}
