/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : MessageServiceImpl.java
 * Project     : SPM Platform
 */

package com.siemens.spm.common.message;

import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Service(MessageService.DEFAULT_BEAN)
@AllArgsConstructor
public class MessageServiceBean implements MessageService {

    private static final String MESSAGE_EMPTY = "";

    private static final String NOT_DEFINED_KEY_MESSAGE = "The following key {} is not defined for locale {}!!!";

    @Autowired
    private MessageSource messageSource;

    @Override
    public String getMessage(String messageKey) {
        return getMessage(messageKey, LocaleContextHolder.getLocale());
    }

    @Override
    public String getMessage(String messageKey, Locale locale) {
        return getMessage(messageKey, null, locale);
    }

    @Override
    public String getMessage(String messageKey, Object[] args) {
        return getMessage(messageKey, args, LocaleContextHolder.getLocale());
    }

    @Override
    public String getMessage(String messageKey, Object[] args, Locale locale) {
        if (!StringUtils.hasText(messageKey) || locale == null)
            return MESSAGE_EMPTY;

        String translatedMessage = messageSource.getMessage(messageKey, args, null, locale);

        if (translatedMessage == null) {
            translatedMessage = messageKey;

            // key does not not contain white space -> ignore free texts containing white
            // space
            if (!messageKey.contains(" ")) {
                log.debug(NOT_DEFINED_KEY_MESSAGE, messageKey, locale.getLanguage());
            }
        }

        return translatedMessage;
    }

    @Override
    public Map<String, String> getMessages(String messageKey) {
        Map<String, String> textMap = new HashMap<>();
        for (Locale locale : MessageService.SUPPORTED_LOCALED_MAP.values()) {
            String text = messageSource.getMessage(messageKey, null, null, locale);
            if (text != null) {
                textMap.put(locale.getLanguage(), text);
            } else {
                log.debug(NOT_DEFINED_KEY_MESSAGE, messageKey, locale.getLanguage());
            }
        }

        return textMap.isEmpty() ? null : textMap;
    }

    @Override
    public Map<String, String> getMessages(String messageKey, Object[] args) {
        Map<String, String> textMap = new HashMap<>();
        for (Locale locale : MessageService.SUPPORTED_LOCALED_MAP.values()) {
            String text = messageSource.getMessage(messageKey, args, null, locale);
            if (text != null) {
                textMap.put(locale.getLanguage(), text);
            } else {
                log.debug(NOT_DEFINED_KEY_MESSAGE, messageKey, locale.getLanguage());
            }
        }

        return textMap.isEmpty() ? null : textMap;
    }

}
