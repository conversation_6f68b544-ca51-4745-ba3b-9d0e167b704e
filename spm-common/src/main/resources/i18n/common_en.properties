## common HTTP response message
not_found=Not found
success=Success
no_data=No Data Available
could_not_get_data=Could not get data
unknown_exception=Unknown exception
invalid_input_exception=Invalid input exception
created=Created
no_content=No Content
no_action=No Action
json_parse_error=Error parse json
error_convert_action_json=Could convert action to JSON
type_mismatch=Type Mismatch
sort_column_invalid=Sorting columns are invalid
sort_order_invalid=Sorting order are invalid
agency_id_not_empty=agency_id cannot be empty
agency_id_invalid=agency_id is invalid
invalid_intersection=Intersection id is invalid
invalid_intersection_agency=Agency and Intersection pair is invalid
agency_required=Please specify Agency
intersection_required=Please specify Intersection
from_time_required=Please specify starting time
to_time_required=Please specify ending time
#
## Common validate message
#
time_zone_invalid=Time Zone is invalid.
time_not_null=Time can not be null.
start_time_not_null=Start time can not be null.
end_time_not_null=End time can not be null.
id_not_null=Id can not be null.
intersection_not_null=Intersection can not be null.
owner_id_not_null=Owner id can not be null.
request_body_not_null=Request body can not be null.
time_not_valid_format=Time is not valid format.
#
## Generic email text
#
email.greeting=Dear {0},
email.regards=Best regards,
email.signature=YUTRAFFIC Insights Team
#
## Common texts
#
any=any
all=all
aog=AoG
aog_percent=%AoG
aog_volume=AoG Volume
aor=AoR
aor_percent=%AoR
aor_volume=AoR Volume
aoy=AoY
aoy_percent=%AoY
aoy_volume=AoY Volume
coord_health_trans_percent=%Transition
split_failure_volume=Split Failures
poor_coordination=Poor Coordination
excess_capacity=Excess Capacity
avg_app_delay=Avg. Approach Delay
avg_queue_length=Avg. Queue Length
preemption_priority_requests=Preemption/Priority Requests
red_light_violation_count=Red Light Violation Count
arrivals_on_green=Arrivals on Green
arrivals_on_red=Arrivals on Red
arrivals_on_yellow=Arrivals on Yellow
yellow_trap_occurrences=Yellow Trap Occurrences
abnormal_data=Abnormal Data
split_monitor=Split Monitor
volume=Volume
coordination=Coordination
coordination_health=Coordination Health
split_failure=Split Failure
approach_delay=Approach Delay
queue_length=Queue Length
preemption_priority=Preemption/Priority
turning_movement=Turning Movement Counts
red_light_violation=Red Light Violation
transition_percent=Transition
max_gor_percent=Max. GOR
avg_gor_percent=Avg. GOR
max_ror5_percent=Max. ROR5
avg_ror5_percent=Avg. ROR5
avg_approach_delay=Avg. Approach Delay
detector_actuation=Detector Actuation
detector_actuation_volume=Detector Actuation Volume
detector=detector
force_off=Force Off
force_off_percent=%Force Off
gap_out=Gap Out
gap_out_percent=%Gap Out
max_out=Max Out
max_out_percent=%Max Out
pedestrian_phase_calls=Pedestrian Phase Calls
ped_phase_call=Pedestrian Phase Calls
phase_call=Phase Calls
phase_calls=Phase Calls
phase=phase
phase_terminations=Phase Terminations
pedestrian=Pedestrian Delay
max_ped_delay=Max. Ped Delay
avg_ped_delay=Avg. Ped Delay
ped_delay_activation=Ped Delay Activation
moe_analysis=MOE Analysis
#
## conjunction
#
conjunction.and=and
conjunction.AND=AND
conjunction.or=or
conjunction.OR=OR
## alarm
alarm=Alarm
#
## alarm rule event
#
alarm_rule.noti.name.rule_created=New alarm rule
alarm_rule.noti.name.rule_activated=Alarm rule activated
alarm_rule.noti.name.rule_deactivated=Alarm rule deactivated
alarm_rule.noti.name.rule_updated=Alarm rule updated
alarm_rule.noti.name.rule_subscribed_no_email=Alarm rule subscribed no email
alarm_rule.noti.name.rule_subscribed_with_email=Alarm rule subscribed receive email
alarm_rule.noti.name.rule_unsubscribed=Alarm rule unsubscribed
alarm_rule.noti.name.rule_deleted=Alarm rule deleted
#
## alarm rule event description
#
alarm_rule.noti.desc.rule_created=A new alarm rule has just been created and you were added to subscriber list of it
alarm_rule.noti.desc.rule_activated=An alarm rule which you subscribed has just been activated
alarm_rule.noti.desc.rule_deactivated=An alarm rule which you subscribed has just been deactivated
alarm_rule.noti.desc.rule_updated=An alarm rule which you subscribed has just been updated
alarm_rule.noti.desc.rule_subscribed_no_email=You have just been added to subscriber list of an alarm rule and not receive email
alarm_rule.noti.desc.rule_subscribed_with_email=You have just been added to subscriber list of an alarm rule and receive email
alarm_rule.noti.desc.rule_unsubscribed=You have just been removed from subscriber list of an alarm rule
alarm_rule.noti.desc.rule_deleted=An alarm rule which you subscribed has just been deleted
#
## alarm category
#
alarm.category.unknown=Unknown
alarm.category.safety=Safety
alarm.category.pedestrian=Pedestrian
alarm.category.oversaturation=Oversaturation
alarm.category.coordination=Coordination
alarm.category.maintenance=Maintenance
#
## Category labels
#
cat_det_act=Detector Actuation
cat_phase_call=Phase Calls
cat_ped_phase_call=Pedestrian Phase Calls
cat_gap_out=Gap Out
cat_max_out=Max Out
cat_force_off=Force Off
cat_aog=Arrivals on Green
cat_aoy=Arrivals on Yellow
cat_aor=Arrivals on Red
cat_max_ped_delay=Max. Ped Delay
cat_avg_ped_delay=Avg. Ped Delay
cat_transition_percent=Transition
cat_split_failure=Split Failure
cat_poor_coordination=Poor Coordination
cat_excess_capacity=Excess Capacity
cat_max_gor_percent=Max. GOR
cat_avg_gor_percent=Avg. GOR
cat_max_ror5_percent=Max. ROR5
cat_avg_ror5_percent=Avg. ROR5
cat_avg_approach_delay=Avg. Approach Delay
cat_avg_queue_length=Avg. Queue Length
cat_no_data=No Data
#
## Traffic metric
#
traffic_metric.arrivals_on_red=Arrivals on red
traffic_metric.percent_arrivals_on_red=Percent arrivals on red
traffic_metric.total_vehicle=Total vehicle
traffic_metric.number_of_gap_out=Number of gap out
traffic_metric.number_of_max_out=Number of max out
traffic_metric.number_of_force_off=Number of force off
traffic_metric.number_of_unknown_termination_cause=Number of unknown termination cause
traffic_metric.number_of_ped_activity=Number of ped activity
traffic_metric.arrivals_on_green=Arrivals on green
traffic_metric.percent_arrivals_on_green=Percent arrivals on green
traffic_metric.arrivals_on_yellow=Arrivals on yellow
traffic_metric.percent_arrivals_on_yellow=Percent arrivals on yellow
traffic_metric.green_time=Green time
traffic_metric.platoon_ratio=Platoon ratio
#
## Metrics labels
#
open_alarm_notifications=Open Alarms
vehicle_volume=Vehicle Volume
#
## Metric unit labels
#
unit.raw_count=Raw Count
unit.hour=Hour
unit.vph=VPH
unit.percent=%
unit.second=Second
unit.second.dashboard=s
unit.vehicle=Vehicle
#
## Notification type
#
notification.type.other=Other
notification.type.alarm_notification=Alarm notification
notification.type.alarm_management=Alarm management
notification.type.summary_report_notification=Report notification
notification.type.summary_report_management=Report management
notification.type.release_note_notification=Release note notification
notification.type.optimization=Optimization
notification.type.performance_metric_notification=Performance metric notification
notification.type.performance_metric_management=Performance metric management
notification.type.user_agency_deletion=User / Agency Deletion
notification.type.detector_report_notification=Detector Report Notification
notification.type.detector_report_management=Detector Report Management
release_notes_title=Release notes
release_notes_desc=New version {0} is available on {1}
#
## Export
#
export_completed=Export completed
export_failed=Export failed
download_btn_title=Download
view_btn_title=View
export_failed_please_try_again=Export failed for intersection {0}. Please try again later.\nUUID: {1}; from {2}; to {3}.
export_failed_message=There is an error during export perflog. Please try again later
export_completed_click_here_to_download=Export completed for intersection {0}. Click here to download.\nUUID: {1}; from {2}; to {3}.
export_progress_title=Export raw data: {0}
export_progress_description=Intersection UUID: {0}; from: {1} to {2}
summary_report_title=Summary report
summary_report_description=Report name: {0}
summary_report_failed_message=There is an error during summary report. Please try again later
#Report
summary-report=Summary Report
performance-metric=Performance Metric
detector-report=Detector Report
detector_report_description=Report name: {0}
detector_report_title=Detector Report
#
## Summary report template
#
summary-report.templates.noti.created=New summary report template
summary-report.templates.noti.desc.created=A new summary report template have just been created
summary-report.templates.noti.updated=Summary report template updated
summary-report.templates.noti.desc.updated=A summary report template have just been updated
summary-report.templates.noti.activated=Summary report template activated
summary-report.templates.noti.desc.activated=A summary report template have just been activated
summary-report.templates.noti.deactivated=Summary report template deactivated
summary-report.templates.noti.desc.deactivated=A summary report template have just been deactivated
summary-report.templates.noti.deleted=Summary report template deleted
summary-report.templates.noti.desc.deleted=A summary report template have just been deleted
# Summary report result
summary-report.results.noti.created=YUTRAFFIC Insights: New summary report result
summary-report.results.noti.desc.created=A new summary report result have just been created

# Performance metric template
performance_metric_title=Performance metric
performance_metric_description=Analysis type: {0}
performance_metric_failed_message=There is an error during run performance metric template. Please try again later
performance-metric.templates.noti.created=New performance metric template
performance-metric.templates.noti.desc.created=A new performance metric template have just been created
performance-metric.templates.noti.updated=Performance metric template updated
performance-metric.templates.noti.desc.updated=A performance metric template have just been updated
performance-metric.templates.noti.activated=Performance metric template activated
performance-metric.templates.noti.desc.activated=A performance metric template have just been activated
performance-metric.templates.noti.deactivated=Performance metric template deactivated
performance-metric.templates.noti.desc.deactivated=A performance metric template have just been deactivated
performance-metric.templates.noti.deleted=Performance metric template deleted
performance-metric.templates.noti.desc.deleted=A performance metric template have just been deleted
# Performance metric result
performance-metric.results.noti.created=YUTRAFFIC Insights: New performance metric result
performance-metric.results.noti.desc.created={0} new performance metric results have just been created

# Detector report template
detector-report.templates.noti.created=New detector report template
detector-report.templates.noti.desc.created=A new detector report template have just been created
detector-report.templates.noti.updated=Detector report template updated
detector-report.templates.noti.desc.updated=A detector report template have just been updated
detector-report.templates.noti.activated=Detector report template activated
detector-report.templates.noti.desc.activated=A detector report template have just been activated
detector-report.templates.noti.deactivated=Detector report template deactivated
detector-report.templates.noti.desc.deactivated=A detector report template have just been deactivated
detector-report.templates.noti.deleted=Detector report template deleted
detector-report.templates.noti.desc.deleted=A detector report template have just been deleted
# Detector report result
detector-report.results.noti.created=YUTRAFFIC Insights: New detector report result
detector-report.results.noti.desc.created=A new detector report result have just been created
detector_report_failed_message=There is an error during detector report. Please try again later
## Optimization
optimization.validation.invalid_type=Invalid type
optimization.validation.id_list_not_empty=Id list can not be empty
#
## Intersection topology - Direction
#
eastbound=Eastbound
westbound=Westbound
northbound=Northbound
southbound=Southbound
other=Other
#
## Intersection topology - Movement
#
move_right=Right
move_left=Left
move_through=Through
move_through_right=Through/Right
move_through_left=Through/Left
#
## Intersection topology - detector type
#
detector_type_advance=Advance
detector_type_stopbar=Stop bar
detector_type_red_light_violation=Red light violation
detector_type_pedestrian=Pedestrian
#
## Agency licenses
#
license.alarm.name=Alarm
license.tsp_dashboard.name=TSP Dashboard
license.performance_metrics.name=Performance Metrics
license.optimizer.name=Optimizer
license.report.name=Report
license.corridor.name=Corridor
license.intersection.name=Intersection

license.alarm.desc=Alarms provide a way to evaluate one or more intersections for one or more conditions and over a set period. The system evaluates intersection data when scanning alarm criteria. The subscriber(s) of alarm will receive  alerts in the platform and email channel as well.
license.tsp_dashboard.desc=TSP Dashboard is used analyze and monitor performance of TSP from the individual intersection's perspective. Components of the TSP Dashboard include a map view of TSP performance, TSP Summary Table, TSP Event History and Intersection Event History.
license.performance_metrics.desc=The feature provides the visualizations of metrics for an intersection. The traffic engineer can investigate or detect the issues from the traffic data with different views/ aspects: Arrival on Red, Arrival on Green, Phase Termination, Coordination, Coordination Health, Split Failure, Pedestrian, Approach Delay, Queue Length ...
license.optimizer.desc=The feature allows the user to execute the traffic optimizations. Currently, there are 2 types: 'Offset Optimization', 'Traffic Profile'.\n - Offset Optimization: In current version, offset optimization supports only single intersection. The purpose of an offset optimization result is finding a new offset that will help get the best AoG for selected phase.\n - Traffic Profile: Application of a clustering algorithm to the historical Perflog data to determine the settled traffic profiles and the corresponding time assignment.
license.report.desc=This module provides a report that will allow users to quickly see the states and trends happening on their intersections. These reports will be trigger automatically based on a template that the user could be defined by himself/herself. There are 2 types of reports: Summary Report and Performance Metrics Report.\n - Summary Report: This provides almost the traffic indicators in the platform. That includes average, high, and low values for each metric. These values are summarized by an aggregation unit during a defined period for only coordinated phases.\n - This feature allows the user to be able to set a Performance Metric Analysis to be run on a schedule. The report result looks the same as the result shown in the performance metric page.
license.corridor.desc=A corridor is defined in Insights by two or more intersections in an agency. This definition is a prerequisite for creating a historical time-space analysis in the Optimizer module.
license.intersection.desc=For managing the intersection in an agency. Including the display of the configuration versions of TACTIC, the download of the raw Perflog data and the possibility for the user to define the intersection topology.
