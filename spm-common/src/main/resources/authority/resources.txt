*
account
account.agencies
account.change-password
account.default-agency
account.language
account.reset-password
account.user-key
account.user-new-key
agencies.{agency_id}
agencies.{agency_id}.assign-user
agencies.{agency_id}.dh-api-key
agencies.{agency_id}.licenses
agencies.{agency_id}.remove-users
agencies.{agency_id}.settings
agency.detail
alarm.analyses
alarm.categories
alarm.messages
alarm.notifications
alarm.records
alarm.records.*
alarm.rules
alarm.rules.{rule_id}
alarm.rules.{rule_id}.intersections
alarm.rules.{rule_id}.subscribers
alarm.rules.{rule_id}.trigger
alarm.rules.*
alarm.rules.active
alarm.rules.inactive
alarm.rules.intersections.available
alarm.rules.subscribers.available
alarm.rules.subscription
alarm.rules.subscriptions
alarm.traffic-metrics.all
analysis.*
corridors
corridors.{corridor_id}
corridors.*
corridors.active
dashboard
intersection-configs
intersection-configs.latest
intersection-configs.topologies
intersection-configs.topologies.*
intersections
intersections.{intersection_id}
intersections.*
intersections.all
intersections.nearbysearch
notifications
notifications.*
optimization
optimization.{optimization_id}
optimization.*
optimization.histories
optimization.histories.{history_id}
optimization.histories.*
perflog.*
performance-metric.results
performance-metric.results.*
performance-metric.templates
performance-metric.templates.{template_id}
performance-metric.templates.*
roles.all
summary-report.results
summary-report.results.*
summary-report.templates
summary-report.templates.{template_id}
summary-report.templates.*
task-progresses
task-progresses.*
tsp-dashboard
tsp.*
users
users?agency_id
users.{user_id}
users.*
users.all
users.current-agency
custom-analysis
custom-analysis.{custom_analysis_id}
