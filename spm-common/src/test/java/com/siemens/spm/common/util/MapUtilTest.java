package com.siemens.spm.common.util;

import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class MapUtilTest {

    @Test
    void test_transform() {
        Map<String, String> emptyMap = Map.of();
        Assertions.assertThrows(IllegalArgumentException.class, () -> MapUtil.transform(null, null));
        Assertions.assertThrows(IllegalArgumentException.class, () -> MapUtil.transform(null, o -> o));
        Assertions.assertThrows(IllegalArgumentException.class, () -> MapUtil.transform(emptyMap, null));
        Map<Integer, String> integerStringMap = new HashMap<>();
        integerStringMap.put(1, "value1");
        Map<Integer, String> actual = MapUtil.transform(integerStringMap, s -> s + "test");
        Assertions.assertEquals("value1test", actual.get(1));

        Assertions.assertThrows(IllegalArgumentException.class, () -> MapUtil.transform(null, null, null));
        Assertions.assertThrows(IllegalArgumentException.class, () -> MapUtil.transform(null, o -> o, null));
        Assertions.assertThrows(IllegalArgumentException.class, () -> MapUtil.transform(null, o -> o, o -> o));
        Assertions.assertThrows(IllegalArgumentException.class, () -> MapUtil.transform(null, null, o -> o));

        Assertions.assertThrows(IllegalArgumentException.class, () -> MapUtil.transform(emptyMap, null, o -> o));
        Assertions.assertThrows(IllegalArgumentException.class, () -> MapUtil.transform(emptyMap, o -> o, null));
        Assertions.assertThrows(IllegalArgumentException.class, () -> MapUtil.transform(emptyMap, null, null));

        Map<Integer, String> integerStringMap1 = new HashMap<>();
        integerStringMap1.put(1, "value1");
        Map<String, String> actual1 = MapUtil.transform(integerStringMap1, i -> "No" + i, s -> s + "test");

        Assertions.assertThrows(IllegalArgumentException.class, () -> MapUtil.transform(null, null, o -> o));
        Assertions.assertThrows(IllegalArgumentException.class, () -> MapUtil.transform(emptyMap, null, null));
        Assertions.assertEquals("value1test", actual1.get("No1"));

    }

}
