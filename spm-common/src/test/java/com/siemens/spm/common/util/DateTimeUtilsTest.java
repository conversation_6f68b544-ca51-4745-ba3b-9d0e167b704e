/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : DateTimeUtilsTest.java
 * Project     : SPM Platform
 */
package com.siemens.spm.common.util;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.TimeZone;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

class DateTimeUtilsTest {

    /**
     *
     */
    @BeforeEach
    void setUp() {
    }

    /**
     *
     */
    @AfterEach
    void tearDown() {
    }

    /**
     * Test method for
     * {@link DateTimeUtils#sqlTimestampFromLocalDateTime(LocalDateTime)}.
     */
    @Test
    void testSqlTimestampFromLocalDateTime() {
        LocalDateTime expDatetime = LocalDateTime.of(2020, 4, 27, 11, 12, 30, 300000000);
        Timestamp expTimestamp = new Timestamp(1587985950300L);

        Timestamp timestamp = DateTimeUtils.sqlTimestampFromLocalDateTime(expDatetime);

        assertEquals(expTimestamp, timestamp);
    }

    /**
     * Test method for
     * {@link DateTimeUtils#sqlTimestampFromLocalDateTime(LocalDateTime)}.
     */
    @Test
    void testSqlTimestampFromLocalDateTimeWithZoneOffset() {
        LocalDateTime expDatetime = LocalDateTime.of(2020, 4, 27, 11, 12, 30, 300000000);
        Timestamp expTimestamp = new Timestamp(1588003950300L);

        Timestamp timestamp = DateTimeUtils.sqlTimestampFromLocalDateTime(expDatetime, ZoneOffset.ofHours(-5));

        assertEquals(expTimestamp, timestamp);
    }

    /**
     * Test method for
     * {@link DateTimeUtils#sqlTimestampFromLocalDateTime(LocalDateTime)}.
     */
    @Test
    void testSqlTimestampFromLocalDateTimeWithTimeZone() {
        LocalDateTime expDatetime = LocalDateTime.of(2020, 4, 27, 11, 12, 30, 300000000);
        Timestamp expTimestamp = new Timestamp(1588003950300L);

        Timestamp timestamp = DateTimeUtils.sqlTimestampFromLocalDateTime(expDatetime,
                TimeZone.getTimeZone("America/Chicago"));

        assertEquals(expTimestamp, timestamp);
    }

    /**
     * Test method for
     * {@link DateTimeUtils#localDateTimeFromSqlTimestamp(Timestamp)}.
     */
    @Test
    void testLocalDateTimeFromSqlTimestamp() {
        LocalDateTime expDatetime = LocalDateTime.of(2020, 4, 27, 11, 12, 30, 300000000);
        Timestamp expTimestamp = new Timestamp(1587985950300L);

        LocalDateTime datetime = DateTimeUtils.localDateTimeFromSqlTimestamp(expTimestamp);

        assertEquals(expDatetime, datetime);
    }

    /**
     * Test method for
     * {@link DateTimeUtils#localDateTimeFromSqlTimestamp(Timestamp)}.
     */
    @Test
    void testLocalDateTimeFromSqlTimestampWithZoneOffset() {
        LocalDateTime expDatetime = LocalDateTime.of(2020, 4, 27, 11, 12, 30, 300000000);
        Timestamp expTimestamp = new Timestamp(1588003950300L);

        LocalDateTime datetime = DateTimeUtils.localDateTimeFromSqlTimestamp(expTimestamp, ZoneOffset.ofHours(-5));

        assertEquals(expDatetime, datetime);
    }

    @Test
    void testConvertTimestampFromLocalDateTime() {
        LocalDateTime sourceLocalDateTime = LocalDateTime.of(2021, 2, 19, 0, 0);
        ZoneId zoneId = ZoneId.of("GMT+7");

        Timestamp targetDateTime = DateTimeUtils.timestampFromLocalDateTime(sourceLocalDateTime, zoneId);
        LocalDateTime expectedDateTime = LocalDateTime.of(2021, 2, 18, 17, 0);
        assertEquals(Timestamp.valueOf(expectedDateTime), targetDateTime);
    }

    @Test
    void testConvertLocalDateFromTimestamp() {
        Timestamp timestamp = new Timestamp(1613937328000L);
        ZoneId zoneId = ZoneId.of("GMT+7");

        LocalDate actualDate = DateTimeUtils.localDateFromTimestamp(timestamp, zoneId);
        LocalDate expectedDate = LocalDate.of(2021, 2, 22);
        assertEquals(expectedDate, actualDate);
    }

}
