# ----------------------------------------------
# Include the scanning pipeline configuration
# ----------------------------------------------
# This brings in the predefined scanning pipeline from the external project `mocca/scvm/scvm-scanning-pipeline`.
# It includes all necessary settings for vulnerability scanning and SBOM processing.
include:
  - project: mocca/scvm/scvm-scanning-pipeline
    file: .gitlab-ci-template.yml
    ref: release

image: registry.mocca.yunextraffic.cloud/mocca/mocca-cli:3-4-1

variables:
  MAVEN_REPO_USER_NAME: $ARTIFACTORY_USER
  MAVEN_REPO_PASSWORD: $ARTIFACTORY_PASSWD
  MAVEN_REPO_URL: https://artifactory.mocca.yunextraffic.cloud/artifactory/its-dev/

cache:
  paths:
    - .m2/repository/

default:
  tags:
    - medium

stages:
  - build-and-push
  #  - lint
  - security-code

spm-common:build-and-push:maven:
  stage: build-and-push
  image: ${BUILD_IMAGE}/jdk17-mvn:1.23.0
  variables:
    DOCKER_DRIVER: overlay
    DOCKER_HOST: $DOCKER_DIND_HOST
  services:
    - $DOCKER_DIND_SERVICE
  script:
    - cd spm-common
    - cp ../settings.xml .
    - mocca maven deploy -s ./settings.xml
  artifacts:
    paths:
      - spm-common/target/
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
    - if: '$CI_COMMIT_BRANCH == "hotfix/reconciliation-agency-provision"'

    #spm-common:lint:sonar:
    #  stage: lint
    #  script:
    #    - cd spm-common
    #    - cp ../settings.xml .
    #    - mocca maven sonar
    #  needs: [ "spm-common:build-and-push:maven" ]
    #  rules:
    #    - if: '$CI_COMMIT_BRANCH == "master"'
    #- if: '$CI_COMMIT_BRANCH == "fix/si-224-negative-ror5"'

spm-common:security:dependency-check:
  stage: security-code
  script:
    - cd spm-common
    - mocca security dependency-check
  artifacts:
    expire_in: 30 days
    paths:
      - spm-common/dependency-check/
  needs: [ "spm-common:build-and-push:maven" ]
  retry: 2
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
    - if: '$CI_COMMIT_BRANCH == "hotfix/reconciliation-agency-provision"'

spm-datahub-sdk:
  needs: [ "spm-common:build-and-push:maven" ]
  stage: build-and-push

  only:
    - master
    - hotfix/reconciliation-agency-provision
  
  image: ${BUILD_IMAGE}/jdk17-mvn:1.23.0
  variables:
    DOCKER_DRIVER: overlay
    DOCKER_HOST: $DOCKER_DIND_HOST
  services:
    - $DOCKER_DIND_SERVICE
  script:
    - cd spm-datahub-sdk
    - cp ../settings.xml .
    - mocca maven deploy -s ./settings.xml


spm-perflog-lib:
  needs: [ "spm-datahub-sdk" ]
  stage: build-and-push

  only:
    - master
    - hotfix/reconciliation-agency-provision

  image: ${BUILD_IMAGE}/jdk17-mvn:1.23.0
  variables:
    DOCKER_DRIVER: overlay
    DOCKER_HOST: $DOCKER_DIND_HOST
  services:
    - $DOCKER_DIND_SERVICE
  script:
    - cd spm-perflog-lib
    - cp ../settings.xml .
    - mocca maven deploy -s ./settings.xml


spm-analysis-lib:
  stage: build-and-push
  needs: [ "spm-perflog-lib" ]

  only:
    - master
    - hotfix/reconciliation-agency-provision

  image: ${BUILD_IMAGE}/jdk17-mvn:1.23.0
  variables:
    DOCKER_DRIVER: overlay
    DOCKER_HOST: $DOCKER_DIND_HOST
  services:
    - $DOCKER_DIND_SERVICE
  script:
    - cd spm-analysis-lib
    - cp ../settings.xml .
    - mocca maven deploy -s ./settings.xml


spm-reporting:
  needs: [ "spm-common:build-and-push:maven" ]
  stage: build-and-push

  only:
    refs:
      - master
      - hotfix/reconciliation-agency-provision
  image: ${BUILD_IMAGE}/jdk17-mvn:1.23.0
  variables:
    DOCKER_DRIVER: overlay
    DOCKER_HOST: $DOCKER_DIND_HOST
  services:
    - $DOCKER_DIND_SERVICE
  script:
    - cd spm-reporting
    - cp ../settings.xml .
    - mocca maven deploy -s ./settings.xml


spm-notification-lib:
  needs: [ "spm-common:build-and-push:maven" ]
  stage: build-and-push

  only:
    refs:
      - master
      - hotfix/reconciliation-agency-provision
  image: ${BUILD_IMAGE}/jdk17-mvn:1.23.0
  variables:
    DOCKER_DRIVER: overlay
    DOCKER_HOST: $DOCKER_DIND_HOST
  services:
    - $DOCKER_DIND_SERVICE
  script:
    - cd spm-notification-lib
    - cp ../settings.xml .
    - mocca maven deploy -s ./settings.xml


spm-rule-service-contract:
  needs: [ "spm-common:build-and-push:maven", "spm-reporting" ]
  stage: build-and-push

  only:
    refs:
      - master
      - hotfix/reconciliation-agency-provision
  image: ${BUILD_IMAGE}/jdk17-mvn:1.23.0
  variables:
    DOCKER_DRIVER: overlay
    DOCKER_HOST: $DOCKER_DIND_HOST
  services:
    - $DOCKER_DIND_SERVICE
  script:
    - cd spm-rule-service-contract
    - cp ../settings.xml .
    - mocca maven deploy -s ./settings.xml


spm-rule-evaluation-service-contract:
  needs: [ "spm-common:build-and-push:maven" ]
  stage: build-and-push

  only:
    refs:
      - master
      - hotfix/reconciliation-agency-provision
  image: ${BUILD_IMAGE}/jdk17-mvn:1.23.0
  variables:
    DOCKER_DRIVER: overlay
    DOCKER_HOST: $DOCKER_DIND_HOST
  services:
    - $DOCKER_DIND_SERVICE
  script:
    - cd spm-rule-evaluation-service-contract
    - cp ../settings.xml .
    - mocca maven deploy -s ./settings.xml


spm-user-mgmt-service-contract:
  needs: [ "spm-common:build-and-push:maven", "spm-datahub-sdk", "spm-analysis-lib" ]
  stage: build-and-push

  only:
    refs:
      - master
      - hotfix/reconciliation-agency-provision
  image: ${BUILD_IMAGE}/jdk17-mvn:1.23.0
  variables:
    DOCKER_DRIVER: overlay
    DOCKER_HOST: $DOCKER_DIND_HOST
  services:
    - $DOCKER_DIND_SERVICE
  script:
    - cd spm-user-mgmt-service-contract
    - cp ../settings.xml .
    - mocca maven deploy -s ./settings.xml

spm-analysis-service-contract:
  needs: [ "spm-common:build-and-push:maven", "spm-analysis-lib" ]
  stage: build-and-push

  only:
    - master
    - hotfix/reconciliation-agency-provision
  image: ${BUILD_IMAGE}/jdk17-mvn:1.23.0
  variables:
    DOCKER_DRIVER: overlay
    DOCKER_HOST: $DOCKER_DIND_HOST
  services:
    - $DOCKER_DIND_SERVICE
  script:
    - cd spm-analysis-service-contract
    - cp ../settings.xml .
    - mocca maven deploy -s ./settings.xml

spm-perflog-crawler-contract:
  needs: [ "spm-common:build-and-push:maven", "spm-perflog-lib" ]
  stage: build-and-push

  only:
    refs:
      - master
      - hotfix/reconciliation-agency-provision
  image: ${BUILD_IMAGE}/jdk17-mvn:1.23.0
  variables:
    DOCKER_DRIVER: overlay
    DOCKER_HOST: $DOCKER_DIND_HOST
  services:
    - $DOCKER_DIND_SERVICE
  script:
    - cd spm-perflog-crawler-contract
    - cp ../settings.xml .
    - mocca maven deploy -s ./settings.xml

spm-studio-sdk:
  stage: build-and-push

  only:
    refs:
      - master
      - hotfix/reconciliation-agency-provision
  image: ${BUILD_IMAGE}/jdk17-mvn:1.23.0
  variables:
    DOCKER_DRIVER: overlay
    DOCKER_HOST: $DOCKER_DIND_HOST
  services:
    - $DOCKER_DIND_SERVICE
  script:
    - cd spm-studio-sdk
    - cp ../settings.xml .
    - mocca maven deploy -s ./settings.xml

spm-agency-supports:
  needs: [ "spm-common:build-and-push:maven" ]
  stage: build-and-push

  only:
    refs:
      - master
      - hotfix/reconciliation-agency-provision
  image: ${BUILD_IMAGE}/jdk17-mvn:1.23.0
  variables:
    DOCKER_DRIVER: overlay
    DOCKER_HOST: $DOCKER_DIND_HOST
  services:
    - $DOCKER_DIND_SERVICE
  script:
    - cd spm-agency-supports
    - cp ../settings.xml .
    - mocca maven deploy -s ./settings.xml

###############################################
# Scanning Trigger Job                        #
###############################################
# This job will trigger the vulnerability scanning process based on the external template.
# It will use the `scvm:scan-trigger` definition from the included scanning pipeline.
# The environment for the scan can be set to `staging` or `production` as needed.
# By default, it uses the `main` branch which is staging
scvm:scan-trigger:
  extends: .scvm:scan-trigger
  variables:
    CONFIG_PROFILE: siwave
  rules:
    - if: "$CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH"
    - if: "$CI_COMMIT_TAG"