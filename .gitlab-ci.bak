variables:
  MAVEN_CLI_OPTS: "-Dmaven.test.skip"
  MAVEN_OPTS: "-Dmaven.repo.local=../.m2/repository"
  APP_NAME: "spm-common"

cache:
  paths:
    - .m2/repository/

stages:
  - setup
  - build-and-push
  - clear
  - alert

setup-env:
  stage: setup
  tags:
    - prod
  only:
    refs:
      - master
      - staging
      - prod
  script: |
    mkdir -p ~/.m2
    cp $SETTING_M2 ~/.m2/settings.xml
    sed -i "s/USER_JFROG/${USER_JFROG}/g" ~/.m2/settings.xml
    sed -i "s/PASS_JFROG/${PASS_JFROG}/g" ~/.m2/settings.xml

spm-clib:
  needs: ["setup-env"]
  stage: build-and-push
  tags:
    - prod
  only:
    refs:
      - master
      - staging
      - prod
  script: |
    cd spm-clib
    mvn $MAVEN_CLI_OPTS $MAVEN_OPTS clean deploy 
    cd ..  


spm-common:
  needs: ["spm-clib"]
  stage: build-and-push
  tags:
    - prod
  only:
    refs:
      - master
      - staging
      - prod
  script: |
    cd spm-common
    mvn clean deploy -Dmaven.test.skip
    cd ..  


spm-datahub-service-contract:
  needs: ["spm-common"]
  stage: build-and-push
  tags:
    - prod
  only:
    refs:
      - master
      - staging
      - prod
  script: |
    cd spm-datahub-service-contract
    mvn $MAVEN_CLI_OPTS $MAVEN_OPTS clean deploy 
    cd ..  


spm-datahub-sdk:
  needs: ["spm-datahub-service-contract"]
  stage: build-and-push
  tags:
    - prod
  only:
    refs:
      - master
      - staging
      - prod
  script: |
    cd spm-datahub-sdk
    mvn $MAVEN_CLI_OPTS $MAVEN_OPTS clean deploy 
    cd ..  


spm-perflog-lib:
  needs: ["spm-datahub-sdk"]
  stage: build-and-push
  tags:
    - prod
  only:
    refs:
      - master
      - staging
      - prod
  script: |
    cd spm-perflog-lib
    mvn $MAVEN_CLI_OPTS $MAVEN_OPTS clean deploy 
    cd ..  


spm-analysis-lib:
  stage: build-and-push
  needs: ["spm-perflog-lib"]
  tags:
    - prod
  only:
    refs:
      - master
      - staging
      - prod
  script: |
    cd spm-analysis-lib
    mvn $MAVEN_CLI_OPTS $MAVEN_OPTS clean deploy 
    cd ..  


spm-reporting:
  needs: ["spm-analysis-lib"]
  stage: build-and-push
  tags:
    - prod
  only:
    refs:
      - master
      - staging
      - prod
  script: |
    cd spm-reporting
    mvn $MAVEN_CLI_OPTS $MAVEN_OPTS clean deploy 
    cd ..  


spm-notification-lib:
  needs: ["spm-reporting"]
  stage: build-and-push
  tags:
    - prod
  only:
    refs:
      - master
      - staging
      - prod
  script: |
    cd spm-notification-lib
    mvn $MAVEN_CLI_OPTS $MAVEN_OPTS clean deploy 
    cd ..  


spm-rule-service-contract:
  needs: ["spm-notification-lib"]
  stage: build-and-push
  tags:
    - prod
  only:
    refs:
      - master
      - staging
      - prod
  script: |
    cd spm-rule-service-contract
    mvn $MAVEN_CLI_OPTS $MAVEN_OPTS clean deploy 
    cd ..  


spm-rule-evaluation-service-contract:
  needs: ["spm-rule-service-contract"]
  stage: build-and-push
  tags:
    - prod
  only:
    refs:
      - master
      - staging
      - prod
  script: |
    cd spm-rule-evaluation-service-contract
    mvn $MAVEN_CLI_OPTS $MAVEN_OPTS clean deploy 
    cd ..  


spm-user-mgmt-service-contract:
  needs: ["spm-rule-evaluation-service-contract"]
  stage: build-and-push
  tags:
    - prod
  only:
    refs:
      - master
      - staging
      - prod
  script: |
    cd spm-user-mgmt-service-contract
    mvn $MAVEN_CLI_OPTS $MAVEN_OPTS clean deploy 
    cd ..  

spm-analysis-service-contract:
  needs: ["spm-user-mgmt-service-contract"]
  stage: build-and-push
  tags:
    - prod
  only:
    refs:
      - master
      - staging
      - prod
  script: |
    cd spm-analysis-service-contract
    mvn $MAVEN_CLI_OPTS $MAVEN_OPTS clean deploy 
    cd ..  

spm-perflog-crawler-contract :
  needs: ["spm-analysis-service-contract"]
  stage: build-and-push
  tags:
    - prod
  only:
    refs:
      - master
      - staging
      - prod
  script: |
    cd spm-perflog-crawler-contract 
    mvn $MAVEN_CLI_OPTS $MAVEN_OPTS clean deploy 
    cd ..  


clear-resource:
  stage: clear
  tags:
    - prod
  only:
    refs:
      - master
      - staging
      - prod
  script:
    - rm ~/.m2/settings.xml

success:
  stage: alert
  tags:
    - prod
  script:
    - |
      curl -X POST -H 'Content-Type: application/json' -d '{"text": "'" \ud83d\udc4d \ud83d\udc4d \ud83d\udc4d <br>**Job name**: $CI_PROJECT_NAME/$CI_COMMIT_REF_NAME<br>**Pipeline id**: $CI_PIPELINE_IID <br>**Status**: SUCCESS <br>**Commit**: $CI_COMMIT_SHA <br>**CI_PIPELINE_URL**: $CI_PIPELINE_URL "'"}' $MSTEAM_URL
  when: on_success


failure:
  stage: alert
  tags:
    - prod
  script:
    - |
      curl -X POST -H 'Content-Type: application/json' -d '{"text": "'" \ud83d\ude21 \ud83d\ude21 \ud83d\ude21 <br>**Job name**: $CI_PROJECT_NAME/$CI_COMMIT_REF_NAME<br>**Pipeline id**: $CI_PIPELINE_IID <br>**Status**: FAILURE <br>**Commit**: $CI_COMMIT_SHA <br>**CI_PIPELINE_URL**: $CI_PIPELINE_URL "'"}' $MSTEAM_URL
  when: on_failure
