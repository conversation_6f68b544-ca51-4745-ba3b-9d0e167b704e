package com.siemens.spm.spmstudiosdk.dto;

import lombok.Data;

@Data
public class StudioUserDto {
    
    private Integer id;

    private String firstName;

    private String lastName;

    private String name;

    private String email;

    private Boolean isEnabled;

    private Integer managingAgencyId;

    private String phoneNumber;

    // Ignore other fields for now

}

/* 
"dateCreated": "2025-02-27T04:24:21.246822+00:00",
"id": 12546,
"name": "<EMAIL>",
"email": "<EMAIL>",
"subjectId": "7ce8b08a-0f4f-4299-b115-d4ac4730d3ac",
"isIdpUser": false,
"agencyRoles": [
  {
    "id": 4,
    "agencyId": 1738,
    "name": "Agency Admin",
    "roleType": "AgencyAdmin"
  },
  {
    "id": 5,
    "agencyId": 1738,
    "name": "Agency Viewer",
    "roleType": "AgencyViewer"
  },
  {
    "id": 6,
    "agencyId": 1738,
    "name": "Agency Operator",
    "roleType": "AgencyOperator"
  },
  {
    "id": 7,
    "agencyId": 1738,
    "name": "Insights Agency Admin",
    "roleType": "InsightsAgencyAdmin"
  },
  {
    "id": 8,
    "agencyId": 1738,
    "name": "Insights Agency Operator",
    "roleType": "InsightsAgencyOperator"
  }
],
"lastLoginTime": null,
"isOptedOutFromSmsNotifications": false,
"managingAgencyId": 1738,
"firstName": "Toan",
"lastName": "Pham",
"isEnabled": true,
"phoneNumber": null
}*/