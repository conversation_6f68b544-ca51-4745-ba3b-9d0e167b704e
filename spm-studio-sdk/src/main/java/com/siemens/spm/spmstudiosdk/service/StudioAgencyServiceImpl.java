package com.siemens.spm.spmstudiosdk.service;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.util.Pair;
import org.springframework.http.HttpStatusCode;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import com.siemens.spm.spmstudiosdk.config.BeanProvider;
import com.siemens.spm.spmstudiosdk.dto.StudioAgencyDto;
import com.siemens.spm.spmstudiosdk.dto.StudioAgencySearchRequestDto;
import com.siemens.spm.spmstudiosdk.exception.StudioException;
import com.siemens.spm.spmstudiosdk.helper.StudioAgencyFilterHelper;
import com.siemens.spm.spmstudiosdk.util.Paginator;

@Service
public class StudioAgencyServiceImpl implements StudioAgencyService {

    private final RestTemplate restTemplate;

    @Value("${studio.agency-service.endpoint}")
    private String agencyServiceEndpoint;

    @Autowired
    public StudioAgencyServiceImpl(@Qualifier(BeanProvider.STUDIO_REST_TEMPLATE) RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    @Override
    public List<StudioAgencyDto> getAllAgencies() throws StudioException {
        try {
            StudioAgencyDto[] studioAgencyDtoList = restTemplate
                    .getForObject(agencyServiceEndpoint + "/api/v1/agencies", StudioAgencyDto[].class);
            if (studioAgencyDtoList == null) {
                return List.of();
            }
            return List.of(studioAgencyDtoList);
        } catch (RestClientException e) {
            throw new StudioException(e);
        }
    }

    @Override
    public List<StudioAgencyDto> getAllActiveAgencies() throws StudioException {
        return getAllAgencies().stream()
                .filter(studioAgencyDto -> Boolean.TRUE.equals(studioAgencyDto.getActivated()))
                .toList();
    }

    @Override
    public Optional<StudioAgencyDto> getAgencyById(Integer agencyId) throws StudioException {
        try {
            StudioAgencyDto studioAgencyDto = restTemplate
                    .getForObject(agencyServiceEndpoint + "/api/v1/agencies/" + agencyId, StudioAgencyDto.class);
            return Optional.ofNullable(studioAgencyDto);
        } catch (HttpStatusCodeException e) {
            if (e.getStatusCode() == HttpStatusCode.valueOf(404)) {
                return Optional.empty();
            }
            throw new StudioException(e);
        } catch (RestClientException e) {
            throw new StudioException(e);
        }
    }

    @Override
    public Map<Integer, StudioAgencyDto> getMapAgenciesByIds(List<Integer> agencyIds) throws StudioException {
        return getAllAgencies()
                .stream()
                .filter(studioAgencyDto -> agencyIds.contains(studioAgencyDto.getAgencyNo()))
                .collect(Collectors.toMap(StudioAgencyDto::getAgencyNo, Function.identity(), (a, b) -> a));
    }

    @Override
    public Pair<List<StudioAgencyDto>, Long> searchAgencies(StudioAgencySearchRequestDto requestDto)
            throws StudioException {
        List<StudioAgencyDto> filteredAgencyDtoList = getAllAgencies().stream()
                .filter(StudioAgencyFilterHelper.byText(requestDto.getText()))
                .filter(StudioAgencyFilterHelper.byAgencyIds(requestDto.getAgencyIds()))
                .filter(StudioAgencyFilterHelper.byExcludeAgencyIds(requestDto.getExcludeAgencyIds()))
                .filter(StudioAgencyFilterHelper.byIsEnabled(requestDto.getIsEnabled()))
                .sorted(Comparator.comparing(StudioAgencyDto::getAgencyNo))
                // TODO: Implement sorting by columns in requestDto
                .toList();

        List<StudioAgencyDto> pagedStudioUserDtoList = Paginator
                .paginate(filteredAgencyDtoList, requestDto.getPage(), requestDto.getSize());

        return Pair.of(pagedStudioUserDtoList, (long) filteredAgencyDtoList.size());
    }

}
