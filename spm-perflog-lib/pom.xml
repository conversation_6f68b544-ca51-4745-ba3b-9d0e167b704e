<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.3.0</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <groupId>com.siemens.spm</groupId>
    <artifactId>spm-perflog-lib</artifactId>
    <version>3.2.0</version>
    <name>spm-perflog-lib</name>
    <description>spm-perflog-lib</description>

    <properties>
        <java.version>17</java.version>

        <spm-common.version>3.2.0</spm-common.version>
        <spm-datahub-sdk.version>3.2.0</spm-datahub-sdk.version>

        <mockito.version>5.2.0</mockito.version>
    </properties>

    <dependencies>
        <!-- Internal -->
        <dependency>
            <groupId>com.siemens.spm</groupId>
            <artifactId>spm-common</artifactId>
            <version>${spm-common.version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.siemens.spm</groupId>
            <artifactId>spm-datahub-sdk</artifactId>
            <version>${spm-datahub-sdk.version}</version>
            <scope>provided</scope>
        </dependency>

        <!-- Other -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>

        <!--test scope-->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <version>${mockito.version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>
			<plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.3.1</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <phase>verify</phase>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>maven-repo-product</id>
            <name>maven-repo-product</name>
            <url>${env.MAVEN_REPO_PRODUCT_URL}</url>
        </repository>
<!--         <snapshotRepository>
            <id>maven-repo</id>
            <name>maven-repo</name>
            <url>${env.MAVEN_REPO_URL}</url>
        </snapshotRepository> -->
    </distributionManagement>

</project>
