/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : Location.java
 * Project     : SPM Platform
 */
package com.siemens.spm.perflog.vo;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class LocationVO implements Serializable {

    /**
     * 
     */
    private static final long serialVersionUID = -5727647660756247092L;

    @JsonProperty("lat")
    private double latitude;

    @JsonProperty("lng")
    private double longitude;

}