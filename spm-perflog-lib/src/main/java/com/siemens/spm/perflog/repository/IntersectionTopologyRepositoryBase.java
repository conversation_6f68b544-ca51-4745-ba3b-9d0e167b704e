package com.siemens.spm.perflog.repository;

import com.siemens.spm.perflog.persistence.IntersectionTopology;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface IntersectionTopologyRepositoryBase extends JpaRepository<IntersectionTopology, Long> {

    Optional<IntersectionTopology> findByIntUUID(String intUUID);

    boolean existsByIntUUID(String intUUID);

    void deleteAllByIntUUIDIn(List<String> intUUIDList);

}
