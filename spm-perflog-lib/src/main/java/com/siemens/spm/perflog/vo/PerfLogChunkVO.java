/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : PerfLogChunkVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.perflog.vo;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.perflog.domaintype.PerfLogChunkStatus;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
public class PerfLogChunkVO implements Serializable {

    public static final String CONFIGKEY_DELIM = "#";

    private static final long serialVersionUID = 1497672614592909848L;

    @JsonProperty("from_time")
    private LocalDateTime fromTime;

    @JsonProperty("to_time")
    private LocalDateTime toTime;

    @JsonProperty("config_id")
    private String configID;

    @JsonProperty("perflog_events")
    private List<PerfLogEventVO> perfLogEvents;

    @JsonProperty("is_no_data")
    private PerfLogChunkStatus status;

    /**
     * Check perlog chunk is no data or not?
     * 
     * @return
     */
    @JsonIgnore
    public boolean isNoData() {
        return PerfLogChunkStatus.NO_DATA.equals(status);
    }

}
