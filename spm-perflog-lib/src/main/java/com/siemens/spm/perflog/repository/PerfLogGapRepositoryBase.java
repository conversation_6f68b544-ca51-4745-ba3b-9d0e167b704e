/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : PerfLogGapRepository.java
 * Project     : SPM Platform
 */

package com.siemens.spm.perflog.repository;

import com.siemens.spm.perflog.persistence.PerfLogGap;
import com.siemens.spm.perflog.persistence.PerfLogGapID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface PerfLogGapRepositoryBase
    extends JpaRepository<PerfLogGap, PerfLogGapID>, JpaSpecificationExecutor<PerfLogGap> {

    @Transactional
    @Modifying
    @Query(value = "delete from PerfLogGap pg where pg.intUUID in (?1)")
    void deleteAllByIntUUIDIn(List<String> intUUIDs);

}
