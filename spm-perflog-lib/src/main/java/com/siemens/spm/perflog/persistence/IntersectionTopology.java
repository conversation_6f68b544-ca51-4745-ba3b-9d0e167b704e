package com.siemens.spm.perflog.persistence;

import java.util.List;

import com.siemens.spm.common.constant.AgencyConstants;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.PostLoad;
import jakarta.persistence.PostUpdate;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.validation.constraints.NotNull;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.siemens.spm.common.util.BeanFinder;
import com.siemens.spm.perflog.vo.ApproachVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@Table(name = IntersectionTopology.TABLE_NAME, schema = AgencyConstants.AGENCY_SCHEMA_PLACEHOLDER)
public class IntersectionTopology {
    public static final String TABLE_NAME = "intersection_topology";

    public static final class ColumnName {

        private ColumnName() {
        }

        public static final String ID = "id";
        public static final String INTERSECTION_TOPOLOGY = "int_topology";
        public static final String INTERSECTION_UUID = "int_uuid";
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = ColumnName.ID)
    private Long id;

    @NotNull
    @Column(name = ColumnName.INTERSECTION_TOPOLOGY, columnDefinition = "TEXT")
    private String approachListJson;

    @NotNull
    @Column(name = ColumnName.INTERSECTION_UUID, unique = true)
    private String intUUID;

    @Transient
    private List<ApproachVO> approachVOList;

    // TODO: Check if postUpdate is working or not
    @PostLoad
    @PostUpdate
    public void postLoadOrUpdate() {
        try {
            approachVOList = BeanFinder.getDefaultObjectMapper().readValue(approachListJson, new TypeReference<>() {});
        } catch (JsonProcessingException e) {
            // If error occur while deserialize, throw unchecked exception to mark as internal server error
            throw new IllegalStateException(e);
        }
    }

}
