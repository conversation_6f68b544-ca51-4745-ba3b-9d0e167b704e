/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : PatternInfoVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.perflog.vo;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ArrayUtils;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PatternInfoVO implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 7749966718576905947L;

    public static final int[] DEFAULT_SPLIT_TIMES_OF_PATTERN = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};

    /**
     * Reference the config data description file
     * '2' = none
     * '3' = Min Vehicle Recall
     * '4' = Max Vehicle Recall
     * '5' = Ped Recall
     * '6' = Max Vehicle & Ped Recall
     * '7' = Phase Omitted
     */
    public static final int[] DEFAULT_SPLIT_MODE_OF_PATTERN = {2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2};

    public static final int[] DEFAULT_SPLIT_COORDPHASES_OF_PATTERN = {0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0};

    @JsonProperty("cycleLength")
    private int[] cycleLength;

    @JsonProperty("splitMode")
    private int[][] splitMode;

    @JsonProperty("offsetTime")
    private int[] offsetTime;

    /**
     * outer index is pattern number - 1
     */
    @JsonProperty("sequenceNumber")
    private int[] sequenceNumber;

    @JsonProperty("splitCoordPhase")
    private int[][] splitCoordPhase;

    /**
     * outer index is pattern number - 1, inner index is phase number - 1
     */
    @JsonProperty("splitTime")
    private int[][] splitTime;

    /**
     * Consider to use this instead of get from getSplitMode() for the case that the configuration is not defined.
     * Normalize split mode of pattern for the case that the configuration is not defined.
     * @param pattern pattern number
     * @return split mode of pattern
     */
    public int[] getSplitModesOfPattern(int pattern) {
        int[] splitModesOfPattern = splitMode[pattern];
        if (ArrayUtils.isEmpty(splitModesOfPattern)) {
            splitModesOfPattern = DEFAULT_SPLIT_COORDPHASES_OF_PATTERN;
        }
        // This is for the case that the configuration is not defined. We made a mistake when set the default value is 0.
        for (int i = 0; i < splitModesOfPattern.length; i++) {
            if (splitModesOfPattern[i] == 0) {
                splitModesOfPattern[i] = DEFAULT_SPLIT_TIMES_OF_PATTERN[i];
            }
        }
        return splitModesOfPattern;
    }

    /**
     * Consider to use this instead of get from getSplitCoordPhase() for the case that the configuration is not defined.
     * Normalize split coordphase of pattern for the case that the configuration is not defined.
     * @param pattern pattern number
     * @return split coord phase of pattern
     */
    public int[] getSplitCoordPhasesOfPattern(int pattern) {
        int[] splitCoordPhasesOfPattern = splitCoordPhase[pattern];
        if (ArrayUtils.isEmpty(splitCoordPhasesOfPattern)) {
            splitCoordPhasesOfPattern = DEFAULT_SPLIT_COORDPHASES_OF_PATTERN;
        }
        return splitCoordPhasesOfPattern;
    }

    /**
     * Consider to use this instead of get from splitTime for the case that the configuration is not defined.
     * Normalize split time of pattern for the case that the configuration is not defined.
     * @param pattern pattern number
     * @return split time of pattern
     */
    public int[] getSplitTimeOfPattern(int pattern) {
        int[] splitTimesOfPattern = splitTime[pattern];
        if (ArrayUtils.isEmpty(splitTimesOfPattern)) {
            splitTimesOfPattern = DEFAULT_SPLIT_TIMES_OF_PATTERN;
        }
        return splitTimesOfPattern;
    }

}
