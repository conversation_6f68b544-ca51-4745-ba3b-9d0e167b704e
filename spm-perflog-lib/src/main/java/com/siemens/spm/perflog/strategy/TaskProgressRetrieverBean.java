package com.siemens.spm.perflog.strategy;

import java.util.Optional;

import jakarta.transaction.Transactional;
import jakarta.transaction.Transactional.TxType;

import org.springframework.beans.factory.annotation.Autowired;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.siemens.spm.common.shared.vo.ActionVO;
import com.siemens.spm.common.util.BeanFinder;
import com.siemens.spm.perflog.boundary.TaskProgressRetriever;
import com.siemens.spm.perflog.domaintype.TaskStatus;
import com.siemens.spm.perflog.persistence.TaskProgress;
import com.siemens.spm.perflog.repository.TaskProgressRepositoryBase;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> Mai
 */
@Slf4j
public class TaskProgressRetrieverBean implements TaskProgressRetriever {

    private static final String INVALID_TASK_ID = "Task ID: %s doesn't exist";

    @Autowired
    private TaskProgressRepositoryBase taskProgressRepository;

    /**
     * {@inheritDoc}}
     * <p>
     * NOTE: Define transaction is REQUIRES_NEW to persist task status immediately to DB
     */
    @Override
    @Transactional(value = TxType.REQUIRES_NEW)
    public boolean checkCancelProcess(Long taskID) {
        if (taskID == null) {
            return false;
        }

        Optional<TaskProgress> taskProgressOptional = taskProgressRepository.findById(taskID);
        if (taskProgressOptional.isEmpty()) {
            log.error(String.format(INVALID_TASK_ID, taskID));
            return false;
        }

        boolean result = false;
        TaskProgress taskProgress = taskProgressOptional.get();
        switch (taskProgress.getStatus()) {
        case QUEUED -> taskProgress.setStatus(TaskStatus.PROCESSING);
        case CANCELLING -> {
            taskProgress.setStatus(TaskStatus.CANCELLED);
            result = true;
        }
        default -> {
            // NOTE: Do nothing
        }
        }
        taskProgressRepository.save(taskProgress);

        String msg = String.format("Status of task ID: %d 's is updated to %s", taskID,
                taskProgress.getStatus().toString());
        log.debug(msg);
        return result;
    }

    /**
     * {@inheritDoc}}
     */
    @Override
    public void checkInterruptProcess(Long taskID) throws TaskCancelException {
        if (taskID == null) {
            log.error("Task ID is null");
            return;
        }

        Optional<TaskProgress> taskProgressOptional = taskProgressRepository.findById(taskID);
        if (taskProgressOptional.isEmpty()) {
            log.error(String.format(INVALID_TASK_ID, taskID));
            return;
        }

        TaskProgress taskProgress = taskProgressOptional.get();
//        taskProgressRepository.refresh(taskProgress);
        if (TaskStatus.CANCELLING.equals(taskProgress.getStatus())) {
            // NOTE: Throw exception to interrupt export process
            throw new TaskCancelException();
        }
    }

    /**
     * {@inheritDoc}}
     */
    @Override
    public void updateTaskProgress(Long taskID, TaskStatus status, ActionVO actionVO) {
        if (taskID == null || status == null)
            return;

        Optional<TaskProgress> taskProgressOptional = taskProgressRepository.findById(taskID);
        if (taskProgressOptional.isEmpty()) {
            log.error(String.format(INVALID_TASK_ID, taskID));
            return;
        }

        TaskProgress taskProgress = taskProgressOptional.get();
        taskProgress.setStatus(status);
        if (actionVO != null) {
            try {
                taskProgress.setAction(BeanFinder.getDefaultObjectMapper().writeValueAsString(actionVO));
            } catch (JsonProcessingException e) {
                log.error("Cann't parse ActionVO to String", e);
            }
        }

        taskProgressRepository.save(taskProgress);

        String msg = String.format("Status of task ID: %d 's is updated to %s", taskID, status);
        log.debug(msg);
    }

}
