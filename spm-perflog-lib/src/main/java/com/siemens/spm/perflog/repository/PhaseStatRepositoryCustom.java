package com.siemens.spm.perflog.repository;

import com.siemens.spm.perflog.persistence.PhaseStat;
import com.siemens.spm.perflog.repository.filterdata.PhaseStatFilterDataVO;
import com.siemens.spm.perflog.repository.projection.PhaseStatMetricProjection;

import java.time.LocalDateTime;
import java.util.List;

public interface PhaseStatRepositoryCustom {

    /**
     * EXTREMELY IMPORTANT: 
     * <p>
     * if {@link PhaseStatFilterDataVO#getIntUUIDList()} or {@link PhaseStatFilterDataVO#getPhases()} is empty, then there are NO filters for them.
     * <p>
     * More precisely, all phases are valid if the list of phases in {@code filterDataVO} is empty. 
     * Meaning, this method will NOT return empty list of phase stats when you filter by an empty list of coordinated phases.
     * BE CAREFUL with empty list.
     * 
     * @param filterDataVO
     * @return list of phase stats fullfilling the filter criteria
     */
    List<PhaseStat> findAllByFilter(PhaseStatFilterDataVO filterDataVO);

    /**
     * EXTREMELY IMPORTANT: 
     * <p>
     * if {@link PhaseStatFilterDataVO#getIntUUIDList()} or {@link PhaseStatFilterDataVO#getPhases()} is empty, then there are NO filters for them.
     * <p>
     * More precisely, all phases are valid if the list of phases in {@code filterDataVO} is empty. 
     * Meaning, this method will NOT return empty list of phase stats when you filter by an empty list of coordinated phases.
     * BE CAREFUL with empty list.
     * 
     * @param filterDataVO
     * @return list of phase stats fullfilling the filter criteria
     */
    List<PhaseStat> findAllByFilterAndGroupByFromTimeAndToTime(PhaseStatFilterDataVO filterDataVO);

    List<String> getIntersectionVolumeCount(LocalDateTime from,
                                            LocalDateTime to,
                                            Integer agencyId,
                                            List<String> intersectionIds);

    List<PhaseStatMetricProjection> findAllByIntUUIDAndFromTimeAndToTime(String intUUID,
                                                                         LocalDateTime fromTime,
                                                                         LocalDateTime toTime,
                                                                         List<String> metricList);

}
