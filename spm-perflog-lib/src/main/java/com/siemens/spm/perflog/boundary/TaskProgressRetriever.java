package com.siemens.spm.perflog.boundary;

import com.siemens.spm.common.shared.vo.ActionVO;
import com.siemens.spm.perflog.domaintype.TaskStatus;
import com.siemens.spm.perflog.strategy.TaskCancelException;

/**
 * <AUTHOR> Mai
 *
 */
public interface TaskProgressRetriever {

    /**
     * Check task progress is cancelled or not? If current status is QUEUED, set
     * status is PROCCESSING, current status is CANNCELING, set status is CANCELLED.
     * 
     * @param taskID
     * @return
     */
    boolean checkCancelProcess(Long taskID);

    /**
     * Check status of a task progress If status is CANCELLING, throw a
     * TaskCancelException
     * 
     * @param taskID
     * @throws TaskCancelException
     */
    void checkInterruptProcess(Long taskID) throws TaskCancelException;

    /**
     * Update status of a task progress
     * 
     * @param taskID
     * @param status
     * @param actionVO
     */
    void updateTaskProgress(Long taskID, TaskStatus status, ActionVO actionVO);
}
