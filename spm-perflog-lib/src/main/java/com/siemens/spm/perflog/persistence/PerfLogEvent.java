/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : IntersectionConfig.java
 * Project     : SPM Platform
 */
package com.siemens.spm.perflog.persistence;

import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PerfLogEvent {

    protected String intUUID;

    protected LocalDateTime datetime;

    protected int eventNum;

    /**
     * Standard range of parameter is [0:255].
     * Siemens proprietary parameter is of long range.
     */
    protected long parameter;

}
