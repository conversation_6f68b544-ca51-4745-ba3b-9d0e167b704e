package com.siemens.spm.perflog.vo;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LaneVO implements Serializable {

    private static final long serialVersionUID = -1218055615890052755L;

    @JsonProperty("Movement")
    private String movement;

    @JsonProperty("Detectors")
    private List<DetectorVO> detectors;

}
