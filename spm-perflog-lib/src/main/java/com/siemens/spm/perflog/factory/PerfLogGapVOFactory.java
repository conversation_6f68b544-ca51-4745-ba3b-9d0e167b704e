/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : PerfLogGapVOFactory.java
 * Project     : SPM Platform
 */
package com.siemens.spm.perflog.factory;

import com.siemens.spm.perflog.persistence.PerfLogGap;
import com.siemens.spm.perflog.vo.PerfLogGapVO;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class PerfLogGapVOFactory {

    private PerfLogGapVOFactory() {
        // Disable public constructor, may change to public if need create instance
    }

    /**
     * @param perfLogGapList
     * @return
     */
    public static List<PerfLogGapVO> create(List<PerfLogGap> perfLogGapList) {
        if (perfLogGapList == null)
            return Collections.emptyList();

        ArrayList<PerfLogGapVO> gapVOList = new ArrayList<>(perfLogGapList.size());
        for (PerfLogGap gap : perfLogGapList) {
            PerfLogGapVO gapVO = PerfLogGapVO.builder()
                    .fromTime(gap.getFromTime())
                    .toTime(gap.getToTime())
                    .gapType(gap.getGapType())
                    .build();
            gapVOList.add(gapVO);
        }

        return gapVOList;
    }

}
