/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : SequenceInfoVO.java
 * Project     : SPM Platform
 */
package com.siemens.spm.perflog.vo;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SequenceInfoVO implements Serializable {

    private static final long serialVersionUID = 6723197090673456382L;

    @JsonProperty("defaultSequence")
    private int defaultSequence;

}
