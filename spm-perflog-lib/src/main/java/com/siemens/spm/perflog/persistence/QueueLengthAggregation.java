package com.siemens.spm.perflog.persistence;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.siemens.spm.common.util.MathUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Slf4j
public class QueueLengthAggregation {

    @JsonProperty("sum_queue_length")
    private Double sumQueueLength;

    @JsonProperty("no_cycle")
    private Integer noCycle;

    public void merge(QueueLengthAggregation other) {
        if (other == null) {
            return;
        }

        this.sumQueueLength = MathUtil.plus(this.sumQueueLength, other.sumQueueLength);
        this.noCycle = MathUtil.plus(this.noCycle, other.noCycle);
    }

    public static QueueLengthAggregation clone(QueueLengthAggregation origin) {
        if (origin == null) {
            return QueueLengthAggregation.init();
        }
        return QueueLengthAggregation.builder()
                .sumQueueLength(origin.sumQueueLength)
                .noCycle(origin.noCycle)
                .build();
    }

    public static QueueLengthAggregation init() {
        return QueueLengthAggregation.builder()
                .sumQueueLength(0D)
                .noCycle(0)
                .build();
    }

    public double avgQueueLength() {
        if (sumQueueLength == null || noCycle == null || noCycle == 0) {
            return 0D;
        }

        if (sumQueueLength < 0 || noCycle < 0) {
            log.warn("sumQueueLength or noCycle is negative, sumQueueLength: {}, noCycle: {}", sumQueueLength, noCycle);
            sumQueueLength = 0D;
        }
        return sumQueueLength / noCycle;
    }

}
