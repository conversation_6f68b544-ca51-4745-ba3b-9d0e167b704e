/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AgencyIntersectionID.java
 * Project     : SPM Platform
 */
package com.siemens.spm.perflog.persistence;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
public class AgencyIntersectionID implements Serializable {

    private static final long serialVersionUID = -1736292028542535921L;

    private Integer agencyId;
    private String intUUID;

}
