package com.siemens.spm.perflog.factory;

import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDateTime;

import com.siemens.spm.common.util.BeanFinder;
import com.siemens.spm.common.util.NotificationUtil;
import com.siemens.spm.perflog.domaintype.TaskStatus;
import com.siemens.spm.perflog.domaintype.TaskType;
import com.siemens.spm.perflog.mock.BeanFinderMocker;
import com.siemens.spm.perflog.persistence.TaskProgress;
import com.siemens.spm.perflog.vo.UITaskStatus;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

class TaskProgressVOFactoryTest {

    private static MockedStatic<BeanFinder> beanFinderMocked;

    private static MockedStatic<NotificationUtil> notificationUtilMocked;

    private final int AGENCY_ID = 1;

    @BeforeEach
    void init() {
    }

    @BeforeAll
    static void initClass() {
        beanFinderMocked = Mockito.mockStatic(BeanFinder.class);
        notificationUtilMocked = Mockito.mockStatic(NotificationUtil.class);
        beanFinderMocked
                .when(BeanFinder::getDefaultObjectMapper)
                .thenReturn(BeanFinderMocker.objectMapper());
        beanFinderMocked
                .when(BeanFinder::getDefaultMessageService)
                .thenReturn(BeanFinderMocker.messageService());
    }

    @AfterAll
    static void afterTestClass() {
        beanFinderMocked.close();
        notificationUtilMocked.close();
    }

    @Test
    void test_create_null() {
        Assertions.assertThrows(IllegalArgumentException.class, () -> TaskProgressVOFactory.create(null));
    }

    @Test
    void test_create_id() {
        Timestamp timestampTest1 = Timestamp.from(Instant.now());
        Timestamp timestampTest2 = Timestamp.from(Instant.now());
        TaskProgress taskProgress = new TaskProgress();
        taskProgress.setId(1L);
        taskProgress.setType(TaskType.EXPORT_PERFLOG);
        taskProgress.setAgencyId(AGENCY_ID);
        taskProgress.setIntUUID("");
        taskProgress.setIntName("");
        taskProgress.setReportName("");
        taskProgress.setUserId(2L);
        taskProgress.setFromTime(LocalDateTime.MIN);
        taskProgress.setToTime(LocalDateTime.MAX);
        taskProgress.setAction("");
        taskProgress.setStatus(TaskStatus.CANCELLED);
        taskProgress.setProgress(2);
        taskProgress.setDeleted(true);
        taskProgress.setCreatedAt(timestampTest1);
        taskProgress.setLastModifiedAt(timestampTest2);
        Assertions.assertEquals(1L, TaskProgressVOFactory.create(taskProgress).getId());
        Assertions.assertEquals(timestampTest1, TaskProgressVOFactory.create(taskProgress).getCreatedAt());
        Assertions.assertEquals(timestampTest2, TaskProgressVOFactory.create(taskProgress).getLastModifiedAt());
    }

    @Test
    void test_create_title() {
        TaskProgress taskProgress = new TaskProgress();
        taskProgress.setId(1L);
        taskProgress.setAgencyId(AGENCY_ID);
        taskProgress.setReportName("");
        taskProgress.setUserId(2L);
        taskProgress.setFromTime(LocalDateTime.MIN);
        taskProgress.setToTime(LocalDateTime.MAX);
        taskProgress.setAction("");
        taskProgress.setStatus(TaskStatus.CANCELLED);
        taskProgress.setProgress(2);
        taskProgress.setDeleted(true);
        taskProgress.setCreatedAt(Timestamp.from(Instant.now()));
        taskProgress.setLastModifiedAt(Timestamp.from(Instant.now()));
        taskProgress.setIntUUID("");

        taskProgress.setIntName("test name");
        taskProgress.setType(TaskType.EXPORT_PERFLOG);
        Assertions.assertEquals("export_progress_title test name test name",
                TaskProgressVOFactory.create(taskProgress).getTitle());

        taskProgress.setType(TaskType.SUMMARY_REPORT);
        Assertions.assertEquals("summary_report_title name", TaskProgressVOFactory.create(taskProgress).getTitle());

        taskProgress.setType(TaskType.PERFORMANCE_METRIC);
        Assertions.assertEquals("performance_metric_title name", TaskProgressVOFactory.create(taskProgress).getTitle());

    }

    @Test
    void test_create_description() {
        TaskProgress taskProgress = new TaskProgress();
        taskProgress.setId(1L);
        taskProgress.setAgencyId(AGENCY_ID);
        taskProgress.setReportName("");
        taskProgress.setUserId(2L);
        taskProgress.setFromTime(LocalDateTime.MIN);
        taskProgress.setToTime(LocalDateTime.MAX);
        taskProgress.setAction("");
        taskProgress.setStatus(TaskStatus.CANCELLED);
        taskProgress.setProgress(2);
        taskProgress.setDeleted(true);
        taskProgress.setCreatedAt(Timestamp.from(Instant.now()));
        taskProgress.setLastModifiedAt(Timestamp.from(Instant.now()));
        taskProgress.setIntName("test name");

        taskProgress.setIntUUID("test uuid");
        taskProgress.setType(TaskType.EXPORT_PERFLOG);
        Assertions.assertEquals(
                "export_progress_description Intersection UUID: test uuid; from: -999999999-01-01T00:00:00 to +999999999-12-31T23:59:59.999999999",
                TaskProgressVOFactory.create(taskProgress).getDescription());

        taskProgress.setType(TaskType.PERFORMANCE_METRIC);
        Assertions.assertNull(TaskProgressVOFactory.create(taskProgress).getDescription());

        taskProgress.setReportName("turning_movement");
        taskProgress.setType(TaskType.SUMMARY_REPORT);
        Assertions.assertEquals("summary_report_description name",
                TaskProgressVOFactory.create(taskProgress).getDescription());

        taskProgress.setReportName("turning_movement");
        taskProgress.setType(TaskType.SUMMARY_REPORT);
        Assertions.assertEquals("summary_report_description name",
                TaskProgressVOFactory.create(taskProgress).getDescription());
    }

    @Test
    void test_create_status() {
        TaskProgress taskProgress = new TaskProgress();
        taskProgress.setId(1L);
        taskProgress.setAgencyId(AGENCY_ID);
        taskProgress.setReportName("");
        taskProgress.setUserId(2L);
        taskProgress.setFromTime(LocalDateTime.MIN);
        taskProgress.setToTime(LocalDateTime.MAX);
        taskProgress.setAction("");
        taskProgress.setProgress(2);
        taskProgress.setDeleted(true);
        taskProgress.setCreatedAt(Timestamp.from(Instant.now()));
        taskProgress.setLastModifiedAt(Timestamp.from(Instant.now()));
        taskProgress.setIntName("test name");

        taskProgress.setIntUUID("test uuid");
        taskProgress.setType(TaskType.EXPORT_PERFLOG);

        taskProgress.setStatus(TaskStatus.COMPLETED);
        Assertions.assertEquals(UITaskStatus.COMPLETED, TaskProgressVOFactory.create(taskProgress).getStatus());

        taskProgress.setStatus(TaskStatus.ERROR);
        Assertions.assertEquals(UITaskStatus.ERROR, TaskProgressVOFactory.create(taskProgress).getStatus());

        taskProgress.setStatus(TaskStatus.QUEUED);
        Assertions.assertEquals(UITaskStatus.PROCESSING, TaskProgressVOFactory.create(taskProgress).getStatus());
        taskProgress.setStatus(TaskStatus.PROCESSING);
        Assertions.assertEquals(UITaskStatus.PROCESSING, TaskProgressVOFactory.create(taskProgress).getStatus());

        taskProgress.setStatus(TaskStatus.CANCELLING);
        Assertions.assertEquals(UITaskStatus.CANCELLED, TaskProgressVOFactory.create(taskProgress).getStatus());
        taskProgress.setStatus(TaskStatus.CANCELLED);
        Assertions.assertEquals(UITaskStatus.CANCELLED, TaskProgressVOFactory.create(taskProgress).getStatus());

        taskProgress.setStatus(null);
        Assertions.assertNull(TaskProgressVOFactory.create(taskProgress).getStatus());
    }

    @Test
    void test_create_errorMessage() {
        TaskProgress taskProgress = new TaskProgress();
        taskProgress.setId(1L);
        taskProgress.setAgencyId(AGENCY_ID);
        taskProgress.setReportName("");
        taskProgress.setUserId(2L);
        taskProgress.setFromTime(LocalDateTime.MIN);
        taskProgress.setToTime(LocalDateTime.MAX);
        taskProgress.setAction("");
        taskProgress.setProgress(2);
        taskProgress.setDeleted(true);
        taskProgress.setCreatedAt(Timestamp.from(Instant.now()));
        taskProgress.setLastModifiedAt(Timestamp.from(Instant.now()));
        taskProgress.setIntName("test name");
        taskProgress.setIntUUID("test uuid");
        taskProgress.setType(TaskType.EXPORT_PERFLOG);

        taskProgress.setStatus(TaskStatus.CANCELLING);
        Assertions.assertNull(TaskProgressVOFactory.create(taskProgress).getErrorMessage());
        taskProgress.setStatus(TaskStatus.COMPLETED);
        Assertions.assertNull(TaskProgressVOFactory.create(taskProgress).getErrorMessage());
        taskProgress.setStatus(TaskStatus.CANCELLED);
        Assertions.assertNull(TaskProgressVOFactory.create(taskProgress).getErrorMessage());
        taskProgress.setStatus(TaskStatus.QUEUED);
        Assertions.assertNull(TaskProgressVOFactory.create(taskProgress).getErrorMessage());
        taskProgress.setStatus(TaskStatus.PROCESSING);
        Assertions.assertNull(TaskProgressVOFactory.create(taskProgress).getErrorMessage());

        taskProgress.setStatus(TaskStatus.ERROR);
        Assertions.assertEquals("export_failed_message name",
                TaskProgressVOFactory.create(taskProgress).getErrorMessage());

        taskProgress.setType(TaskType.SUMMARY_REPORT);
        taskProgress.setStatus(TaskStatus.ERROR);
        Assertions.assertEquals("summary_report_failed_message name",
                TaskProgressVOFactory.create(taskProgress).getErrorMessage());

        taskProgress.setType(TaskType.PERFORMANCE_METRIC);
        taskProgress.setStatus(TaskStatus.ERROR);
        Assertions.assertEquals("performance_metric_failed_message name",
                TaskProgressVOFactory.create(taskProgress).getErrorMessage());

    }

    @Test
    void test_create_canCancel() {
        TaskProgress taskProgress = new TaskProgress();
        taskProgress.setId(1L);
        taskProgress.setAgencyId(AGENCY_ID);
        taskProgress.setReportName("");
        taskProgress.setUserId(2L);
        taskProgress.setFromTime(LocalDateTime.MIN);
        taskProgress.setToTime(LocalDateTime.MAX);
        taskProgress.setAction("");
        taskProgress.setProgress(2);
        taskProgress.setDeleted(true);
        taskProgress.setCreatedAt(Timestamp.from(Instant.now()));
        taskProgress.setLastModifiedAt(Timestamp.from(Instant.now()));
        taskProgress.setIntName("test name");
        taskProgress.setIntUUID("test uuid");

        taskProgress.setType(TaskType.SUMMARY_REPORT);
        Assertions.assertFalse(TaskProgressVOFactory.create(taskProgress).isCanCancel());
        taskProgress.setType(TaskType.PERFORMANCE_METRIC);
        taskProgress.setStatus(TaskStatus.QUEUED);
        Assertions.assertTrue(TaskProgressVOFactory.create(taskProgress).isCanCancel());
        taskProgress.setStatus(TaskStatus.PROCESSING);
        Assertions.assertTrue(TaskProgressVOFactory.create(taskProgress).isCanCancel());
        taskProgress.setStatus(TaskStatus.ERROR);
        Assertions.assertFalse(TaskProgressVOFactory.create(taskProgress).isCanCancel());
        taskProgress.setStatus(TaskStatus.CANCELLED);
        Assertions.assertFalse(TaskProgressVOFactory.create(taskProgress).isCanCancel());
        taskProgress.setStatus(TaskStatus.CANCELLING);
        Assertions.assertFalse(TaskProgressVOFactory.create(taskProgress).isCanCancel());
        taskProgress.setStatus(TaskStatus.COMPLETED);
        Assertions.assertFalse(TaskProgressVOFactory.create(taskProgress).isCanCancel());

        taskProgress.setType(TaskType.EXPORT_PERFLOG);
        taskProgress.setStatus(TaskStatus.QUEUED);
        Assertions.assertTrue(TaskProgressVOFactory.create(taskProgress).isCanCancel());
        taskProgress.setStatus(TaskStatus.PROCESSING);
        Assertions.assertTrue(TaskProgressVOFactory.create(taskProgress).isCanCancel());
        taskProgress.setStatus(TaskStatus.ERROR);
        Assertions.assertFalse(TaskProgressVOFactory.create(taskProgress).isCanCancel());
        taskProgress.setStatus(TaskStatus.CANCELLED);
        Assertions.assertFalse(TaskProgressVOFactory.create(taskProgress).isCanCancel());
        taskProgress.setStatus(TaskStatus.CANCELLING);
        Assertions.assertFalse(TaskProgressVOFactory.create(taskProgress).isCanCancel());
        taskProgress.setStatus(TaskStatus.COMPLETED);
        Assertions.assertFalse(TaskProgressVOFactory.create(taskProgress).isCanCancel());
    }

    @Test
    void test_create_canRemove() {
        TaskProgress taskProgress = new TaskProgress();
        taskProgress.setId(1L);
        taskProgress.setAgencyId(AGENCY_ID);
        taskProgress.setReportName("");
        taskProgress.setUserId(2L);
        taskProgress.setFromTime(LocalDateTime.MIN);
        taskProgress.setToTime(LocalDateTime.MAX);
        taskProgress.setAction("");
        taskProgress.setProgress(2);
        taskProgress.setDeleted(true);
        taskProgress.setCreatedAt(Timestamp.from(Instant.now()));
        taskProgress.setLastModifiedAt(Timestamp.from(Instant.now()));
        taskProgress.setIntName("test name");
        taskProgress.setIntUUID("test uuid");
        taskProgress.setType(TaskType.SUMMARY_REPORT);

        taskProgress.setStatus(TaskStatus.COMPLETED);
        Assertions.assertTrue(TaskProgressVOFactory.create(taskProgress).isCanRemove());
        taskProgress.setStatus(TaskStatus.ERROR);
        Assertions.assertTrue(TaskProgressVOFactory.create(taskProgress).isCanRemove());
        taskProgress.setStatus(TaskStatus.CANCELLING);
        Assertions.assertTrue(TaskProgressVOFactory.create(taskProgress).isCanRemove());
        taskProgress.setStatus(TaskStatus.CANCELLED);
        Assertions.assertTrue(TaskProgressVOFactory.create(taskProgress).isCanRemove());

        taskProgress.setStatus(TaskStatus.QUEUED);
        Assertions.assertFalse(TaskProgressVOFactory.create(taskProgress).isCanRemove());
        taskProgress.setStatus(TaskStatus.PROCESSING);
        Assertions.assertFalse(TaskProgressVOFactory.create(taskProgress).isCanRemove());

    }
}
