package com.siemens.spm.perflog.domaintype;

import com.siemens.spm.common.util.BeanFinder;
import com.siemens.spm.perflog.mock.BeanFinderMocker;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

class PerflogEventGroupTest {

    private static MockedStatic<BeanFinder> beanFinderMocked;

    @BeforeEach
    void init() {
    }

    @BeforeAll
    static void initClass() {
        beanFinderMocked = Mockito.mockStatic(BeanFinder.class);
        beanFinderMocked
                .when(BeanFinder::getDefaultObjectMapper)
                .thenReturn(BeanFinderMocker.objectMapper());
        beanFinderMocked
                .when(BeanFinder::getDefaultMessageService)
                .thenReturn(BeanFinderMocker.messageService());
    }

    @AfterAll
    static void afterTestClass() {
        beanFinderMocked.close();
    }

    @Test
    void test_getTranslatedName() {
        Assertions.assertEquals("active_phase text test en", PerflogEventGroup.ACTIVE_PHASE.getTranslatedName());
    }

    @Test
    void test_constructor() {
        Assertions.assertEquals(0, PerflogEventGroup.ACTIVE_PHASE.getStartEvent());
        Assertions.assertEquals(20, PerflogEventGroup.ACTIVE_PHASE.getEndEvent());
        Assertions.assertEquals("active_phase", PerflogEventGroup.ACTIVE_PHASE.getKeyName());
    }
}
