package com.siemens.spm.perflog.domaintype;

import com.siemens.spm.common.util.BeanFinder;
import com.siemens.spm.perflog.mock.BeanFinderMocker;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

class ApproachMovementTest {

    private static MockedStatic<BeanFinder> beanFinderMocked;

    @BeforeEach
    void init() {
    }

    @BeforeAll
    static void initClass() {
        beanFinderMocked = Mockito.mockStatic(BeanFinder.class);
        beanFinderMocked
                .when(BeanFinder::getDefaultObjectMapper)
                .thenReturn(BeanFinderMocker.objectMapper());
        beanFinderMocked
                .when(BeanFinder::getDefaultMessageService)
                .thenReturn(BeanFinderMocker.messageService());
    }

    @AfterAll
    static void afterTestClass() {
        beanFinderMocked.close();
    }

    @Test
    void test_resolve() {
        Assertions.assertEquals(ApproachMovement.UNKNOWN, ApproachMovement.resolve(null));
        Assertions.assertEquals(ApproachMovement.RIGHT, ApproachMovement.resolve("Right"));
        Assertions.assertEquals(ApproachMovement.LEFT, ApproachMovement.resolve("Left"));
        Assertions.assertEquals(ApproachMovement.STRAIGHT, ApproachMovement.resolve("Through"));
        Assertions.assertEquals(ApproachMovement.STRAIGHT_LEFT, ApproachMovement.resolve("Through/Left"));
        Assertions.assertEquals(ApproachMovement.STRAIGHT_RIGHT, ApproachMovement.resolve("Through/Right"));
        Assertions.assertEquals(ApproachMovement.UNKNOWN, ApproachMovement.resolve("ballad"));
    }
}
