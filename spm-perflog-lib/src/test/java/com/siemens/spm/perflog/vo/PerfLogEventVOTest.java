package com.siemens.spm.perflog.vo;

import java.time.LocalDateTime;

import com.siemens.spm.common.util.BeanFinder;
import com.siemens.spm.perflog.mock.BeanFinderMocker;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

class PerfLogEventVOTest {

    private static MockedStatic<BeanFinder> beanFinderMocked;

    @BeforeEach
    void init() {
    }

    @BeforeAll
    static void initClass() {
        beanFinderMocked = Mockito.mockStatic(BeanFinder.class);
        beanFinderMocked
                .when(BeanFinder::getDefaultObjectMapper)
                .thenReturn(BeanFinderMocker.objectMapper());
        beanFinderMocked
                .when(BeanFinder::getDefaultMessageService)
                .thenReturn(BeanFinderMocker.messageService());
    }

    @Test
    void testEvent() {
        Assertions.assertEquals(131, PerfLogEventVO.Event.COORD_PATTERN_CHANGE.getEventNum());
        Assertions.assertEquals(PerfLogEventVO.Event.COORD_PATTERN_CHANGE, PerfLogEventVO.Event.of(131));
        Assertions.assertEquals("COORD PATTERN CHANGE name test en",
                PerfLogEventVO.Event.COORD_PATTERN_CHANGE.translatedName());
    }

    @Test
    void test_getEventMap() {
        Assertions.assertEquals(
                PerfLogEventVO.Event.COORD_PATTERN_CHANGE,
                PerfLogEventVO.getEventMap().get(PerfLogEventVO.Event.COORD_PATTERN_CHANGE.getEventNum())
        );
    }

    @Test
    void test_getSet() {
        final Long paramTest = 2L;
        final LocalDateTime datetimeTest = LocalDateTime.of(2022, 01, 01, 01, 01, 01);

        PerfLogEventVO perfLogEventVO = new PerfLogEventVO();
        perfLogEventVO.setParameter(paramTest);
        perfLogEventVO.setDateTime(datetimeTest);
        perfLogEventVO.setEventNum(300);

        Assertions.assertEquals(paramTest, perfLogEventVO.getParameter());
        Assertions.assertEquals(datetimeTest, perfLogEventVO.getDateTime());
        Assertions.assertNull(perfLogEventVO.getEvent());

        perfLogEventVO.setEventNum(2);
        Assertions.assertEquals(PerfLogEventVO.Event.PHASE_CHECK, perfLogEventVO.getEvent());

        perfLogEventVO.setEvent(PerfLogEventVO.Event.PHASE_CHECK);
        Assertions.assertEquals(PerfLogEventVO.Event.PHASE_CHECK, perfLogEventVO.getEvent());

    }

    @Test
    void test_allMethod() {
        final long paramTest = 2L;
        final LocalDateTime datetimeTest = LocalDateTime.of(2022, 01, 01, 01, 01, 01);

        PerfLogEventVO perfLogEventVO = new PerfLogEventVO();
        perfLogEventVO.setParameter(paramTest);
        perfLogEventVO.setDateTime(datetimeTest);
        perfLogEventVO.setEvent(null);


        Assertions.assertFalse(perfLogEventVO.isPhaseEvent());
        Assertions.assertFalse(perfLogEventVO.isPedestrianEvent());
        Assertions.assertFalse(perfLogEventVO.isPreemptionEvent());
        Assertions.assertFalse(perfLogEventVO.isPriorityEvent());
        Assertions.assertFalse(perfLogEventVO.isGreenSignal());
        Assertions.assertFalse(perfLogEventVO.isColorChangeSignal());
        Assertions.assertFalse(perfLogEventVO.isRedSignal());
        Assertions.assertFalse(perfLogEventVO.isYellowSignal());

        //  test isPhaseEvent
        perfLogEventVO.setEventNum(5);
        Assertions.assertTrue(perfLogEventVO.isPhaseEvent());
        perfLogEventVO.setEvent(PerfLogEventVO.Event.PHASE_ON);
        Assertions.assertTrue(perfLogEventVO.isPhaseEvent());
        perfLogEventVO.setEvent(PerfLogEventVO.Event.PHASE_INACTIVE);
        Assertions.assertTrue(perfLogEventVO.isPhaseEvent());
        perfLogEventVO.setEvent(PerfLogEventVO.Event.PHASE_END_YELLOW_CLEARANCE);
        Assertions.assertTrue(perfLogEventVO.isPhaseEvent());
        perfLogEventVO.setEvent(PerfLogEventVO.Event.PHASE_SKIP);
        Assertions.assertFalse(perfLogEventVO.isPhaseEvent());
        perfLogEventVO.setEvent(PerfLogEventVO.Event.NO_DATA);
        Assertions.assertFalse(perfLogEventVO.isPhaseEvent());

        //  isPedestrianEvent
        perfLogEventVO.setEventNum(5);
        Assertions.assertFalse(perfLogEventVO.isPedestrianEvent());
        perfLogEventVO.setEvent(PerfLogEventVO.Event.PHASE_ON);
        Assertions.assertTrue(perfLogEventVO.isPedestrianEvent());
        perfLogEventVO.setEvent(PerfLogEventVO.Event.PEDESTRIAN_BEGIN_WALK);
        Assertions.assertTrue(perfLogEventVO.isPedestrianEvent());
        perfLogEventVO.setEvent(PerfLogEventVO.Event.PEDESTRIAN_DARK);
        Assertions.assertTrue(perfLogEventVO.isPedestrianEvent());
        perfLogEventVO.setEvent(PerfLogEventVO.Event.PEDESTRIAN_BEGIN_NOT_WALK);
        Assertions.assertTrue(perfLogEventVO.isPedestrianEvent());
        perfLogEventVO.setEvent(PerfLogEventVO.Event.BARRIER_TERMINATION);
        Assertions.assertFalse(perfLogEventVO.isPedestrianEvent());

        //  isPriorityEvent
        perfLogEventVO.setEventNum(5);
        Assertions.assertFalse(perfLogEventVO.isPriorityEvent());
        perfLogEventVO.setEvent(PerfLogEventVO.Event.TSP_CHECK_IN);
        Assertions.assertTrue(perfLogEventVO.isPriorityEvent());
        perfLogEventVO.setEvent(PerfLogEventVO.Event.TSP_CHECK_OUT);
        Assertions.assertTrue(perfLogEventVO.isPriorityEvent());
        perfLogEventVO.setEvent(PerfLogEventVO.Event.TSP_ADJUSTMENT_TO_EARLY_GREEN);
        Assertions.assertTrue(perfLogEventVO.isPriorityEvent());
        perfLogEventVO.setEvent(PerfLogEventVO.Event.COORD_PATTERN_CHANGE);
        Assertions.assertFalse(perfLogEventVO.isPriorityEvent());

        //  isPreemptionEvent
        perfLogEventVO.setEventNum(5);
        Assertions.assertFalse(perfLogEventVO.isPreemptionEvent());
        perfLogEventVO.setEvent(PerfLogEventVO.Event.PREEMPT_ADVANCE_WARNING_INPUT);
        Assertions.assertTrue(perfLogEventVO.isPreemptionEvent());
        perfLogEventVO.setEvent(PerfLogEventVO.Event.PREEMPTION_BEGIN_EXIT_INTERVAL);
        Assertions.assertTrue(perfLogEventVO.isPreemptionEvent());
        perfLogEventVO.setEvent(PerfLogEventVO.Event.PREEMPTION_LINK_ACTIVE_OFF);
        Assertions.assertTrue(perfLogEventVO.isPreemptionEvent());
        perfLogEventVO.setEvent(PerfLogEventVO.Event.TSP_CHECK_IN);
        Assertions.assertFalse(perfLogEventVO.isPreemptionEvent());


        //  isColorChangeSignal
        perfLogEventVO.setEvent(PerfLogEventVO.Event.PHASE_BEGIN_GREEN);
        Assertions.assertTrue(perfLogEventVO.isGreenSignal());
        Assertions.assertFalse(perfLogEventVO.isYellowSignal());
        Assertions.assertFalse(perfLogEventVO.isRedSignal());
        Assertions.assertTrue(perfLogEventVO.isColorChangeSignal());

        perfLogEventVO.setEvent(PerfLogEventVO.Event.PHASE_BEGIN_RED_CLEARANCE);
        Assertions.assertFalse(perfLogEventVO.isGreenSignal());
        Assertions.assertFalse(perfLogEventVO.isYellowSignal());
        Assertions.assertTrue(perfLogEventVO.isRedSignal());
        Assertions.assertTrue(perfLogEventVO.isColorChangeSignal());

        perfLogEventVO.setEvent(PerfLogEventVO.Event.PHASE_BEGIN_YELLOW_CLEARANCE);
        Assertions.assertFalse(perfLogEventVO.isGreenSignal());
        Assertions.assertTrue(perfLogEventVO.isYellowSignal());
        Assertions.assertFalse(perfLogEventVO.isRedSignal());
        Assertions.assertTrue(perfLogEventVO.isColorChangeSignal());

        perfLogEventVO.setEvent(PerfLogEventVO.Event.PHASE_END_RED_CLEARANCE);
        Assertions.assertFalse(perfLogEventVO.isColorChangeSignal());

    }

}
