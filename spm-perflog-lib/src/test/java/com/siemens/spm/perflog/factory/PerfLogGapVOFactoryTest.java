package com.siemens.spm.perflog.factory;

import java.time.LocalDateTime;
import java.util.List;

import com.siemens.spm.perflog.persistence.PerfLogGap;
import com.siemens.spm.perflog.vo.PerfLogGapVO;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class PerfLogGapVOFactoryTest {

    @Test
    void test_create() {

        Assertions.assertNotNull(PerfLogGapVOFactory.create(null));
        Assertions.assertNotNull(PerfLogGapVOFactory.create(List.of()));
        Assertions.assertTrue(PerfLogGapVOFactory.create(List.of()).isEmpty());
        Assertions.assertEquals(1, PerfLogGapVOFactory.create(List.of(PerfLogGap.builder().build())).size());
        List<PerfLogGapVO> perfLogGapVOS = PerfLogGapVOFactory.create(
                List.of(PerfLogGap.builder().fromTime(LocalDateTime.now()).toTime(LocalDateTime.MAX).build()));
        Assertions.assertNotNull(perfLogGapVOS.get(0).getFromTime());
        Assertions.assertNotNull(perfLogGapVOS.get(0).getToTime());
    }
}
