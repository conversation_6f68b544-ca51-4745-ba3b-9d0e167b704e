package com.siemens.spm.perflog.factory;

import java.time.LocalDateTime;

import com.siemens.spm.perflog.persistence.PerfLogEvent;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class PerfLogEventFactoryTest {

    @Test
    void test_create() {

        Assertions.assertThrows(IllegalArgumentException.class, () -> PerfLogEventFactory.create(null, null, null));

        Assertions.assertDoesNotThrow(() -> {
            PerfLogEvent perfLogEvent = PerfLogEventFactory.create(
                    PerfLogEventVO.builder()
                            .event(PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE)
                            .dateTime(LocalDateTime.MAX)
                            .parameter(1L)
                            .build(),
                    "test",
                    PerfLogEvent.class);
            Assertions.assertEquals(PerfLogEventVO.Event.COORD_CYCLE_STATE_CHANGE.getEventNum(), perfLogEvent.getEventNum());
            Assertions.assertEquals("test", perfLogEvent.getIntUUID());
            Assertions.assertEquals(1L, perfLogEvent.getParameter());
            Assertions.assertEquals(LocalDateTime.MAX, perfLogEvent.getDatetime());
        });

    }
}
