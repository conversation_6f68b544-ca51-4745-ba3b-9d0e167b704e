package com.siemens.spm.common.agency.exception;

/**
 * Exception thrown when schema creation operations fail.
 * This runtime exception is used to handle schema-related errors in the application.
 */
public class SchemaCreationException extends RuntimeException {

    /**
     * Constructs a new SchemaCreationException with the specified detail message and cause.
     *
     * @param message the detail message (which is saved for later retrieval by the getMessage() method)
     * @param cause   the cause (which is saved for later retrieval by the getCause() method)
     */
    public SchemaCreationException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * Constructs a new SchemaCreationException with the specified detail message.
     *
     * @param message the detail message (which is saved for later retrieval by the getMessage() method)
     */
    public SchemaCreationException(String message) {
        super(message);
    }

    /**
     * Constructs a new SchemaCreationException with the specified cause.
     *
     * @param cause the cause (which is saved for later retrieval by the getCause() method)
     */
    public SchemaCreationException(Throwable cause) {
        super(cause);
    }

}