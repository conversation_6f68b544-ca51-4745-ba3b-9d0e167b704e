package com.siemens.spm.common.agency.supports.provision;

import com.siemens.spm.common.agency.exception.AgencySchemaInitializationException;
import com.siemens.spm.common.agency.exception.SchemaCreationException;
import liquibase.exception.LiquibaseException;
import liquibase.integration.spring.SpringLiquibase;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.liquibase.LiquibaseProperties;
import org.springframework.core.io.ResourceLoader;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.StatementCallback;
import org.springframework.util.Assert;

import javax.sql.DataSource;
import java.util.Collections;
import java.util.Map;
import java.util.Optional;

@Slf4j
public class AgencyLiquibaseService {

    public static final String CREATE_SCHEMA = "CREATE SCHEMA IF NOT EXISTS %s;";
    private final LiquibaseProperties agencyLiquibaseProperties;
    private final DataSource dataSource;
    private final ResourceLoader resourceLoader;
    private final JdbcTemplate jdbcTemplate;

    public AgencyLiquibaseService(LiquibaseProperties agencyLiquibaseProperties, DataSource dataSource,
        ResourceLoader resourceLoader, JdbcTemplate jdbcTemplate) {
        Assert.notNull(agencyLiquibaseProperties, "LiquibaseProperties must not be null");
        Assert.notNull(dataSource, "DataSource must not be null");
        Assert.notNull(resourceLoader, "ResourceLoader must not be null");
        Assert.notNull(jdbcTemplate, "JdbcTemplate must not be null");

        this.agencyLiquibaseProperties = agencyLiquibaseProperties;
        this.dataSource = dataSource;
        this.resourceLoader = resourceLoader;
        this.jdbcTemplate = jdbcTemplate;
    }

    public void initAgencySchema(Integer agencyId, String schemaName) {
        Assert.notNull(agencyId, "Agency ID must not be null");
        Assert.hasText(schemaName, "Schema name must not be empty");

        if (agencyId == 0) {
            log.debug("Skipping initialization for public schema (agencyId = 0)");
            return;
        }

        log.info("Initializing liquibase for agencyId {} with schema {}", agencyId, schemaName);

        try {
            createSchema(schemaName);
            initSchemaByLiquibase(schemaName);
            log.info("Successfully initialized liquibase for agency {} with schemaName {}", agencyId, schemaName);
        } catch (Exception e) {
            String errorMessage = String.format("Failed to initialize liquibase for agency %d", agencyId);
            log.error(errorMessage, e);
            throw new AgencySchemaInitializationException(errorMessage, e);
        }
    }

    private void initSchemaByLiquibase(String schemaName) throws LiquibaseException {
        SpringLiquibase liquibase = createLiquibaseInstance(schemaName);
        configureLiquibaseProperties(liquibase, schemaName);
        liquibase.afterPropertiesSet();
    }

    private SpringLiquibase createLiquibaseInstance(String schemaName) {
        SpringLiquibase liquibase = new SpringLiquibase();
        liquibase.setDataSource(dataSource);
        liquibase.setResourceLoader(resourceLoader);
        liquibase.setDefaultSchema(schemaName);
        return liquibase;
    }

    private void configureLiquibaseProperties(SpringLiquibase liquibase, String schemaName) {
        liquibase.setChangeLogParameters(buildChangeLogParameters(schemaName));
        liquibase.setChangeLog(agencyLiquibaseProperties.getChangeLog());
        liquibase.setContexts(agencyLiquibaseProperties.getContexts());
        liquibase.setLiquibaseSchema(agencyLiquibaseProperties.getLiquibaseSchema());
        liquibase.setLiquibaseTablespace(agencyLiquibaseProperties.getLiquibaseTablespace());
        liquibase.setDatabaseChangeLogTable(agencyLiquibaseProperties.getDatabaseChangeLogTable());
        liquibase.setDatabaseChangeLogLockTable(agencyLiquibaseProperties.getDatabaseChangeLogLockTable());
        liquibase.setDropFirst(agencyLiquibaseProperties.isDropFirst());
        liquibase.setShouldRun(agencyLiquibaseProperties.isEnabled());
        liquibase.setRollbackFile(agencyLiquibaseProperties.getRollbackFile());
        liquibase.setTestRollbackOnUpdate(agencyLiquibaseProperties.isTestRollbackOnUpdate());
    }

    private Map<String, String> buildChangeLogParameters(String schemaName) {
        return Optional.ofNullable(agencyLiquibaseProperties.getParameters()).map(params -> {
            params.put("schema", schemaName);
            return params;
        }).orElse(Collections.singletonMap("schema", schemaName));
    }

    private void createSchema(String schemaName) {
        try {
            boolean created = Boolean.TRUE.equals(jdbcTemplate.execute(
                (StatementCallback<Boolean>) stmt -> stmt.execute(String.format(CREATE_SCHEMA, schemaName))));
            logSchemaCreationResult(created, schemaName);
        } catch (Exception e) {
            String errorMessage = String.format("Failed to create schema %s", schemaName);
            log.error(errorMessage, e);
            throw new SchemaCreationException(errorMessage, e);
        }
    }

    private void logSchemaCreationResult(boolean created, String schemaName) {
        if (created) {
            log.info("Schema {} created successfully", schemaName);
        } else {
            log.debug("Schema {} already exists", schemaName);
        }
    }

}
