package com.siemens.spm.common.agency.supports.multiagencies;

import com.siemens.spm.common.agency.security.SecurityProperties;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Import;
import org.springframework.core.annotation.AliasFor;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import static com.siemens.spm.common.agency.utils.AgencyPersistenceConstants.AGENCY_ENTITY_MANAGER_FACTORY;
import static com.siemens.spm.common.agency.utils.AgencyPersistenceConstants.AGENCY_TRANSACTION_MANAGER;

@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
@EnableJpaRepositories
@EnableConfigurationProperties({SecurityProperties.class, JpaProperties.class})
@Import(AgencyPersistenceConfiguration.class)
public @interface EnableMultiAgenciesPersistence {
    @AliasFor(annotation = EnableJpaRepositories.class, attribute = "basePackages")
    String[] value() default {};

    @AliasFor(annotation = EnableJpaRepositories.class, attribute = "basePackages")
    String[] basePackages() default {};

    @AliasFor(annotation = EnableJpaRepositories.class, attribute = "basePackageClasses")
    Class<?>[] basePackageClasses() default {};

    String entityManagerFactoryRef() default AGENCY_ENTITY_MANAGER_FACTORY;

    String transactionManagerRef() default AGENCY_TRANSACTION_MANAGER;

    String[] entityPackages() default {};

}