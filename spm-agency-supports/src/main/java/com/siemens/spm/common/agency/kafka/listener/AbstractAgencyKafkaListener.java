package com.siemens.spm.common.agency.kafka.listener;

import com.siemens.spm.common.agency.kafka.boundary.AgencyKafkaFacade;
import com.siemens.spm.common.agency.kafka.dto.AgencySchemaPayloadDto;
import com.siemens.spm.common.kafka.common.ChangeTypeEnum;
import com.siemens.spm.common.kafka.common.KafkaEventListener;
import lombok.RequiredArgsConstructor;
import org.springframework.kafka.support.serializer.JsonDeserializer;

@RequiredArgsConstructor
public abstract class AbstractAgencyKafkaListener extends KafkaEventListener<String, String, AgencySchemaPayloadDto> {

    protected final AgencyKafkaFacade agencyKafkaFacade;

    protected final JsonDeserializer<AgencySchemaPayloadDto> deserializer;

    @Override
    protected void doBusinessLogic(AgencySchemaPayloadDto event) {
        String eventType = event.getEventType();
        ChangeTypeEnum changeTypeEnum = ChangeTypeEnum.fromIgnoreCaseString(eventType);
        agencyKafkaFacade.handleAgencyKafkaEvent(changeTypeEnum, event);
    }

}
