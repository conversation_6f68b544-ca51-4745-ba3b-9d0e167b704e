package com.siemens.spm.common.agency.utils;

import org.springframework.jdbc.core.JdbcTemplate;

public class JdbcTemplateUtils {

    public static void createSchema(JdbcTemplate jdbcTemplate, String schemaName) {
        String createSchemaStatement =
                "CREATE SCHEMA IF NOT EXISTS " + schemaName;

        //operation of master schema
        jdbcTemplate.execute(createSchemaStatement);
    }

    public static void deleteSchema(JdbcTemplate jdbcTemplate, String schemaName) {
        String deleteSchemaStatement = "DROP SCHEMA IF EXISTS " + schemaName + " CASCADE";
        jdbcTemplate.execute(deleteSchemaStatement);
    }
}
